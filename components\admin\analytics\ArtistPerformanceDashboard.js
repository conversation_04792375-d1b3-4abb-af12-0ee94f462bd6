import { useState, useEffect, useMemo } from 'react';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { authenticatedFetch } from '@/lib/auth-utils';
import { ArtistPerformanceAnalyzer } from '@/lib/analytics/performance-metrics';
import styles from '@/styles/admin/analytics/ArtistPerformanceDashboard.module.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler
);

/**
 * Artist Performance Dashboard Component
 * Provides comprehensive analytics for individual artist performance
 */
export default function ArtistPerformanceDashboard({ artistId, timeframe = 'monthly' }) {
  const [performanceData, setPerformanceData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [comparisonMode, setComparisonMode] = useState('none');
  const [forecastEnabled, setForecastEnabled] = useState(false);

  const analyzer = useMemo(() => new ArtistPerformanceAnalyzer(), []);

  useEffect(() => {
    loadArtistPerformanceData();
  }, [artistId, timeframe]);

  const loadArtistPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await authenticatedFetch(`/api/analytics/artist-performance?artistId=${artistId}&timeframe=${timeframe}`);
      
      if (response.success) {
        setPerformanceData(response.data);
      } else {
        throw new Error(response.error || 'Failed to load performance data');
      }
    } catch (err) {
      console.error('Error loading artist performance data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Artist Performance Metrics'
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time Period'
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Value'
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };

  const generateRevenueChartData = () => {
    if (!performanceData?.revenue?.monthly) return null;

    const months = Object.keys(performanceData.revenue.monthly).sort();
    const revenues = months.map(month => performanceData.revenue.monthly[month]);

    return {
      labels: months.map(month => {
        const [year, monthNum] = month.split('-');
        return new Date(year, monthNum - 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      }),
      datasets: [
        {
          label: 'Revenue',
          data: revenues,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    };
  };

  const generateBookingTrendData = () => {
    if (!performanceData?.bookings?.byMonth) return null;

    const months = Object.keys(performanceData.bookings.byMonth).sort();
    const bookings = months.map(month => performanceData.bookings.byMonth[month]);

    return {
      labels: months.map(month => {
        const [year, monthNum] = month.split('-');
        return new Date(year, monthNum - 1).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      }),
      datasets: [
        {
          label: 'Bookings',
          data: bookings,
          backgroundColor: 'rgba(16, 185, 129, 0.8)',
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1
        }
      ]
    };
  };

  const generateServiceDistributionData = () => {
    if (!performanceData?.revenue?.byService) return null;

    const services = Object.keys(performanceData.revenue.byService);
    const revenues = Object.values(performanceData.revenue.byService);

    return {
      labels: services,
      datasets: [
        {
          data: revenues,
          backgroundColor: [
            'rgba(239, 68, 68, 0.8)',
            'rgba(245, 158, 11, 0.8)',
            'rgba(16, 185, 129, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(139, 92, 246, 0.8)',
            'rgba(236, 72, 153, 0.8)'
          ],
          borderColor: [
            'rgb(239, 68, 68)',
            'rgb(245, 158, 11)',
            'rgb(16, 185, 129)',
            'rgb(59, 130, 246)',
            'rgb(139, 92, 246)',
            'rgb(236, 72, 153)'
          ],
          borderWidth: 2
        }
      ]
    };
  };

  const generatePerformanceRadarData = () => {
    if (!performanceData) return null;

    const metrics = {
      'Revenue': (performanceData.revenue?.total || 0) / 1000, // Normalize to thousands
      'Bookings': (performanceData.bookings?.total || 0) / 10, // Normalize
      'Quality': (performanceData.quality?.averageRating || 0) * 20, // Scale 0-5 to 0-100
      'Efficiency': performanceData.efficiency?.efficiencyScore || 0,
      'Growth': Math.max(0, Math.min(100, (performanceData.growth?.revenue?.rate || 0) + 50)), // Normalize growth rate
      'Retention': performanceData.performance?.repeatCustomerRate || 0
    };

    return {
      labels: Object.keys(metrics),
      datasets: [
        {
          label: 'Performance Metrics',
          data: Object.values(metrics),
          backgroundColor: 'rgba(59, 130, 246, 0.2)',
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 2,
          pointBackgroundColor: 'rgb(59, 130, 246)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgb(59, 130, 246)'
        }
      ]
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p>Loading artist performance data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h3>Error Loading Performance Data</h3>
        <p>{error}</p>
        <button onClick={loadArtistPerformanceData} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (!performanceData) {
    return (
      <div className={styles.noDataContainer}>
        <h3>No Performance Data Available</h3>
        <p>No data found for the selected artist and time period.</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h2>Artist Performance Dashboard</h2>
        <div className={styles.controls}>
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value)}
            className={styles.timeframeSelect}
          >
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="yearly">Yearly</option>
          </select>
          
          <button
            onClick={() => setForecastEnabled(!forecastEnabled)}
            className={`${styles.forecastButton} ${forecastEnabled ? styles.active : ''}`}
          >
            {forecastEnabled ? 'Hide Forecast' : 'Show Forecast'}
          </button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className={styles.kpiGrid}>
        <div className={styles.kpiCard}>
          <h3>Total Revenue</h3>
          <div className={styles.kpiValue}>
            {formatCurrency(performanceData.revenue?.total || 0)}
          </div>
          <div className={`${styles.kpiChange} ${(performanceData.revenue?.growthRate || 0) >= 0 ? styles.positive : styles.negative}`}>
            {formatPercentage(performanceData.revenue?.growthRate || 0)} vs last period
          </div>
        </div>

        <div className={styles.kpiCard}>
          <h3>Total Bookings</h3>
          <div className={styles.kpiValue}>
            {performanceData.bookings?.total || 0}
          </div>
          <div className={`${styles.kpiChange} ${(performanceData.bookings?.bookingTrend || 0) >= 0 ? styles.positive : styles.negative}`}>
            {formatPercentage(performanceData.bookings?.bookingTrend || 0)} vs last period
          </div>
        </div>

        <div className={styles.kpiCard}>
          <h3>Average Rating</h3>
          <div className={styles.kpiValue}>
            {(performanceData.quality?.averageRating || 0).toFixed(1)} ⭐
          </div>
          <div className={styles.kpiSubtext}>
            {formatPercentage(performanceData.quality?.satisfactionRate || 0)} satisfaction
          </div>
        </div>

        <div className={styles.kpiCard}>
          <h3>Completion Rate</h3>
          <div className={styles.kpiValue}>
            {formatPercentage(performanceData.bookings?.completionRate || 0)}
          </div>
          <div className={styles.kpiSubtext}>
            {performanceData.bookings?.cancelled || 0} cancelled
          </div>
        </div>

        <div className={styles.kpiCard}>
          <h3>Revenue per Booking</h3>
          <div className={styles.kpiValue}>
            {formatCurrency(performanceData.performance?.revenuePerBooking || 0)}
          </div>
          <div className={styles.kpiSubtext}>
            {formatPercentage(performanceData.performance?.utilizationRate || 0)} utilization
          </div>
        </div>

        <div className={styles.kpiCard}>
          <h3>Repeat Customers</h3>
          <div className={styles.kpiValue}>
            {formatPercentage(performanceData.performance?.repeatCustomerRate || 0)}
          </div>
          <div className={styles.kpiSubtext}>
            {performanceData.customers?.total || 0} total customers
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className={styles.chartsGrid}>
        {/* Revenue Trend Chart */}
        <div className={styles.chartCard}>
          <h3>Revenue Trend</h3>
          <div className={styles.chartContainer}>
            {generateRevenueChartData() && (
              <Line data={generateRevenueChartData()} options={chartOptions} />
            )}
          </div>
        </div>

        {/* Booking Trend Chart */}
        <div className={styles.chartCard}>
          <h3>Booking Trend</h3>
          <div className={styles.chartContainer}>
            {generateBookingTrendData() && (
              <Bar data={generateBookingTrendData()} options={chartOptions} />
            )}
          </div>
        </div>

        {/* Service Distribution */}
        <div className={styles.chartCard}>
          <h3>Revenue by Service</h3>
          <div className={styles.chartContainer}>
            {generateServiceDistributionData() && (
              <Doughnut 
                data={generateServiceDistributionData()} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            )}
          </div>
        </div>

        {/* Performance Radar */}
        <div className={styles.chartCard}>
          <h3>Performance Overview</h3>
          <div className={styles.chartContainer}>
            {generatePerformanceRadarData() && (
              <Radar 
                data={generatePerformanceRadarData()} 
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 100
                    }
                  }
                }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className={styles.insightsSection}>
        <h3>Performance Insights</h3>
        <div className={styles.insightsGrid}>
          <div className={styles.insightCard}>
            <h4>Peak Performance</h4>
            <p>
              Best month: {performanceData.revenue?.highestMonth?.key} 
              ({formatCurrency(performanceData.revenue?.highestMonth?.value || 0)})
            </p>
            <p>
              Peak hours: {performanceData.bookings?.peakHours?.map(h => `${h.hour}:00`).join(', ')}
            </p>
          </div>

          <div className={styles.insightCard}>
            <h4>Growth Trend</h4>
            <p>
              Revenue growth: {formatPercentage(performanceData.growth?.revenue?.rate || 0)}
            </p>
            <p>
              Booking growth: {formatPercentage(performanceData.growth?.bookings?.rate || 0)}
            </p>
            <p>
              Trend: {performanceData.growth?.trend || 'stable'}
            </p>
          </div>

          <div className={styles.insightCard}>
            <h4>Customer Metrics</h4>
            <p>
              Average lifetime value: {formatCurrency(performanceData.customers?.averageLifetimeValue || 0)}
            </p>
            <p>
              New customers this month: {performanceData.customers?.newCustomersThisMonth || 0}
            </p>
            <p>
              30-day retention: {formatPercentage(performanceData.customers?.retention?.thirtyDay || 0)}
            </p>
          </div>

          <div className={styles.insightCard}>
            <h4>Efficiency Metrics</h4>
            <p>
              Revenue per hour: {formatCurrency(performanceData.efficiency?.revenuePerHour || 0)}
            </p>
            <p>
              Bookings per day: {(performanceData.efficiency?.bookingsPerDay || 0).toFixed(1)}
            </p>
            <p>
              Efficiency score: {(performanceData.efficiency?.efficiencyScore || 0).toFixed(0)}/100
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
