# Authentication Session Fixes - Testing Guide

## Quick Testing Steps

### 1. Start the Development Server
```bash
npm run dev
```

### 2. Test Authentication Flow

#### Step 1: Login to Admin
1. Navigate to: `http://localhost:3000/admin/login`
2. Login with admin credentials
3. **Expected**: Successful login, redirect to `/admin`

#### Step 2: Navigate to Bookings
1. Click on "Bookings" in the sidebar OR navigate to: `http://localhost:3000/admin/bookings`
2. **Expected**: Page loads without authentication errors
3. **Check**: No "authentication session expired" messages
4. **Check**: BookingCalendar component loads successfully

#### Step 3: Verify Error Monitoring
1. Open browser Developer Tools (F12) → Console
2. **Expected**: No authentication-related errors
3. **Expected**: AuthErrorMonitor component visible in top-right corner (development mode)
4. **Expected**: Monitor shows "✅ No authentication errors detected"

#### Step 4: Test Session Persistence
1. Refresh the page (F5)
2. **Expected**: No re-authentication required
3. **Expected**: Page loads immediately without login redirect

#### Step 5: Test Cross-Page Navigation
1. Navigate between different admin pages:
   - `/admin` (Dashboard)
   - `/admin/bookings` (Bookings)
   - `/admin/customers` (Customers)
   - `/admin/inventory` (Inventory)
2. **Expected**: No authentication errors on any page
3. **Expected**: Smooth navigation without session expiration

### 3. Monitor Console Logs

#### Expected Log Patterns (Success)
```
[Supabase] Creating singleton client instance
[AuthTokenManager] Using cached token
[Auth abc123] User already authenticated, skipping re-initialization
[BookingCalendar] Auth token obtained successfully
```

#### Error Patterns to Watch For (Should NOT appear)
```
❌ 401 Unauthorized
❌ Invalid token: invalid JWT
❌ Multiple GoTrueClient instances detected
❌ Authentication session expired
❌ Error: Abort fetching component for route
```

### 4. Test Token Refresh

#### Manual Token Refresh Test
1. Open browser console
2. Run: `localStorage.clear(); sessionStorage.clear();`
3. Wait 30 seconds
4. Navigate to a new admin page
5. **Expected**: Automatic token refresh, no authentication errors

#### Proactive Refresh Test
1. Stay on an admin page for 10+ minutes
2. **Expected**: Automatic token refresh every 10 minutes
3. **Expected**: Console logs showing successful token refresh

### 5. Test Error Monitoring

#### Trigger Error Monitoring
1. Open browser console
2. Run: `console.error('Test authentication error: 401 Unauthorized')`
3. **Expected**: Error appears in AuthErrorMonitor overlay
4. **Expected**: Error is sent to server (check server logs)

#### Check Server Error Logging
1. Look for server console output:
```
🚨 CLIENT-SIDE ERROR DETECTED
================================================================================
Request ID: abc123
Type: auth_error
Message: Test authentication error: 401 Unauthorized
```

### 6. Verify API Calls

#### Check Network Tab
1. Open Developer Tools → Network tab
2. Navigate to `/admin/bookings`
3. Look for API calls to:
   - `/api/admin/bookings`
   - `/api/admin/services`
4. **Expected**: All API calls return 200 status
5. **Expected**: All requests include proper Authorization headers

#### Check Request Headers
```
Authorization: Bearer eyJ...
X-Auth-Token: eyJ...
Content-Type: application/json
```

## Troubleshooting

### If Authentication Errors Still Occur

#### 1. Check Environment Variables
```bash
# Verify these are set in .env.local
NEXT_PUBLIC_SUPABASE_URL=your_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

#### 2. Clear All Storage
```javascript
// Run in browser console
localStorage.clear();
sessionStorage.clear();
document.cookie.split(";").forEach(function(c) { 
  document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
});
```

#### 3. Check Token Storage
```javascript
// Run in browser console to check token storage
console.log('SessionStorage:', sessionStorage.getItem('oss_auth_token_cache'));
console.log('Cookies:', document.cookie);
console.log('Supabase session:', await supabase.auth.getSession());
```

#### 4. Enable Debug Mode
```bash
# Add to .env.local
NEXT_PUBLIC_DEBUG_AUTH=true
```

### Common Issues and Solutions

#### Issue: "Multiple GoTrueClient instances"
**Solution**: Restart development server, clear browser cache

#### Issue: 401 Unauthorized on API calls
**Solution**: Check if tokens are being stored in cookies properly

#### Issue: Session expires immediately
**Solution**: Verify token refresh interval is working

#### Issue: AuthErrorMonitor not showing
**Solution**: Ensure you're in development mode or have `NEXT_PUBLIC_DEBUG_AUTH=true`

## Success Criteria

✅ **Login works without errors**
✅ **Bookings page loads without authentication issues**
✅ **No "session expired" messages**
✅ **Cross-page navigation works smoothly**
✅ **Token refresh happens automatically**
✅ **Error monitoring captures and displays issues**
✅ **API calls include proper authentication headers**
✅ **No console errors related to authentication**

## Performance Verification

### Expected Improvements
- **Reduced Login Frequency**: Users should not need to re-authenticate frequently
- **Faster Page Loads**: No authentication delays when navigating
- **Better Error Visibility**: Real-time error monitoring and reporting
- **Consistent Session Management**: Reliable authentication state across the application

### Metrics to Monitor
- **Authentication Error Rate**: Should be near 0%
- **Session Duration**: Should persist for intended duration
- **Page Load Time**: Should not be delayed by authentication issues
- **User Experience**: Smooth navigation without interruptions

---

**Note**: This testing guide should be run after implementing all the authentication fixes outlined in `AUTHENTICATION_SESSION_FIXES_IMPLEMENTATION.md`.
