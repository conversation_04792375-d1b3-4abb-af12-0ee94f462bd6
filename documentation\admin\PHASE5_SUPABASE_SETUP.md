# Phase 5 PWA - Supabase Database Setup Guide

## Overview

Phase 5 PWA implementation requires additional Supabase database tables, storage buckets, and policies to support photo management, offline synchronization, and enhanced PWA functionality.

## 🗄️ Required Database Tables

### 1. Photos Table

The photos table stores metadata for all captured photos (before/after, portfolio, receipts).

```sql
-- Create photos table
CREATE TABLE IF NOT EXISTS photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  original_filename TEXT,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  type TEXT NOT NULL CHECK (type IN ('before', 'after', 'portfolio', 'receipt')),
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  public_url TEXT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  
  -- Indexes for performance
  CONSTRAINT photos_type_check CHECK (type IN ('before', 'after', 'portfolio', 'receipt'))
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_photos_type ON photos(type);
CREATE INDEX IF NOT EXISTS idx_photos_booking_id ON photos(booking_id);
CREATE INDEX IF NOT EXISTS idx_photos_uploaded_at ON photos(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_photos_created_at ON photos(created_at);
```

### 2. PWA Sync Queue Table (Optional)

For advanced offline sync tracking (optional - IndexedDB handles most of this):

```sql
-- Create PWA sync queue table (optional)
CREATE TABLE IF NOT EXISTS pwa_sync_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  item_type TEXT NOT NULL,
  item_id TEXT NOT NULL,
  data JSONB NOT NULL,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  retry_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Indexes
  UNIQUE(item_type, item_id)
);

CREATE INDEX IF NOT EXISTS idx_pwa_sync_status ON pwa_sync_queue(status);
CREATE INDEX IF NOT EXISTS idx_pwa_sync_priority ON pwa_sync_queue(priority);
CREATE INDEX IF NOT EXISTS idx_pwa_sync_created_at ON pwa_sync_queue(created_at);
```

## 🔐 Row Level Security (RLS) Policies

### Photos Table Policies

```sql
-- Enable RLS on photos table
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;

-- Policy: Photos are viewable by authenticated users
CREATE POLICY "Photos are viewable by authenticated users" ON photos
  FOR SELECT USING (auth.role() = 'authenticated');

-- Policy: Photos can be inserted by authenticated users
CREATE POLICY "Photos can be inserted by authenticated users" ON photos
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Photos can be updated by authenticated users
CREATE POLICY "Photos can be updated by authenticated users" ON photos
  FOR UPDATE USING (auth.role() = 'authenticated');

-- Policy: Photos can be deleted by authenticated users
CREATE POLICY "Photos can be deleted by authenticated users" ON photos
  FOR DELETE USING (auth.role() = 'authenticated');

-- Optional: More restrictive policy based on user roles
-- CREATE POLICY "Photos management by admin/dev roles" ON photos
--   FOR ALL USING (
--     auth.jwt() ->> 'role' IN ('admin', 'dev') OR
--     auth.jwt() ->> 'email' = '<EMAIL>'
--   );
```

### PWA Sync Queue Policies (if using)

```sql
-- Enable RLS on pwa_sync_queue table
ALTER TABLE pwa_sync_queue ENABLE ROW LEVEL SECURITY;

-- Policy: Sync queue accessible by authenticated users
CREATE POLICY "Sync queue accessible by authenticated users" ON pwa_sync_queue
  FOR ALL USING (auth.role() = 'authenticated');
```

## 📁 Storage Bucket Setup

### Create Storage Bucket for Photos

```sql
-- Create storage bucket for photos
INSERT INTO storage.buckets (id, name, public)
VALUES ('ocean-soul-sparkles', 'ocean-soul-sparkles', true)
ON CONFLICT (id) DO NOTHING;
```

### Storage Policies

```sql
-- Policy: Allow authenticated users to upload photos
CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );

-- Policy: Allow public access to photos
CREATE POLICY "Allow public access" ON storage.objects
  FOR SELECT USING (bucket_id = 'ocean-soul-sparkles');

-- Policy: Allow authenticated users to update their uploads
CREATE POLICY "Allow authenticated updates" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );

-- Policy: Allow authenticated users to delete photos
CREATE POLICY "Allow authenticated deletes" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );
```

## 🔧 Database Functions

### Helper Function for Table Creation

```sql
-- Function to create photos table if it doesn't exist
CREATE OR REPLACE FUNCTION create_photos_table_if_not_exists()
RETURNS void AS $$
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'photos') THEN
    -- Create the table (use the CREATE TABLE statement from above)
    RAISE NOTICE 'Photos table created successfully';
  ELSE
    RAISE NOTICE 'Photos table already exists';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Execute the function
SELECT create_photos_table_if_not_exists();
```

### Photo Cleanup Function

```sql
-- Function to clean up old photos
CREATE OR REPLACE FUNCTION cleanup_old_photos(days_old INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete photos older than specified days that are not linked to bookings
  DELETE FROM photos 
  WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND booking_id IS NULL
    AND type IN ('portfolio', 'receipt');
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (optional - can be called via cron or API)
-- SELECT cleanup_old_photos(90); -- Clean photos older than 90 days
```

## 📊 Views for Analytics

### Photo Analytics View

```sql
-- Create view for photo analytics
CREATE OR REPLACE VIEW photo_analytics AS
SELECT 
  type,
  COUNT(*) as total_photos,
  SUM(file_size) as total_size_bytes,
  AVG(file_size) as avg_size_bytes,
  MIN(created_at) as first_photo,
  MAX(created_at) as latest_photo,
  COUNT(DISTINCT booking_id) as unique_bookings
FROM photos
GROUP BY type;

-- Grant access to the view
GRANT SELECT ON photo_analytics TO authenticated;
```

## 🔄 Database Triggers

### Update Booking Photos Trigger

```sql
-- Function to update booking photo URLs
CREATE OR REPLACE FUNCTION update_booking_photo_urls()
RETURNS TRIGGER AS $$
BEGIN
  -- Update booking with photo URL when before/after photos are added
  IF NEW.type = 'before' AND NEW.booking_id IS NOT NULL THEN
    UPDATE bookings 
    SET before_photo_url = NEW.public_url 
    WHERE id = NEW.booking_id;
  ELSIF NEW.type = 'after' AND NEW.booking_id IS NOT NULL THEN
    UPDATE bookings 
    SET after_photo_url = NEW.public_url 
    WHERE id = NEW.booking_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_booking_photos_trigger
  AFTER INSERT ON photos
  FOR EACH ROW
  EXECUTE FUNCTION update_booking_photo_urls();
```

## 🚀 Setup Instructions

### 1. Run in Supabase SQL Editor

Copy and paste the following SQL commands in your Supabase SQL Editor:

```sql
-- Step 1: Create photos table
CREATE TABLE IF NOT EXISTS photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  original_filename TEXT,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  type TEXT NOT NULL CHECK (type IN ('before', 'after', 'portfolio', 'receipt')),
  booking_id UUID REFERENCES bookings(id) ON DELETE SET NULL,
  public_url TEXT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);

-- Step 2: Create indexes
CREATE INDEX IF NOT EXISTS idx_photos_type ON photos(type);
CREATE INDEX IF NOT EXISTS idx_photos_booking_id ON photos(booking_id);
CREATE INDEX IF NOT EXISTS idx_photos_uploaded_at ON photos(uploaded_at);

-- Step 3: Enable RLS
ALTER TABLE photos ENABLE ROW LEVEL SECURITY;

-- Step 4: Create policies
CREATE POLICY "Photos are viewable by authenticated users" ON photos
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Photos can be inserted by authenticated users" ON photos
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Photos can be updated by authenticated users" ON photos
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Photos can be deleted by authenticated users" ON photos
  FOR DELETE USING (auth.role() = 'authenticated');
```

### 2. Create Storage Bucket

In Supabase Dashboard:
1. Go to **Storage** section
2. Click **Create Bucket**
3. Name: `ocean-soul-sparkles`
4. Set as **Public bucket**
5. Click **Create bucket**

### 3. Configure Storage Policies

In the Storage section, go to **Policies** and add the storage policies listed above.

### 4. Update Bookings Table (if needed)

If your bookings table doesn't have photo URL columns:

```sql
-- Add photo URL columns to bookings table
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS before_photo_url TEXT,
ADD COLUMN IF NOT EXISTS after_photo_url TEXT;
```

## 🧪 Testing the Setup

### Test Photo Upload

```javascript
// Test photo upload in browser console
const testPhotoUpload = async () => {
  const formData = new FormData()
  formData.append('photo', file) // your file object
  formData.append('type', 'portfolio')
  formData.append('timestamp', new Date().toISOString())
  
  const response = await fetch('/api/admin/photos/upload', {
    method: 'POST',
    body: formData
  })
  
  console.log(await response.json())
}
```

### Verify Database Setup

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name IN ('photos', 'pwa_sync_queue');

-- Check photos table structure
\d photos;

-- Check storage bucket
SELECT * FROM storage.buckets WHERE name = 'ocean-soul-sparkles';

-- Test photo insertion
INSERT INTO photos (filename, file_path, public_url, type) 
VALUES ('test.jpg', 'photos/test/test.jpg', 'https://example.com/test.jpg', 'portfolio');
```

## 🔒 Security Considerations

1. **File Size Limits**: The upload API limits files to 10MB
2. **File Type Validation**: Only JPEG, PNG, and WebP files allowed
3. **Authentication Required**: All operations require authenticated users
4. **RLS Enabled**: Row Level Security protects data access
5. **Storage Policies**: Proper bucket policies for secure access

## 📝 Environment Variables

Ensure these are set in your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

---

**Your Supabase database is now ready for Phase 5 PWA functionality!** 🚀

The setup provides secure photo storage, offline sync capabilities, and proper data management for the PWA features.
