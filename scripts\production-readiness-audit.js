#!/usr/bin/env node

/**
 * Ocean Soul Sparkles - Production Readiness Audit Script
 * 
 * Comprehensive audit script that checks:
 * 1. Build status and component errors
 * 2. Database security (RLS, SECURITY DEFINER views)
 * 3. Test files and debugging code
 * 4. Environment configuration
 * 5. Artist/Braider system functionality
 * 6. Cross-platform integration
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { execSync } from 'child_process'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '..')

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

let supabase = null
if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey)
}

/**
 * Audit Results Storage
 */
const auditResults = {
  build: { status: 'unknown', issues: [] },
  security: { status: 'unknown', issues: [] },
  testFiles: { status: 'unknown', issues: [] },
  environment: { status: 'unknown', issues: [] },
  artistSystem: { status: 'unknown', issues: [] },
  overall: { status: 'unknown', readyForProduction: false }
}

/**
 * Check build status
 */
async function checkBuildStatus() {
  console.log('\n🔨 CHECKING BUILD STATUS...')
  
  try {
    // Check if node_modules exists
    if (!fs.existsSync(path.join(projectRoot, 'node_modules'))) {
      auditResults.build.issues.push('node_modules directory missing - run npm install')
      auditResults.build.status = 'failed'
      return
    }
    
    // Try to run build
    console.log('   Running production build test...')
    execSync('npm run build', { 
      cwd: projectRoot, 
      stdio: 'pipe',
      timeout: 300000 // 5 minutes
    })
    
    auditResults.build.status = 'passed'
    console.log('   ✅ Production build successful')
    
  } catch (error) {
    auditResults.build.status = 'failed'
    auditResults.build.issues.push(`Build failed: ${error.message}`)
    console.log('   ❌ Production build failed')
  }
}

/**
 * Check for test files and debugging code
 */
async function checkTestFiles() {
  console.log('\n🧪 CHECKING FOR TEST FILES AND DEBUG CODE...')
  
  const testPatterns = [
    'pages/debug/**/*',
    'pages/admin/test-*.js',
    'debug-*.js',
    '**/*test*.js',
    '**/*debug*.js'
  ]
  
  const foundTestFiles = []
  
  // Check for test/debug files
  const checkDirectory = (dir, relativePath = '') => {
    if (!fs.existsSync(dir)) return
    
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const relativeItemPath = path.join(relativePath, item)
      
      if (fs.statSync(fullPath).isDirectory()) {
        if (item === 'debug' && relativePath.includes('pages')) {
          foundTestFiles.push(relativeItemPath)
        } else if (!item.startsWith('.') && !item.includes('node_modules')) {
          checkDirectory(fullPath, relativeItemPath)
        }
      } else if (item.includes('test') || item.includes('debug')) {
        foundTestFiles.push(relativeItemPath)
      }
    }
  }
  
  checkDirectory(path.join(projectRoot, 'pages'), 'pages')
  checkDirectory(projectRoot, '')
  
  if (foundTestFiles.length > 0) {
    auditResults.testFiles.status = 'failed'
    auditResults.testFiles.issues = foundTestFiles
    console.log(`   ❌ Found ${foundTestFiles.length} test/debug files:`)
    foundTestFiles.forEach(file => console.log(`      - ${file}`))
  } else {
    auditResults.testFiles.status = 'passed'
    console.log('   ✅ No test/debug files found')
  }
}

/**
 * Check environment configuration
 */
async function checkEnvironment() {
  console.log('\n🌍 CHECKING ENVIRONMENT CONFIGURATION...')
  
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  const securityEnvVars = {
    'NEXT_PUBLIC_DEV_MODE': 'false',
    'NEXT_PUBLIC_DEBUG_AUTH': 'false',
    'ENABLE_AUTH_BYPASS': 'false'
  }
  
  // Check required variables
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      auditResults.environment.issues.push(`Missing required environment variable: ${envVar}`)
    }
  }
  
  // Check security variables
  for (const [envVar, expectedValue] of Object.entries(securityEnvVars)) {
    if (process.env[envVar] !== expectedValue) {
      auditResults.environment.issues.push(`Security risk: ${envVar} should be "${expectedValue}", got "${process.env[envVar]}"`)
    }
  }
  
  if (auditResults.environment.issues.length === 0) {
    auditResults.environment.status = 'passed'
    console.log('   ✅ Environment configuration secure')
  } else {
    auditResults.environment.status = 'failed'
    console.log(`   ❌ Found ${auditResults.environment.issues.length} environment issues`)
  }
}

/**
 * Check database security
 */
async function checkDatabaseSecurity() {
  console.log('\n🔒 CHECKING DATABASE SECURITY...')
  
  if (!supabase) {
    auditResults.security.status = 'failed'
    auditResults.security.issues.push('Cannot connect to Supabase - missing credentials')
    console.log('   ❌ Cannot connect to Supabase')
    return
  }
  
  try {
    // Check for SECURITY DEFINER views
    const { data: securityDefinerViews } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT viewname 
        FROM pg_views 
        WHERE schemaname = 'public' 
        AND definition LIKE '%SECURITY DEFINER%'
      `
    })
    
    if (securityDefinerViews && securityDefinerViews.length > 0) {
      auditResults.security.issues.push(`Found ${securityDefinerViews.length} SECURITY DEFINER views`)
    }
    
    // Check for tables without RLS
    const { data: tablesWithoutRLS } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT IN (
          SELECT tablename 
          FROM pg_tables t
          JOIN pg_class c ON c.relname = t.tablename
          WHERE t.schemaname = 'public' AND c.relrowsecurity = true
        )
      `
    })
    
    if (tablesWithoutRLS && tablesWithoutRLS.length > 0) {
      auditResults.security.issues.push(`Found ${tablesWithoutRLS.length} tables without RLS`)
    }
    
    // Check for anonymous access to sensitive views
    const { data: anonAccess } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT table_name 
        FROM information_schema.role_table_grants 
        WHERE grantee = 'anon' 
        AND privilege_type = 'SELECT'
        AND table_schema = 'public'
        AND table_name LIKE '%analytics%' OR table_name LIKE '%statistics%'
      `
    })
    
    if (anonAccess && anonAccess.length > 0) {
      auditResults.security.issues.push(`Found ${anonAccess.length} sensitive views with anonymous access`)
    }
    
    if (auditResults.security.issues.length === 0) {
      auditResults.security.status = 'passed'
      console.log('   ✅ Database security configuration secure')
    } else {
      auditResults.security.status = 'failed'
      console.log(`   ❌ Found ${auditResults.security.issues.length} security issues`)
    }
    
  } catch (error) {
    auditResults.security.status = 'failed'
    auditResults.security.issues.push(`Database security check failed: ${error.message}`)
    console.log('   ❌ Database security check failed')
  }
}

/**
 * Check artist/braider system
 */
async function checkArtistSystem() {
  console.log('\n👨‍🎨 CHECKING ARTIST/BRAIDER SYSTEM...')
  
  if (!supabase) {
    auditResults.artistSystem.status = 'failed'
    auditResults.artistSystem.issues.push('Cannot verify artist system - missing Supabase connection')
    return
  }
  
  try {
    // Check if artist tables exist
    const { data: artistTables } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('artist_profiles', 'artist_availability_schedule', 'artist_service_specializations')
      `
    })
    
    if (!artistTables || artistTables.length < 3) {
      auditResults.artistSystem.issues.push('Missing artist system tables')
    }
    
    // Check if user_roles table exists and has proper roles
    const { data: userRoles } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT DISTINCT role 
        FROM user_roles 
        WHERE role IN ('dev', 'admin', 'artist', 'braider', 'user')
      `
    })
    
    if (!userRoles || userRoles.length < 5) {
      auditResults.artistSystem.issues.push('5-tier role system not properly configured')
    }
    
    if (auditResults.artistSystem.issues.length === 0) {
      auditResults.artistSystem.status = 'passed'
      console.log('   ✅ Artist/Braider system properly configured')
    } else {
      auditResults.artistSystem.status = 'failed'
      console.log(`   ❌ Found ${auditResults.artistSystem.issues.length} artist system issues`)
    }
    
  } catch (error) {
    auditResults.artistSystem.status = 'failed'
    auditResults.artistSystem.issues.push(`Artist system check failed: ${error.message}`)
    console.log('   ❌ Artist system check failed')
  }
}

/**
 * Generate final report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80))
  console.log('🚨 OCEAN SOUL SPARKLES PRODUCTION READINESS AUDIT REPORT')
  console.log('='.repeat(80))
  
  // Determine overall status
  const allPassed = Object.values(auditResults)
    .filter(result => result.status !== undefined)
    .every(result => result.status === 'passed')
  
  auditResults.overall.readyForProduction = allPassed
  auditResults.overall.status = allPassed ? 'READY' : 'NOT READY'
  
  console.log(`\n🎯 OVERALL STATUS: ${auditResults.overall.status}`)
  
  // Build Status
  console.log(`\n🔨 BUILD STATUS: ${auditResults.build.status.toUpperCase()}`)
  if (auditResults.build.issues.length > 0) {
    auditResults.build.issues.forEach(issue => console.log(`   ❌ ${issue}`))
  }
  
  // Security Status
  console.log(`\n🔒 SECURITY STATUS: ${auditResults.security.status.toUpperCase()}`)
  if (auditResults.security.issues.length > 0) {
    auditResults.security.issues.forEach(issue => console.log(`   ❌ ${issue}`))
  }
  
  // Test Files Status
  console.log(`\n🧪 TEST FILES STATUS: ${auditResults.testFiles.status.toUpperCase()}`)
  if (auditResults.testFiles.issues.length > 0) {
    auditResults.testFiles.issues.forEach(issue => console.log(`   ❌ ${issue}`))
  }
  
  // Environment Status
  console.log(`\n🌍 ENVIRONMENT STATUS: ${auditResults.environment.status.toUpperCase()}`)
  if (auditResults.environment.issues.length > 0) {
    auditResults.environment.issues.forEach(issue => console.log(`   ❌ ${issue}`))
  }
  
  // Artist System Status
  console.log(`\n👨‍🎨 ARTIST SYSTEM STATUS: ${auditResults.artistSystem.status.toUpperCase()}`)
  if (auditResults.artistSystem.issues.length > 0) {
    auditResults.artistSystem.issues.forEach(issue => console.log(`   ❌ ${issue}`))
  }
  
  // Recommendations
  console.log('\n📋 RECOMMENDATIONS:')
  if (auditResults.overall.readyForProduction) {
    console.log('   ✅ System is ready for production deployment!')
  } else {
    console.log('   ❌ Fix the issues above before production deployment')
    
    if (auditResults.security.status === 'failed') {
      console.log('   🔧 Run: node scripts/apply-security-fixes.js')
    }
    
    if (auditResults.testFiles.status === 'failed') {
      console.log('   🧹 Remove test/debug files manually')
    }
  }
  
  console.log('='.repeat(80))
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 STARTING PRODUCTION READINESS AUDIT...')
  
  await checkBuildStatus()
  await checkTestFiles()
  await checkEnvironment()
  await checkDatabaseSecurity()
  await checkArtistSystem()
  
  generateReport()
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { main as runProductionAudit }
