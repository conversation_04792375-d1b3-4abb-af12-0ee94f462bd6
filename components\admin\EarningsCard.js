import { useState } from 'react'
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css'

export default function EarningsCard({ stats, recentPayments }) {
  const [timeframe, setTimeframe] = useState('week')

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount || 0)
  }

  const getEarningsForTimeframe = () => {
    switch (timeframe) {
      case 'week':
        return stats?.thisWeekEarnings || 0
      case 'month':
        return stats?.thisMonthEarnings || 0
      case 'total':
        return stats?.totalEarnings || 0
      default:
        return 0
    }
  }

  return (
    <div className={styles.card}>
      <div className={styles.cardHeader}>
        <h3>Earnings</h3>
        <select 
          value={timeframe} 
          onChange={(e) => setTimeframe(e.target.value)}
          className={styles.timeframeSelect}
        >
          <option value="week">This Week</option>
          <option value="month">This Month</option>
          <option value="total">All Time</option>
        </select>
      </div>

      <div className={styles.earningsOverview}>
        <div className={styles.mainEarning}>
          <span className={styles.earningAmount}>
            {formatCurrency(getEarningsForTimeframe())}
          </span>
          <span className={styles.earningLabel}>
            {timeframe === 'week' ? 'This Week' : 
             timeframe === 'month' ? 'This Month' : 'Total Earned'}
          </span>
        </div>

        <div className={styles.earningStats}>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats?.totalBookings || 0}</span>
            <span className={styles.statLabel}>Total Bookings</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{stats?.completedServices || 0}</span>
            <span className={styles.statLabel}>Completed</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>
              {stats?.averageRating ? stats.averageRating.toFixed(1) : '0.0'}
            </span>
            <span className={styles.statLabel}>Avg Rating</span>
          </div>
        </div>
      </div>

      {recentPayments && recentPayments.length > 0 && (
        <div className={styles.recentPayments}>
          <h4>Recent Payments</h4>
          <div className={styles.paymentsList}>
            {recentPayments.slice(0, 3).map((payment, index) => (
              <div key={index} className={styles.paymentItem}>
                <div className={styles.paymentInfo}>
                  <span className={styles.paymentService}>{payment.service_name}</span>
                  <span className={styles.paymentDate}>
                    {new Date(payment.created_at).toLocaleDateString()}
                  </span>
                </div>
                <span className={styles.paymentAmount}>
                  {formatCurrency(payment.amount)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
