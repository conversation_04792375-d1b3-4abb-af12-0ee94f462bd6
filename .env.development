xextension-error-suppression.js:235  Export fetch error: ReferenceError: getAuthToken is not defined
    at handleExport (index.js:287:23)
    at onClick (index.js:834:38)
    at HTMLUnknownElement.callCallback (react-dom.development.js:4164:1)
    at Object.invokeGuardedCallbackDev (react-dom.development.js:4213:1)
    at invokeGuardedCallback (react-dom.development.js:4277:1)
    at invokeGuardedCallbackAndCatchFirstError (react-dom.development.js:4291:1)
    at executeDispatch (react-dom.development.js:9041:1)
    at processDispatchQueueItemsInOrder (react-dom.development.js:9073:1)
    at processDispatchQueue (react-dom.development.js:9086:1)
    at dispatchEventsForPlugins (react-dom.development.js:9097:1)
    at eval (react-dom.development.js:9288:1)
    at batchedUpdates$1 (react-dom.development.js:26179:1)
    at batchedUpdates (react-dom.development.js:3991:1)
    at dispatchEventForPluginEventSystem (react-dom.development.js:9287:1)
    at dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay (react-dom.development.js:6465:1)
    at dispatchEvent (react-dom.development.js:6457:1)
    at dispatchDiscreteEvent (react-dom.development.js:6430:1)
(anonymous) @ extension-error-suppression.js:235
console.error @ AuthErrorMonitor.js:24
console.error @ react-hydration-helpers.js:93
console.error @ AuthErrorMonitor.js:24
console.error @ AuthErrorMonitor.js:24
console.error @ AuthErrorMonitor.js:24
console.error @ index.js:56
handleExport @ index.js:371
onClick @ index.js:834
callCallback @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
eval @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
extension-error-suppression.js:235  Export error: ReferenceError: getAuthToken is not defined
    at handleExport (index.js:287:23)
    at onClick (index.js:834:38)
    at HTMLUnknownElement.callCallback (react-dom.development.js:4164:1)
    at Object.invokeGuardedCallbackDev (react-dom.development.js:4213:1)
    at invokeGuardedCallback (react-dom.development.js:4277:1)
    at invokeGuardedCallbackAndCatchFirstError (react-dom.development.js:4291:1)
    at executeDispatch (react-dom.development.js:9041:1)
    at processDispatchQueueItemsInOrder (react-dom.development.js:9073:1)
    at processDispatchQueue (react-dom.development.js:9086:1)
    at dispatchEventsForPlugins (react-dom.development.js:9097:1)
    at eval (react-dom.development.js:9288:1)
    at batchedUpdates$1 (react-dom.development.js:26179:1)
    at batchedUpdates (react-dom.development.js:3991:1)
    at dispatchEventForPluginEventSystem (react-dom.development.js:9287:1)
    at dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay (react-dom.development.js:6465:1)
    at dispatchEvent (react-dom.development.js:6457:1)
    at dispatchDiscreteEvent (react-dom.development.js:6430:1)
(anonymous) @ extension-error-suppression.js:235
console.error @ AuthErrorMonitor.js:24
console.error @ react-hydration-helpers.js:93
console.error @ AuthErrorMonitor.js:24
console.error @ AuthErrorMonitor.js:24
console.error @ AuthErrorMonitor.js:24
console.error @ index.js:56
handleExport @ index.js:390
onClick @ index.js:834
callCallback @ react-dom.development.js:4164
invokeGuardedCallbackDev @ react-dom.development.js:4213
invokeGuardedCallback @ react-dom.development.js:4277
invokeGuardedCallbackAndCatchFirstError @ react-dom.development.js:4291
executeDispatch @ react-dom.development.js:9041
processDispatchQueueItemsInOrder @ react-dom.development.js:9073
processDispatchQueue @ react-dom.development.js:9086
dispatchEventsForPlugins @ react-dom.development.js:9097
eval @ react-dom.development.js:9288
batchedUpdates$1 @ react-dom.development.js:26179
batchedUpdates @ react-dom.development.js:3991
dispatchEventForPluginEventSystem @ react-dom.development.js:9287
dispatchEventWithEnableCapturePhaseSelectiveHydrationWithoutDiscreteEventReplay @ react-dom.development.js:6465
dispatchEvent @ react-dom.development.js:6457
dispatchDiscreteEvent @ react-dom.development.js:6430
# Development Environment Configuration
# This file is used when NODE_ENV=development or when running npm run dev

# Supabase Configuration (same as production)
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY

# Site Configuration - Development URLs (flexible for any Vercel deployment)
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000/admin

# OneSignal Configuration (same as production)
NEXT_PUBLIC_ONESIGNAL_APP_ID=************************************
ONESIGNAL_API_KEY=
ONESIGNAL_REST_API_KEY=nivzmsejbeoiehtwsovd4sjyq

# Email Configuration (same as production)
GMAIL_SMTP_HOST=smtp.gmail.com
GMAIL_SMTP_PORT=587
GMAIL_SMTP_SECURE=false
GMAIL_SMTP_USER=<EMAIL>
GMAIL_SMTP_APP_PASSWORD=jjmfjcfqqrzgsogy
GMAIL_FROM_NAME=OceanSoulSparkles
GMAIL_FROM_EMAIL=<EMAIL>

# Google Configuration (same as production)
GOOGLE_CLIENT_ID=429450887908-bdaro6svl47787ec21bi11251eoolj9n.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Development Security Configuration - More permissive
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_DEBUG_AUTH=true
ENABLE_AUTH_BYPASS=true
NEXT_PUBLIC_ENABLE_AUTH_BYPASS=true
FORCE_EMAIL_IN_DEV=true

# Square Configuration - Sandbox for development
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox  # Server-side only
NEXT_PUBLIC_SQUARE_ENVIRONMENT=sandbox  # Client-side accessible

# Security Headers - Disabled for development flexibility
NEXT_PUBLIC_ENABLE_SECURITY_HEADERS=false
NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=false
