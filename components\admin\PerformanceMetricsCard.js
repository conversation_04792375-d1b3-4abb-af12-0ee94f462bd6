import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { getAuthToken } from '@/lib/auth-token-manager'
import styles from '@/styles/admin/PerformanceMetricsCard.module.css'

export default function PerformanceMetricsCard({ profile }) {
  const { user } = useAuth()
  const [metrics, setMetrics] = useState({
    thisMonth: {
      bookings: 0,
      revenue: 0,
      rating: 0,
      completionRate: 0
    },
    lastMonth: {
      bookings: 0,
      revenue: 0,
      rating: 0,
      completionRate: 0
    },
    trends: {
      bookings: 0,
      revenue: 0,
      rating: 0,
      completionRate: 0
    }
  })
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth')

  useEffect(() => {
    fetchMetrics()
  }, [])

  const fetchMetrics = async () => {
    try {
      setLoading(true)
      const authToken = await getAuthToken()
      const response = await fetch('/api/artist/performance-metrics', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setMetrics(data.metrics || metrics)
      }
    } catch (error) {
      console.error('Error fetching performance metrics:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount)
  }

  const formatPercentage = (value) => {
    return `${Math.round(value)}%`
  }

  const getTrendIcon = (trend) => {
    if (trend > 0) return '↗️'
    if (trend < 0) return '↘️'
    return '➡️'
  }

  const getTrendColor = (trend) => {
    if (trend > 0) return '#10b981'
    if (trend < 0) return '#ef4444'
    return '#6b7280'
  }

  const currentMetrics = metrics[selectedPeriod]

  if (loading) {
    return (
      <div className={styles.card}>
        <div className={styles.header}>
          <h3>Performance Metrics</h3>
        </div>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <span>Loading metrics...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3>Performance Metrics</h3>
        <div className={styles.periodSelector}>
          <button
            onClick={() => setSelectedPeriod('thisMonth')}
            className={`${styles.periodButton} ${selectedPeriod === 'thisMonth' ? styles.active : ''}`}
          >
            This Month
          </button>
          <button
            onClick={() => setSelectedPeriod('lastMonth')}
            className={`${styles.periodButton} ${selectedPeriod === 'lastMonth' ? styles.active : ''}`}
          >
            Last Month
          </button>
        </div>
      </div>

      <div className={styles.content}>
        {/* Key Metrics Grid */}
        <div className={styles.metricsGrid}>
          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <span className={styles.metricLabel}>Total Bookings</span>
              <span 
                className={styles.trendIndicator}
                style={{ color: getTrendColor(metrics.trends.bookings) }}
              >
                {getTrendIcon(metrics.trends.bookings)} {Math.abs(metrics.trends.bookings)}%
              </span>
            </div>
            <div className={styles.metricValue}>
              {currentMetrics.bookings}
            </div>
            <div className={styles.metricSubtext}>
              {selectedPeriod === 'thisMonth' ? 'This month' : 'Last month'}
            </div>
          </div>

          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <span className={styles.metricLabel}>Revenue</span>
              <span 
                className={styles.trendIndicator}
                style={{ color: getTrendColor(metrics.trends.revenue) }}
              >
                {getTrendIcon(metrics.trends.revenue)} {Math.abs(metrics.trends.revenue)}%
              </span>
            </div>
            <div className={styles.metricValue}>
              {formatCurrency(currentMetrics.revenue)}
            </div>
            <div className={styles.metricSubtext}>
              Gross earnings
            </div>
          </div>

          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <span className={styles.metricLabel}>Average Rating</span>
              <span 
                className={styles.trendIndicator}
                style={{ color: getTrendColor(metrics.trends.rating) }}
              >
                {getTrendIcon(metrics.trends.rating)} {Math.abs(metrics.trends.rating)}%
              </span>
            </div>
            <div className={styles.metricValue}>
              {currentMetrics.rating.toFixed(1)} ⭐
            </div>
            <div className={styles.metricSubtext}>
              Customer satisfaction
            </div>
          </div>

          <div className={styles.metricCard}>
            <div className={styles.metricHeader}>
              <span className={styles.metricLabel}>Completion Rate</span>
              <span 
                className={styles.trendIndicator}
                style={{ color: getTrendColor(metrics.trends.completionRate) }}
              >
                {getTrendIcon(metrics.trends.completionRate)} {Math.abs(metrics.trends.completionRate)}%
              </span>
            </div>
            <div className={styles.metricValue}>
              {formatPercentage(currentMetrics.completionRate)}
            </div>
            <div className={styles.metricSubtext}>
              Successful bookings
            </div>
          </div>
        </div>

        {/* Performance Summary */}
        <div className={styles.summarySection}>
          <h4>Performance Summary</h4>
          <div className={styles.summaryGrid}>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Best Service</span>
              <span className={styles.summaryValue}>Face Painting</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Peak Hours</span>
              <span className={styles.summaryValue}>2PM - 4PM</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Repeat Customers</span>
              <span className={styles.summaryValue}>23%</span>
            </div>
            <div className={styles.summaryItem}>
              <span className={styles.summaryLabel}>Avg. Booking Value</span>
              <span className={styles.summaryValue}>{formatCurrency(currentMetrics.revenue / Math.max(currentMetrics.bookings, 1))}</span>
            </div>
          </div>
        </div>

        {/* Goals Progress */}
        <div className={styles.goalsSection}>
          <h4>Monthly Goals</h4>
          <div className={styles.goalsList}>
            <div className={styles.goalItem}>
              <div className={styles.goalHeader}>
                <span className={styles.goalLabel}>Bookings Target</span>
                <span className={styles.goalProgress}>
                  {currentMetrics.bookings} / {profile?.monthly_booking_goal || 50}
                </span>
              </div>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ 
                    width: `${Math.min((currentMetrics.bookings / (profile?.monthly_booking_goal || 50)) * 100, 100)}%`,
                    backgroundColor: currentMetrics.bookings >= (profile?.monthly_booking_goal || 50) ? '#10b981' : '#3b82f6'
                  }}
                />
              </div>
            </div>

            <div className={styles.goalItem}>
              <div className={styles.goalHeader}>
                <span className={styles.goalLabel}>Revenue Target</span>
                <span className={styles.goalProgress}>
                  {formatCurrency(currentMetrics.revenue)} / {formatCurrency(profile?.monthly_revenue_goal || 2000)}
                </span>
              </div>
              <div className={styles.progressBar}>
                <div 
                  className={styles.progressFill}
                  style={{ 
                    width: `${Math.min((currentMetrics.revenue / (profile?.monthly_revenue_goal || 2000)) * 100, 100)}%`,
                    backgroundColor: currentMetrics.revenue >= (profile?.monthly_revenue_goal || 2000) ? '#10b981' : '#3b82f6'
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={styles.actionsSection}>
          <button 
            className={styles.actionButton}
            onClick={() => window.open('/admin/analytics', '_blank')}
          >
            View Detailed Analytics
          </button>
          <button 
            className={styles.actionButton}
            onClick={fetchMetrics}
          >
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  )
}
