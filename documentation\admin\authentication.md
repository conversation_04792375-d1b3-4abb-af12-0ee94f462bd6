# Authentication System

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel authentication system.

## Overview

The authentication system is built using Supabase Auth, providing secure login, session management, and role-based access control for the admin panel. This system ensures that only authorized users can access administrative functions.

## Features

- Secure login with email/password
- Role-based access control (<PERSON><PERSON>, Staff)
- Password reset functionality
- Session management
- Protection for admin routes and API endpoints
- Audit logging for authentication events

## Implementation Steps

### 1. Set Up Supabase Project

1. Create a new Supabase project at [https://app.supabase.com](https://app.supabase.com)
2. Note your Supabase URL and anon key for configuration

### 2. Configure Supabase Client

Create a Supabase client configuration file:

```javascript
// lib/supabase.js
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnon<PERSON>ey)
```

### 3. Set Up Environment Variables

Add the following to your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

Add these variables to your Vercel project settings as well.

### 4. Create User Roles in Supabase

1. In Supabase, go to SQL Editor
2. Run the following SQL to create a custom claims function:

```sql
-- Create a function to get user roles
create or replace function public.get_user_role(user_id uuid)
returns text as $$
declare
  role_name text;
begin
  select role into role_name from public.user_roles where user_id = $1;
  return role_name;
end;
$$ language plpgsql security definer;

-- Create a table for user roles
create table public.user_roles (
  id uuid references auth.users on delete cascade not null primary key,
  role text not null check (role in ('admin', 'staff')) default 'staff'
);

-- Enable RLS
alter table public.user_roles enable row level security;

-- Create policies
create policy "Users can view their own role" on public.user_roles
  for select using (auth.uid() = id);

create policy "Admins can view all roles" on public.user_roles
  for select using (
    get_user_role(auth.uid()) = 'admin'
  );

create policy "Admins can update roles" on public.user_roles
  for update using (
    get_user_role(auth.uid()) = 'admin'
  );
```

### 5. Create Authentication Utilities

Create authentication utility functions:

```javascript
// lib/auth.js
import { supabase } from './supabase'

// Sign in with email and password
export async function signIn(email, password) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  return { data, error }
}

// Sign out
export async function signOut() {
  const { error } = await supabase.auth.signOut()
  return { error }
}

// Get current user
export async function getCurrentUser() {
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error || !session) {
    return { user: null, error }
  }
  
  // Get user role
  const { data: roleData, error: roleError } = await supabase
    .from('user_roles')
    .select('role')
    .eq('id', session.user.id)
    .single()
  
  if (roleError || !roleData) {
    return { 
      user: session.user, 
      role: null, 
      error: roleError 
    }
  }
  
  return { 
    user: session.user, 
    role: roleData.role, 
    error: null 
  }
}

// Check if user has admin role
export async function isAdmin() {
  const { user, role, error } = await getCurrentUser()
  
  if (error || !user) {
    return false
  }
  
  return role === 'admin'
}

// Check if user has staff role or higher
export async function isStaffOrAdmin() {
  const { user, role, error } = await getCurrentUser()
  
  if (error || !user) {
    return false
  }
  
  return role === 'staff' || role === 'admin'
}
```

### 6. Create Authentication Context

Create a React context for authentication:

```javascript
// contexts/AuthContext.js
import { createContext, useContext, useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { getCurrentUser } from '@/lib/auth'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [role, setRole] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { user, role, error } = await getCurrentUser()
      
      setUser(user || null)
      setRole(role || null)
      setLoading(false)
    }
    
    getInitialSession()
    
    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session) {
          const { user, role } = await getCurrentUser()
          setUser(user || null)
          setRole(role || null)
        } else {
          setUser(null)
          setRole(null)
        }
        setLoading(false)
      }
    )
    
    return () => {
      authListener?.subscription?.unsubscribe()
    }
  }, [])
  
  const value = {
    user,
    role,
    loading,
    isAdmin: role === 'admin',
    isStaff: role === 'staff',
    isAuthenticated: !!user,
  }
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  return useContext(AuthContext)
}
```

### 7. Create Protected Route Component

Create a higher-order component for protected routes:

```javascript
// components/admin/ProtectedRoute.js
import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'

export default function ProtectedRoute({ children, adminOnly = false }) {
  const { user, role, loading, isAdmin } = useAuth()
  const router = useRouter()
  
  // If still loading, show loading state
  if (loading) {
    return <div>Loading...</div>
  }
  
  // If not authenticated, redirect to login
  if (!user) {
    router.push('/admin/login')
    return null
  }
  
  // If admin only and not admin, redirect to dashboard
  if (adminOnly && !isAdmin) {
    router.push('/admin')
    return null
  }
  
  // Otherwise, render children
  return <>{children}</>
}
```

### 8. Create Login Page

Create the admin login page:

```javascript
// pages/admin/login.js
import { useState } from 'react'
import { useRouter } from 'next/router'
import { signIn } from '@/lib/auth'
import styles from '@/styles/admin/Login.module.css'

export default function Login() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    
    const { data, error } = await signIn(email, password)
    
    if (error) {
      setError(error.message)
      setLoading(false)
      return
    }
    
    // Redirect to admin dashboard
    router.push('/admin')
  }
  
  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginCard}>
        <h1>OceanSoulSparkles Admin</h1>
        
        {error && <div className={styles.error}>{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <div className={styles.formGroup}>
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          
          <div className={styles.formGroup}>
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          
          <button
            type="submit"
            className={styles.loginButton}
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Log In'}
          </button>
        </form>
      </div>
    </div>
  )
}
```

### 9. Update _app.js to Include Auth Provider

Wrap your application with the AuthProvider:

```javascript
// pages/_app.js
import { AuthProvider } from '@/contexts/AuthContext'

function MyApp({ Component, pageProps }) {
  return (
    <AuthProvider>
      <Component {...pageProps} />
    </AuthProvider>
  )
}

export default MyApp
```

## Testing

1. Create test admin and staff users in Supabase
2. Test login functionality
3. Test role-based access control
4. Test protected routes
5. Test API endpoint protection

## Security Considerations

- Use HTTPS for all communications
- Implement proper password policies
- Set up rate limiting for login attempts
- Use secure cookies for session management
- Implement CSRF protection
- Log all authentication events for audit purposes
