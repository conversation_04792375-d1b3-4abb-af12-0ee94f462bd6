/**
 * Admin Performance Diagnostics API
 * Provides comprehensive performance metrics for the admin dashboard
 */

import { withAdminAuth } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const startTime = Date.now();
  const adminClient = getAdminClient();
  const diagnostics = {
    timestamp: new Date().toISOString(),
    performance: {},
    database: {},
    authentication: {},
    recommendations: []
  };

  try {
    // 1. Database Performance Metrics
    console.log('[Performance] Checking database metrics...');
    
    // Check unused indexes count
    const { data: unusedIndexes } = await adminClient.rpc('exec_sql', {
      sql: `
        SELECT COUNT(*) as count
        FROM pg_stat_user_indexes
        WHERE schemaname = 'public'
          AND idx_scan = 0
          AND indexrelname NOT LIKE '%_pkey'
          AND indexrelname NOT LIKE '%_key'
          AND indexrelname NOT LIKE '%_fkey'
      `
    });

    diagnostics.database.unusedIndexes = unusedIndexes?.[0]?.count || 0;

    // Check total indexes
    const { data: totalIndexes } = await adminClient.rpc('exec_sql', {
      sql: `SELECT COUNT(*) as count FROM pg_indexes WHERE schemaname = 'public'`
    });

    diagnostics.database.totalIndexes = totalIndexes?.[0]?.count || 0;

    // Check authentication table performance
    const authStartTime = Date.now();
    const { data: roleCheck } = await adminClient
      .from('user_roles')
      .select('role')
      .limit(1);
    
    diagnostics.authentication.roleQueryTime = Date.now() - authStartTime;
    diagnostics.authentication.roleQuerySuccess = !roleCheck?.error;

    // 2. Performance Recommendations
    if (diagnostics.database.unusedIndexes > 50) {
      diagnostics.recommendations.push({
        type: 'database',
        priority: 'high',
        message: `${diagnostics.database.unusedIndexes} unused indexes detected. Consider removing them to improve performance.`,
        action: 'Remove unused indexes'
      });
    }

    if (diagnostics.authentication.roleQueryTime > 1000) {
      diagnostics.recommendations.push({
        type: 'authentication',
        priority: 'high',
        message: `Role query taking ${diagnostics.authentication.roleQueryTime}ms. Consider implementing caching.`,
        action: 'Implement role caching'
      });
    }

    // 3. Overall Performance Score
    let score = 100;
    if (diagnostics.database.unusedIndexes > 100) score -= 30;
    else if (diagnostics.database.unusedIndexes > 50) score -= 15;
    
    if (diagnostics.authentication.roleQueryTime > 2000) score -= 25;
    else if (diagnostics.authentication.roleQueryTime > 1000) score -= 10;

    diagnostics.performance.score = Math.max(0, score);
    diagnostics.performance.grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
    diagnostics.performance.totalTime = Date.now() - startTime;

    // 4. Status Summary
    diagnostics.status = {
      healthy: diagnostics.performance.score >= 80,
      warnings: diagnostics.recommendations.filter(r => r.priority === 'medium').length,
      errors: diagnostics.recommendations.filter(r => r.priority === 'high').length
    };

    console.log(`[Performance] Diagnostics completed in ${diagnostics.performance.totalTime}ms`);
    console.log(`[Performance] Score: ${diagnostics.performance.score}/100 (${diagnostics.performance.grade})`);

    return res.status(200).json({
      success: true,
      data: diagnostics
    });

  } catch (error) {
    console.error('[Performance] Error running diagnostics:', error);
    
    return res.status(500).json({
      success: false,
      error: 'Failed to run performance diagnostics',
      details: error.message,
      partialData: diagnostics
    });
  }
}

export default withAdminAuth(handler);
