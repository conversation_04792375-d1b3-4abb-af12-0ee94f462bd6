# Artist Availability Security Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the critical security fixes for the Ocean Soul Sparkles artist availability system, addressing the security gaps identified in commit e34aa0ba51bf9080196add1e40e0395786df137e.

## 🚨 CRITICAL - Deploy Before Using Artist Features

The new artist availability functionality **WILL NOT WORK** without these database security fixes. Artists will encounter RLS policy violations when trying to:
- Access their profile data
- Set availability exceptions
- Use the "Set Unavailable This Week" feature

## Deployment Steps

### Step 1: Apply Core RLS Policy Fixes (CRITICAL)
```sql
-- Run this in Supabase SQL Editor
\i db/migrations/secure_artist_tables_rls.sql
```

**What this fixes:**
- ✅ Artists can read their own `artist_profiles` data
- ✅ Artists can insert/update their own `artist_availability_exceptions`
- ✅ All policies use `public.user_roles` table instead of `auth.users.role`
- ✅ Maintains 5-tier role system (DEV, Admin, Artist, Braider, User)
- ✅ Preserves admin/dev full access to all data

### Step 2: Apply Security Enhancements (RECOMMENDED)
```sql
-- Run this in Supabase SQL Editor
\i db/migrations/artist_availability_security_enhancements.sql
```

**What this adds:**
- 🛡️ Rate limiting infrastructure (10 requests/minute per artist)
- 📋 Comprehensive audit logging for availability changes
- ✅ Business logic validation (no past dates, max 1 year future)
- 🔒 Secure access controls for new tables

### Step 3: Verify Deployment (REQUIRED)
```sql
-- Run this test script in Supabase SQL Editor
\i db/migrations/test_artist_availability_rls.sql
```

**Expected output:**
```
PASSED: Artist can read their own profile
PASSED: Artist can insert availability exception
PASSED: Artist can upsert availability exception
PASSED: Artist cannot see other artists profiles
PASSED: Artist cannot insert exceptions for other artists
PASSED: Admin can see all artist profiles
```

## Verification Checklist

### ✅ Database Level Verification
- [ ] All RLS policies created without errors
- [ ] Test script passes all 5 test cases
- [ ] No policy violations in Supabase logs

### ✅ API Level Verification
Test the API endpoint directly:

```bash
# Test with artist credentials
curl -X PUT \
  -H "Authorization: Bearer YOUR_ARTIST_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  https://your-domain.com/api/artist/availability/set-unavailable-week
```

**Expected response:**
```json
{
  "message": "Successfully marked unavailable for the current week.",
  "weekDates": ["2025-01-06", "2025-01-07", ...],
  "affectedDates": 7
}
```

### ✅ UI Level Verification
1. Login as an artist/braider
2. Navigate to Artist Dashboard
3. Click "Set Unavailable This Week" in Quick Actions
4. Verify success message appears
5. Check that availability status updates

## Security Validation

### Role-Based Access Control
- ✅ **DEV/Admin**: Full access to all artist data
- ✅ **Artist/Braider**: Access only to own data
- ✅ **User**: No access to artist tables
- ❌ **Cross-Artist Access**: Artists cannot see other artists' data

### Data Protection
- ✅ **Authentication Required**: All endpoints require valid JWT
- ✅ **Role Verification**: Only artist/braider roles can modify availability
- ✅ **Data Isolation**: RLS policies prevent cross-artist data access
- ✅ **Audit Trail**: All changes logged with user ID and timestamp

### Rate Limiting (If Enhanced Security Applied)
- ✅ **API Protection**: Max 10 requests per minute per artist
- ✅ **Abuse Prevention**: Rate limit violations return 429 status
- ✅ **Graceful Degradation**: Rate limits don't affect other users

## Troubleshooting

### Common Issues

#### 1. "Row Level Security Policy Violation"
**Cause**: RLS policies not applied or user role not set correctly
**Solution**: 
```sql
-- Check user role
SELECT role FROM public.user_roles WHERE id = 'USER_ID_HERE';

-- If no role found, add it
INSERT INTO public.user_roles (id, role) VALUES ('USER_ID_HERE', 'artist');
```

#### 2. "Artist profile not found"
**Cause**: Artist profile doesn't exist for the user
**Solution**:
```sql
-- Create artist profile
INSERT INTO public.artist_profiles (user_id, artist_name, display_name)
VALUES ('USER_ID_HERE', 'Artist Name', 'Display Name');
```

#### 3. "Rate limit exceeded"
**Cause**: Too many requests in short time (if enhanced security applied)
**Solution**: Wait 1 minute or increase rate limits in function

#### 4. "Invalid date range"
**Cause**: Trying to set availability for past dates (if enhanced security applied)
**Solution**: Only set availability for current or future dates

### Debug Queries

```sql
-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('artist_profiles', 'artist_availability_exceptions');

-- Check user context
SELECT auth.uid(), auth.role();

-- Check user role
SELECT ur.role FROM public.user_roles ur WHERE ur.id = auth.uid();

-- Check artist profile
SELECT ap.* FROM public.artist_profiles ap WHERE ap.user_id = auth.uid();
```

## Rollback Plan

If issues occur, rollback by dropping the new policies:

```sql
-- Rollback RLS policies (EMERGENCY ONLY)
DROP POLICY IF EXISTS "Artists can read own profile" ON public.artist_profiles;
DROP POLICY IF EXISTS "Artists can read own availability_exceptions" ON public.artist_availability_exceptions;
DROP POLICY IF EXISTS "Admin dev and artists can insert artist_availability_exceptions" ON public.artist_availability_exceptions;
DROP POLICY IF EXISTS "Admin dev and artists can update artist_availability_exceptions" ON public.artist_availability_exceptions;

-- Restore original admin-only policies
CREATE POLICY "Admin can read artist_profiles" ON public.artist_profiles
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (SELECT 1 FROM public.user_roles ur WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin'))
  );
```

## Post-Deployment Monitoring

### Key Metrics to Monitor
1. **API Success Rate**: Should be >95% for artist availability endpoints
2. **RLS Policy Violations**: Should be 0 in Supabase logs
3. **Rate Limit Hits**: Monitor for abuse patterns
4. **Audit Log Volume**: Verify all changes are being logged

### Alerts to Set Up
- RLS policy violations in artist tables
- High rate limit violation rates
- Failed artist profile lookups
- Missing audit log entries

## Support

If you encounter issues during deployment:
1. Check the troubleshooting section above
2. Run the verification test script
3. Review Supabase logs for specific error messages
4. Ensure all migration files were applied in order

**Remember**: The artist availability features will not work until the core RLS policy fixes are deployed.
