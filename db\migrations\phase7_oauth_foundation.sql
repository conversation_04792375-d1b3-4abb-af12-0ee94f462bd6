-- Phase 7: OAuth Foundation & Security Framework
-- Ocean Soul Sparkles - Advanced Integrations & Ecosystem
-- Database schema for OAuth 2.0 and integration management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- INTEGRATION CREDENTIALS TABLE
-- Stores encrypted OAuth tokens and credentials
-- =====================================================

CREATE TABLE IF NOT EXISTS integration_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    encrypted_tokens TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one credential per user per provider
    UNIQUE(user_id, provider)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_integration_credentials_user_id ON integration_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_integration_credentials_provider ON integration_credentials(provider);
CREATE INDEX IF NOT EXISTS idx_integration_credentials_expires_at ON integration_credentials(expires_at);

-- =====================================================
-- INTEGRATION LOGS TABLE
-- Audit trail for integration activities
-- =====================================================

CREATE TABLE IF NOT EXISTS integration_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    provider VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error', 'warning', 'info')),
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance and querying
CREATE INDEX IF NOT EXISTS idx_integration_logs_user_id ON integration_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_integration_logs_provider ON integration_logs(provider);
CREATE INDEX IF NOT EXISTS idx_integration_logs_action ON integration_logs(action);
CREATE INDEX IF NOT EXISTS idx_integration_logs_status ON integration_logs(status);
CREATE INDEX IF NOT EXISTS idx_integration_logs_created_at ON integration_logs(created_at);

-- =====================================================
-- INTEGRATION SETTINGS TABLE
-- User preferences and configuration for integrations
-- =====================================================

CREATE TABLE IF NOT EXISTS integration_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    settings JSONB DEFAULT '{}',
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one settings record per user per provider
    UNIQUE(user_id, provider)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_integration_settings_user_id ON integration_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_integration_settings_provider ON integration_settings(provider);
CREATE INDEX IF NOT EXISTS idx_integration_settings_enabled ON integration_settings(enabled);

-- =====================================================
-- SECURITY LOGS TABLE
-- Security events and audit trail
-- =====================================================

CREATE TABLE IF NOT EXISTS security_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_name VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for security monitoring
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_name ON security_logs(event_name);
CREATE INDEX IF NOT EXISTS idx_security_logs_severity ON security_logs(severity);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);

-- =====================================================
-- API ACCESS LOGS TABLE
-- Track API usage and performance
-- =====================================================

CREATE TABLE IF NOT EXISTS api_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status INTEGER NOT NULL,
    response_time INTEGER, -- in milliseconds
    details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance monitoring
CREATE INDEX IF NOT EXISTS idx_api_access_logs_user_id ON api_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_endpoint ON api_access_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_method ON api_access_logs(method);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_status ON api_access_logs(status);
CREATE INDEX IF NOT EXISTS idx_api_access_logs_created_at ON api_access_logs(created_at);

-- =====================================================
-- RATE LIMIT REQUESTS TABLE
-- Distributed rate limiting support
-- =====================================================

CREATE TABLE IF NOT EXISTS rate_limit_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for rate limiting queries
CREATE INDEX IF NOT EXISTS idx_rate_limit_requests_identifier ON rate_limit_requests(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limit_requests_created_at ON rate_limit_requests(created_at);

-- =====================================================
-- INTEGRATION SYNC STATUS TABLE
-- Track synchronization status for integrations
-- =====================================================

CREATE TABLE IF NOT EXISTS integration_sync_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- 'calendar', 'contacts', 'posts', etc.
    last_sync_at TIMESTAMP WITH TIME ZONE,
    next_sync_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'paused')),
    error_message TEXT,
    sync_details JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one sync status per user per provider per type
    UNIQUE(user_id, provider, sync_type)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_integration_sync_status_user_id ON integration_sync_status(user_id);
CREATE INDEX IF NOT EXISTS idx_integration_sync_status_provider ON integration_sync_status(provider);
CREATE INDEX IF NOT EXISTS idx_integration_sync_status_sync_type ON integration_sync_status(sync_type);
CREATE INDEX IF NOT EXISTS idx_integration_sync_status_status ON integration_sync_status(status);
CREATE INDEX IF NOT EXISTS idx_integration_sync_status_next_sync_at ON integration_sync_status(next_sync_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- Secure access to integration data
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE integration_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limit_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_sync_status ENABLE ROW LEVEL SECURITY;

-- Integration credentials policies
CREATE POLICY "Users can view their own integration credentials" ON integration_credentials
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own integration credentials" ON integration_credentials
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own integration credentials" ON integration_credentials
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own integration credentials" ON integration_credentials
    FOR DELETE USING (auth.uid() = user_id);

-- Integration logs policies
CREATE POLICY "Users can view their own integration logs" ON integration_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can insert integration logs" ON integration_logs
    FOR INSERT WITH CHECK (true);

-- Integration settings policies
CREATE POLICY "Users can manage their own integration settings" ON integration_settings
    FOR ALL USING (auth.uid() = user_id);

-- Security logs policies (admin only)
CREATE POLICY "Admins can view security logs" ON security_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_id = auth.uid() 
            AND role IN ('dev', 'admin')
        )
    );

CREATE POLICY "Service role can insert security logs" ON security_logs
    FOR INSERT WITH CHECK (true);

-- API access logs policies (admin only)
CREATE POLICY "Admins can view API access logs" ON api_access_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_id = auth.uid() 
            AND role IN ('dev', 'admin')
        )
    );

CREATE POLICY "Service role can insert API access logs" ON api_access_logs
    FOR INSERT WITH CHECK (true);

-- Rate limit requests policies (service role only)
CREATE POLICY "Service role can manage rate limit requests" ON rate_limit_requests
    FOR ALL USING (true);

-- Integration sync status policies
CREATE POLICY "Users can view their own sync status" ON integration_sync_status
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own sync status" ON integration_sync_status
    FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- Automated maintenance and updates
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_integration_credentials_updated_at 
    BEFORE UPDATE ON integration_credentials 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_integration_settings_updated_at 
    BEFORE UPDATE ON integration_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_integration_sync_status_updated_at 
    BEFORE UPDATE ON integration_sync_status 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up old logs
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS void AS $$
BEGIN
    -- Clean up logs older than 90 days
    DELETE FROM integration_logs WHERE created_at < NOW() - INTERVAL '90 days';
    DELETE FROM security_logs WHERE created_at < NOW() - INTERVAL '90 days';
    DELETE FROM api_access_logs WHERE created_at < NOW() - INTERVAL '30 days';
    DELETE FROM rate_limit_requests WHERE created_at < NOW() - INTERVAL '1 day';
END;
$$ language 'plpgsql';

-- =====================================================
-- INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Insert default integration settings for existing users
INSERT INTO integration_settings (user_id, provider, settings, enabled)
SELECT 
    id as user_id,
    'google_calendar' as provider,
    '{"sync_enabled": false, "sync_frequency": "hourly", "conflict_resolution": "manual"}' as settings,
    false as enabled
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM integration_settings WHERE provider = 'google_calendar')
ON CONFLICT (user_id, provider) DO NOTHING;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE integration_credentials IS 'Stores encrypted OAuth tokens and credentials for third-party integrations';
COMMENT ON TABLE integration_logs IS 'Audit trail for all integration activities and events';
COMMENT ON TABLE integration_settings IS 'User preferences and configuration for each integration provider';
COMMENT ON TABLE security_logs IS 'Security events and audit trail for monitoring and compliance';
COMMENT ON TABLE api_access_logs IS 'API usage tracking for performance monitoring and analytics';
COMMENT ON TABLE rate_limit_requests IS 'Distributed rate limiting support across multiple instances';
COMMENT ON TABLE integration_sync_status IS 'Track synchronization status and scheduling for integrations';

-- Migration complete
SELECT 'Phase 7 OAuth Foundation migration completed successfully' as status;
