# Ocean Soul Sparkles Square Refund Demo - Step-by-Step

## Quick Start Guide for Testing Square Refunds

### Prerequisites Verification

**1. Check Environment Configuration**
Open `.env.local` and verify these variables are set:
```env
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
```

**2. Verify Admin Access**
- Ensure you have Admin or DEV role permissions
- Development mode should have `ENABLE_AUTH_BYPASS=true`

### Demo Walkthrough

#### Step 1: Access Payment Management
1. **Navigate to**: `http://localhost:3000/admin/payments`
2. **Login**: Use admin credentials or development bypass
3. **Verify**: You should see the Payment Management interface

#### Step 2: Locate Test Payment
Look for the test payment with these details:
- **Amount**: $150.00 AUD
- **Payment Method**: square_terminal
- **Status**: completed
- **Transaction ID**: test_payment_refund_demo_001

**Search Tips:**
- Use the search box to search for "150" or "test_payment"
- Filter by payment method "square"
- Sort by date (newest first)

#### Step 3: Initiate Refund Process
1. **Find the Refund Button**: Look for a red "Refund" button in the Actions column
2. **Click Refund**: This opens the RefundModal
3. **Verify Modal Content**: Should show payment details and refund form

#### Step 4: Process Partial Refund (Test 1)
**Fill out the refund form:**
- **Refund Amount**: `75.00` (half of $150.00)
- **Refund Reason**: Select "Customer Request"
- **Refund Method**: Should auto-select "Square API"
- **Refund Notes**: "Testing partial refund functionality"

**Process the refund:**
1. Click "Process Refund" button
2. Confirm in the confirmation dialog
3. Wait for processing (2-5 seconds)

**Expected Results:**
- ✅ Success message appears
- ✅ Payment status changes to "partially_refunded"
- ✅ Refund history shows new entry
- ✅ Remaining amount shows $75.00

#### Step 5: Process Full Refund (Test 2)
**Process the remaining amount:**
- **Refund Amount**: `75.00` (remaining balance)
- **Refund Reason**: Select "Service Issue"
- **Refund Notes**: "Testing full refund completion"

**Expected Results:**
- ✅ Payment status changes to "refunded"
- ✅ No more refund button appears
- ✅ Total refunded shows $150.00

### Verification Steps

#### In Ocean Soul Sparkles Admin
1. **Payment Status**: Should show "refunded"
2. **Refund History**: Should show 2 refund entries
3. **Total Refunded**: Should equal original payment amount

#### In Square Sandbox Dashboard
1. **Navigate to**: `https://squareupsandbox.com/dashboard`
2. **Login**: Use your Square sandbox account
3. **Go to**: Transactions → Refunds
4. **Verify**: Refunds appear with correct amounts and timestamps

### Troubleshooting Common Issues

#### Issue: "Refund button not visible"
**Possible Causes:**
- User doesn't have Admin/DEV role
- Payment status is not "completed"
- Payment method doesn't contain "square"

**Solutions:**
- Check user role in database
- Verify payment status
- Ensure payment was processed through Square

#### Issue: "Square refund failed"
**Possible Causes:**
- Invalid Square API credentials
- Network connectivity issues
- Transaction ID not found in Square

**Solutions:**
- Verify environment variables
- Check internet connection
- Confirm transaction exists in Square dashboard

#### Issue: "Refund amount exceeds available balance"
**Cause:** Trying to refund more than remaining amount
**Solution:** Check existing refunds and calculate remaining balance

### Advanced Testing Scenarios

#### Test 3: Multiple Partial Refunds
1. Create another test payment for $200.00
2. Process refunds of $50.00, $75.00, and $75.00
3. Verify payment status updates correctly at each step

#### Test 4: Error Handling
1. Try to refund more than the available balance
2. Verify appropriate error message appears
3. Confirm no partial refund is processed

#### Test 5: Cash vs Square Refund Methods
1. Create a cash payment
2. Verify refund method auto-selects "Cash"
3. Compare processing behavior with Square refunds

### API Testing with Browser Console

Open browser developer tools and monitor these logs:

**Successful Refund:**
```
[abc123] Refund API called for payment: d2b0927a-f7dd-49cf-96d4-7b04c17c666d
[abc123] Processing Square refund: {idempotency_key: "...", amount_money: {...}}
[abc123] Refund created successfully: refund-uuid
```

**Failed Refund:**
```
[abc123] Square refund failed: Error message
[abc123] Refund API Error: Failed to process refund request
```

### Database Verification

Check refund records in the database:

```sql
-- View all refunds for the test payment
SELECT 
  r.id,
  r.refund_amount,
  r.refund_reason,
  r.refund_method,
  r.refund_status,
  r.square_refund_id,
  r.processed_at
FROM public.refunds r
WHERE r.payment_id = 'd2b0927a-f7dd-49cf-96d4-7b04c17c666d'
ORDER BY r.created_at DESC;

-- Check payment status update
SELECT 
  id,
  amount,
  payment_status,
  updated_at
FROM public.payments 
WHERE id = 'd2b0927a-f7dd-49cf-96d4-7b04c17c666d';
```

### Performance Monitoring

**Expected Processing Times:**
- **Square API Refund**: 2-5 seconds
- **Cash Refund**: Instant (marked completed immediately)
- **Manual Refund**: Instant (marked completed immediately)

**Network Requests:**
- POST to `/api/admin/payments/[id]/refund`
- GET to `/api/admin/payments/[id]/refunds` (for history refresh)
- Square API call to `https://connect.squareupsandbox.com/v2/refunds`

### Success Criteria

✅ **Refund Processing**: Both partial and full refunds process successfully
✅ **Status Updates**: Payment status updates correctly (partially_refunded → refunded)
✅ **Square Integration**: Refunds appear in Square sandbox dashboard
✅ **Audit Trail**: Complete refund history with admin user tracking
✅ **Error Handling**: Appropriate error messages for invalid operations
✅ **UI/UX**: Responsive interface works on desktop and mobile
✅ **Security**: Only Admin/DEV users can access refund functionality

### Next Steps After Testing

1. **Production Configuration**: Update environment variables for production
2. **Staff Training**: Train admin staff on refund procedures
3. **Documentation**: Share this guide with relevant team members
4. **Monitoring**: Set up alerts for failed refunds
5. **Backup Procedures**: Document manual refund processes for emergencies

### Support Contacts

- **Technical Issues**: Check application logs and console output
- **Square API Issues**: Contact Square Developer Support
- **Database Issues**: Check Supabase dashboard and logs
- **Business Logic**: Review refund policies and procedures
