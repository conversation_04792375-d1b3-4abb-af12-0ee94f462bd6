import { useState } from 'react'
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * QuickEventServiceSelector component for streamlined category-based service and tier selection
 * Two-tier navigation: Categories → Service Variations/Tiers. Now includes a cart.
 *
 * @param {Object} props - Component props
 * @param {Array} props.services - Array of services with pricing tiers
 * @param {Array} props.quickEventCart - Array of items in the cart
 * @param {Function} props.onAddToCart - Callback to add a service and tier to the cart
 * @param {Function} props.onRemoveFromCart - Callback to remove an item from the cart
 * @param {Function} props.onProceedToCheckout - Callback to proceed to checkout
 * @returns {JSX.Element}
 */
export default function QuickEventServiceSelector({
  services,
  quickEventCart,
  onAddToCart,
  onRemoveFromCart,
  onProceedToCheckout
}) {
  const [selectedCategory, setSelectedCategory] = useState(null)
  // selectedServiceId and selectedTierId are removed as selection is now cart-based

  // Get services suitable for quick events and organize by category
  const getQuickEventServices = () => {
    console.log('🔍 QuickEventServiceSelector - Total services received:', services.length)
    console.log('🔍 QuickEventServiceSelector - Sample service:', services[0])

    const filteredServices = services.filter(service => {
      // Include services that are visible on events or POS
      // Handle both boolean and string representations
      const visibleOnEvents = service.visible_on_events === true || service.visible_on_events === 'true'
      const visibleOnPos = service.visible_on_pos === true || service.visible_on_pos === 'true'

      // Fallback: if visibility flags are undefined/null, include the service
      // This ensures backward compatibility and prevents empty service lists
      const hasVisibilityFlags = service.visible_on_events !== undefined || service.visible_on_pos !== undefined
      const isVisible = hasVisibilityFlags ? (visibleOnEvents || visibleOnPos) : true

      console.log(`🔍 Service "${service.name}": visible_on_events=${service.visible_on_events}, visible_on_pos=${service.visible_on_pos}, hasFlags=${hasVisibilityFlags}, included=${isVisible}`)
      return isVisible
    })

    console.log('🔍 QuickEventServiceSelector - Filtered services count:', filteredServices.length)
    console.log('🔍 QuickEventServiceSelector - Filtered services:', filteredServices.map(s => s.name))
    return filteredServices
  }

  // Group services by category for two-tier navigation
  const getServiceCategories = () => {
    const quickServices = getQuickEventServices()
    const categoryMap = new Map()

    quickServices.forEach(service => {
      // Handle empty/null categories properly
      const rawCategory = service.category || ''
      const category = rawCategory.trim() || 'General'
      const categoryKey = category.toLowerCase().trim() || 'general'

      console.log(`🔍 Service "${service.name}" - Raw category: "${rawCategory}", Processed: "${category}", Key: "${categoryKey}"`)

      if (!categoryMap.has(categoryKey)) {
        categoryMap.set(categoryKey, {
          name: category,
          key: categoryKey,
          services: [],
          icon: getCategoryIcon(categoryKey)
        })
      }

      categoryMap.get(categoryKey).services.push(service)
    })

    // Sort categories by service count (most popular first)
    const categories = Array.from(categoryMap.values()).sort((a, b) => b.services.length - a.services.length)

    console.log('🔍 QuickEventServiceSelector - Categories organized:', categories.map(cat => ({
      name: cat.name,
      key: cat.key,
      serviceCount: cat.services.length,
      services: cat.services.map(s => s.name)
    })))

    return categories
  }

  // Get category icon based on service category - optimized for quick events
  const getCategoryIcon = (categoryKey) => {
    const iconMap = {
      'face painting': '🎨',
      'painting': '🎨',
      'airbrush': '🎨',
      'makeup': '💄',
      'hair': '💇‍♀️',
      'nails': '💅',
      'massage': '💆‍♀️',
      'skincare': '✨',
      'photography': '📸',
      'entertainment': '🎭',
      'special': '⭐',
      'general': '⭐',
      'glitter': '✨',
      'body': '🎨',
      'uv': '🌟',
      'temporary': '🎨'
    }
    return iconMap[categoryKey?.toLowerCase()] || iconMap.general
  }

  // Get pricing tiers, fallback to service base price if no tiers
  const getPricingTiers = (service) => {
    if (service.pricing_tiers && service.pricing_tiers.length > 0) {
      return service.pricing_tiers.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
    }
    
    // Fallback: create a single tier from service base data
    return [{
      id: `fallback-${service.id}`,
      name: 'Standard',
      description: 'Standard service duration and pricing',
      duration: service.duration || 60,
      price: service.price || 0,
      is_default: true,
      sort_order: 1
    }]
  }

  const formatDuration = (minutes) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    if (remainingMinutes === 0) {
      return `${hours}h`
    }
    return `${hours}h ${remainingMinutes}m`
  }

  const handleCategorySelect = (category) => {
    setSelectedCategory(category)
    // No need to manage selectedServiceId or selectedTierId here anymore
  }

  const handleBackToCategories = () => {
    setSelectedCategory(null)
    // No need to manage selectedServiceId or selectedTierId here anymore
  }

  const handleServiceTierSelect = (service, tier) => {
    // No need to set selectedServiceId or selectedTierId locally for highlighting
    // The parent component (POS terminal) will manage the cart state

    // Small delay for visual feedback (optional, can be removed if not desired for cart)
    setTimeout(() => {
      onAddToCart(service, tier)
    }, 150) // Reduced delay as it's an add to cart, not a page transition
  }

  const serviceCategories = getServiceCategories()

  const calculateTotalAmount = () => {
    return quickEventCart.reduce((total, item) => total + parseFloat(item.tier.price || 0), 0)
  }

  if (serviceCategories.length === 0) {
    return (
      <div className={styles.quickEventSelector}>
        <div className={styles.quickEventHeader}>
          <h2 className={styles.quickEventTitle}>Quick Event Services</h2>
          <p className={styles.quickEventSubtitle}>No quick event services available</p>
        </div>
        <div className={styles.noQuickServices}>
          <div className={styles.noServicesIcon}>⚡</div>
          <h3>No Quick Event Services</h3>
          <p>Please configure services for quick events in the inventory section.</p>
        </div>
      </div>
    )
  }

  // Render category selection (first tier)
  if (!selectedCategory) {
    return (
      <div className={styles.quickEventSelector}>
        <div className={styles.quickEventHeader}>
          <h2 className={styles.quickEventTitle}>Select Service Category</h2>
          <p className={styles.quickEventSubtitle}>Choose a category to see available services</p>
        </div>

        <div className={styles.categoryGrid}>
          {serviceCategories.map((category) => (
            <button
              key={category.key}
              className={styles.categoryButton}
              onClick={() => handleCategorySelect(category)}
            >
              <div className={styles.categoryIcon}>{category.icon}</div>
              <div className={styles.categoryName}>{category.name}</div>
              <div className={styles.categoryCount}>
                {category.services.length} service{category.services.length !== 1 ? 's' : ''}
              </div>
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Render service/tier selection for selected category (second tier)
  return (
    <div className={styles.quickEventSelector}>
      <div className={styles.quickEventHeader}>
        <button
          className={styles.backToCategoriesButton}
          onClick={handleBackToCategories}
        >
          ← Back to Categories
        </button>
        <h2 className={styles.quickEventTitle}>{selectedCategory.name}</h2>
        <p className={styles.quickEventSubtitle}>Select service and duration to add to cart</p>
      </div>

      <div className={styles.quickServiceGrid}>
        {selectedCategory.services.map((service) => {
          const pricingTiers = getPricingTiers(service)

          return (
            <div key={service.id} className={styles.quickServiceCard}>
              <div className={styles.quickServiceHeader}>
                <h3 className={styles.quickServiceName}>
                  {safeRender(service.name, 'Unnamed Service')}
                </h3>
              </div>

              <div className={styles.quickTierGrid}>
                {pricingTiers.map((tier) => {
                  // isSelected state is removed as we now use a cart system
                  const isRecommended = tier.is_default || (pricingTiers.length > 1 && pricingTiers.indexOf(tier) === 1)

                  return (
                    <button
                      key={tier.id}
                      className={`${styles.quickTierButton} ${isRecommended ? styles.recommended : ''}`} // Removed isSelected
                      onClick={() => handleServiceTierSelect(service, tier)}
                    >
                      <div className={styles.quickTierName}>
                        {safeRender(tier.name, 'Tier')}
                      </div>
                      <div className={styles.quickTierDuration}>
                        {formatDuration(tier.duration)}
                      </div>
                      <div className={styles.quickTierPrice}>
                        {safeFormatCurrency(tier.price)}
                      </div>
                      {tier.description && (
                        <div className={styles.quickTierDescription}>
                          {safeRender(tier.description, '')}
                        </div>
                      )}
                    </button>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {quickEventCart && quickEventCart.length > 0 && (
        <div className={styles.cartSection}>
          <h3 className={styles.cartHeader}>Shopping Cart</h3>
          <div className={styles.cartItemList}>
            {quickEventCart.map((item, index) => (
              <div key={index} className={styles.cartItem}>
                <div className={styles.cartItemDetails}>
                  <div className={styles.serviceName}>{safeRender(item.service.name)}</div>
                  <div className={styles.tierInfo}>
                    {safeRender(item.tier.name)} - {formatDuration(item.tier.duration)} - {safeFormatCurrency(item.tier.price)}
                  </div>
                </div>
                <div className={styles.cartItemActions}>
                  <button
                    onClick={() => onRemoveFromCart(index)}
                    className={styles.removeButton}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
          <div className={styles.cartTotal}>
            <p className={styles.totalAmountText}>
              Total: <span>{safeFormatCurrency(calculateTotalAmount())}</span>
            </p>
          </div>
          <button
            onClick={onProceedToCheckout}
            className={styles.proceedButton}
            disabled={quickEventCart.length === 0}
          >
            Proceed to Checkout ({quickEventCart.length} item{quickEventCart.length !== 1 ? 's' : ''})
          </button>
        </div>
      )}
    </div>
  )
}
