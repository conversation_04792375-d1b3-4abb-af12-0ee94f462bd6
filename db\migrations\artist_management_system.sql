-- Artist Management System for POS Terminal
-- This migration creates the artist management system for Ocean Soul Sparkles
-- Supports multi-artist team structure with service specializations and availability

-- =============================================
-- ARTIST PROFILES TABLE
-- =============================================

-- Create artist profiles table (extends user_profiles for artists)
CREATE TABLE IF NOT EXISTS public.artist_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  artist_name TEXT NOT NULL,
  display_name TEXT, -- Public display name (can be different from real name)
  bio TEXT,
  profile_image_url TEXT,
  specializations TEXT[] DEFAULT '{}', -- Array of service categories they specialize in
  skill_level TEXT CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
  hourly_rate DECIMAL(10,2),
  commission_rate DECIMAL(5,2) DEFAULT 15.00, -- Percentage commission on bookings
  is_active BOOLEAN DEFAULT TRUE,
  is_available_today BOOLEAN DEFAULT TRUE,
  availability_notes TEXT,
  portfolio_urls TEXT[] DEFAULT '{}',
  social_media_links JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- =============================================
-- ARTIST SERVICE SPECIALIZATIONS TABLE
-- =============================================

-- Create artist-service specialization mapping
CREATE TABLE IF NOT EXISTS public.artist_service_specializations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  service_id UUID REFERENCES public.services(id) ON DELETE CASCADE,
  is_primary_service BOOLEAN DEFAULT FALSE, -- Is this their main specialty?
  skill_level TEXT CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
  custom_rate DECIMAL(10,2), -- Override default hourly rate for this service
  estimated_duration_modifier DECIMAL(3,2) DEFAULT 1.00, -- Multiplier for service duration (0.8 = 20% faster)
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(artist_id, service_id)
);

-- =============================================
-- ARTIST AVAILABILITY SCHEDULE TABLE
-- =============================================

-- Create artist availability schedule
CREATE TABLE IF NOT EXISTS public.artist_availability_schedule (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday, 6 = Saturday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN DEFAULT TRUE,
  break_start_time TIME, -- Optional break time
  break_end_time TIME,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ARTIST BOOKING ASSIGNMENTS TABLE
-- =============================================

-- Create artist booking assignments (links bookings to specific artists)
CREATE TABLE IF NOT EXISTS public.artist_booking_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  assignment_type TEXT CHECK (assignment_type IN ('manual', 'auto', 'preferred')) DEFAULT 'manual',
  status TEXT CHECK (status IN ('assigned', 'confirmed', 'completed', 'cancelled')) DEFAULT 'assigned',
  artist_notes TEXT,
  customer_rating INTEGER CHECK (customer_rating BETWEEN 1 AND 5),
  customer_feedback TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(booking_id, artist_id)
);

-- =============================================
-- VIEWS FOR EASY QUERYING
-- =============================================

-- Create view for artist availability with service specializations
CREATE OR REPLACE VIEW public.artist_availability_view AS
SELECT 
  ap.id as artist_id,
  ap.artist_name,
  ap.display_name,
  ap.is_active,
  ap.is_available_today,
  ap.hourly_rate,
  ap.skill_level as overall_skill_level,
  -- Aggregate service specializations
  COALESCE(
    json_agg(
      DISTINCT jsonb_build_object(
        'service_id', ass.service_id,
        'service_name', s.name,
        'service_category', s.category,
        'is_primary_service', ass.is_primary_service,
        'skill_level', ass.skill_level,
        'custom_rate', ass.custom_rate,
        'duration_modifier', ass.estimated_duration_modifier
      )
    ) FILTER (WHERE ass.service_id IS NOT NULL),
    '[]'::json
  ) as service_specializations,
  -- Aggregate availability schedule
  COALESCE(
    json_agg(
      DISTINCT jsonb_build_object(
        'day_of_week', aas.day_of_week,
        'start_time', aas.start_time,
        'end_time', aas.end_time,
        'is_available', aas.is_available,
        'break_start_time', aas.break_start_time,
        'break_end_time', aas.break_end_time
      )
    ) FILTER (WHERE aas.day_of_week IS NOT NULL),
    '[]'::json
  ) as weekly_schedule
FROM public.artist_profiles ap
LEFT JOIN public.artist_service_specializations ass ON ap.id = ass.artist_id
LEFT JOIN public.services s ON ass.service_id = s.id
LEFT JOIN public.artist_availability_schedule aas ON ap.id = aas.artist_id
WHERE ap.is_active = true
GROUP BY ap.id, ap.artist_name, ap.display_name, ap.is_active, ap.is_available_today, ap.hourly_rate, ap.skill_level;

-- Create view for services with available artists
CREATE OR REPLACE VIEW public.services_with_available_artists AS
SELECT 
  s.*,
  -- Aggregate available artists for this service
  COALESCE(
    json_agg(
      DISTINCT jsonb_build_object(
        'artist_id', ap.id,
        'artist_name', ap.artist_name,
        'display_name', ap.display_name,
        'skill_level', ass.skill_level,
        'custom_rate', ass.custom_rate,
        'is_primary_service', ass.is_primary_service,
        'is_available_today', ap.is_available_today
      )
    ) FILTER (WHERE ap.id IS NOT NULL AND ap.is_active = true),
    '[]'::json
  ) as available_artists,
  -- Count of available artists
  COUNT(DISTINCT CASE WHEN ap.is_active = true AND ap.is_available_today = true THEN ap.id END) as available_artist_count
FROM public.services s
LEFT JOIN public.artist_service_specializations ass ON s.id = ass.service_id
LEFT JOIN public.artist_profiles ap ON ass.artist_id = ap.id
WHERE s.status = 'active'
GROUP BY s.id, s.name, s.description, s.duration, s.price, s.color, s.category, 
         s.image_url, s.status, s.featured, s.created_at, s.updated_at;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_artist_profiles_user_id ON public.artist_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_artist_profiles_active ON public.artist_profiles(is_active, is_available_today);
CREATE INDEX IF NOT EXISTS idx_artist_service_specializations_artist ON public.artist_service_specializations(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_service_specializations_service ON public.artist_service_specializations(service_id);
CREATE INDEX IF NOT EXISTS idx_artist_availability_schedule_artist ON public.artist_availability_schedule(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_booking_assignments_booking ON public.artist_booking_assignments(booking_id);
CREATE INDEX IF NOT EXISTS idx_artist_booking_assignments_artist ON public.artist_booking_assignments(artist_id);

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Insert sample artist profiles (these would be created through the admin interface)
-- Note: These are placeholder UUIDs - in real implementation, these would reference actual user accounts

-- Sample Artist 1: Hair Braiding Specialist
INSERT INTO public.artist_profiles (
  id, artist_name, display_name, bio, specializations, skill_level, hourly_rate, is_active, is_available_today
) VALUES (
  '********-1111-1111-1111-********1111',
  'Sarah Johnson',
  'Sarah J',
  'Expert hair braiding artist with 5+ years experience in festival and event styling.',
  ARRAY['braiding', 'hair'],
  'expert',
  45.00,
  true,
  true
) ON CONFLICT (id) DO NOTHING;

-- Sample Artist 2: Face Painting & UV Art Specialist  
INSERT INTO public.artist_profiles (
  id, artist_name, display_name, bio, specializations, skill_level, hourly_rate, is_active, is_available_today
) VALUES (
  '********-2222-2222-2222-********2222',
  'Alex Chen',
  'Alex C',
  'Professional face painter specializing in UV reactive art and glitter designs.',
  ARRAY['painting', 'uv', 'glitter'],
  'advanced',
  40.00,
  true,
  true
) ON CONFLICT (id) DO NOTHING;

-- Sample Artist 3: Multi-Service Artist
INSERT INTO public.artist_profiles (
  id, artist_name, display_name, bio, specializations, skill_level, hourly_rate, is_active, is_available_today
) VALUES (
  '33333333-3333-3333-3333-333333333333',
  'Morgan Taylor',
  'Morgan T',
  'Versatile artist skilled in both hair braiding and face painting for events.',
  ARRAY['braiding', 'painting', 'glitter'],
  'intermediate',
  35.00,
  true,
  true
) ON CONFLICT (id) DO NOTHING;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.artist_profiles IS 'Artist profiles with specializations and availability';
COMMENT ON TABLE public.artist_service_specializations IS 'Mapping of artists to their service specializations';
COMMENT ON TABLE public.artist_availability_schedule IS 'Weekly availability schedule for each artist';
COMMENT ON TABLE public.artist_booking_assignments IS 'Assignment of specific artists to bookings';
COMMENT ON VIEW public.artist_availability_view IS 'Comprehensive view of artist availability and specializations';
COMMENT ON VIEW public.services_with_available_artists IS 'Services with their available artists for POS terminal';
