# Enhanced Admin Panel User Guide

## 🎯 Overview

The Ocean Soul Sparkles admin panel has been comprehensively enhanced with a professional settings management interface. This guide will help you navigate and use all the new features.

## 🔐 Accessing the Admin Panel

1. Navigate to `/admin/settings` in your browser
2. Ensure you're logged in with admin privileges
3. You'll see the new tabbed interface with 6 main sections

## 📋 Settings Sections

### 1. General Settings Tab
**Purpose**: Basic website information and contact details

**Key Fields**:
- **Site Name**: Your business name (appears in browser title)
- **Site Description**: Brief description for SEO and social media
- **Contact Email**: Primary contact email address
- **Contact Phone**: Business phone number
- **Business Hours**: Operating hours (displayed on website)

**Booking Settings**:
- **Booking Lead Time**: Minimum hours advance notice required
- **Max Booking Days Ahead**: How far in advance customers can book
- **Enable Online Bookings**: Toggle online booking functionality
- **Enable Online Payments**: Toggle payment processing

**Theme Settings**:
- **Primary Color**: Main brand color
- **Secondary Color**: Secondary brand color  
- **Accent Color**: Accent/highlight color
- Use color picker or enter hex codes directly

### 2. Payments & Square Tab
**Purpose**: Configure Square payment processing

**Key Fields**:
- **Enable Square Payments**: Master toggle for Square integration
- **Application ID**: Your Square application identifier
- **Access Token**: Square API access token
- **Environment**: Sandbox (testing) or Production
- **Location ID**: Your Square business location ID
- **Currency**: Payment currency (AUD, USD, EUR, GBP)
- **Webhook Signature Key**: For webhook verification (optional)

**Connection Testing**:
- Click "Test Square Connection" to verify API credentials
- Green checkmark = successful connection
- Red X = connection failed (check credentials)

### 3. Google Integration Tab
**Purpose**: Connect various Google services

**Analytics & Tracking**:
- **Google Analytics Measurement ID**: GA4 tracking ID (G-XXXXXXXXXX)
- **Tag Manager ID**: Google Tag Manager container ID
- **Search Console Verification**: Verification meta tag

**Business Profile**:
- **Business Profile ID**: Google My Business profile ID
- **My Business API Key**: API key for business profile access
- **Enable Business Integration**: Toggle GMB features
- **Enable Reviews Widget**: Show Google reviews on site

**Maps & Ads**:
- **Maps API Key**: For embedded maps and location features
- **Ads Customer ID**: Google Ads account ID
- **Conversion ID**: For conversion tracking

### 4. SEO & Analytics Tab
**Purpose**: Search engine optimization settings

**Meta Tags**:
- **SEO Meta Title**: Page title for search engines
- **Meta Description**: Page description for search results
- **Keywords**: Target keywords (comma-separated)
- **Canonical URL**: Preferred URL for this page

**Schema Markup**:
- **Enable Schema Markup**: Automatic structured data generation

### 5. Email Settings Tab
**Purpose**: Configure email notifications and SMTP

**Email Configuration**:
- **Enable Email Notifications**: Master toggle for all email features

**SMTP Settings**:
- **SMTP Host**: Mail server hostname (e.g., smtp.gmail.com)
- **SMTP Port**: Server port (usually 587 for TLS, 465 for SSL)
- **Username**: Email account username
- **Password**: Email account password
- **Encryption**: TLS or SSL
- **From Address**: Email address for outgoing messages
- **From Name**: Display name for outgoing messages

**Connection Testing**:
- Click "Test Email Connection" to verify SMTP settings
- Requires host and username to be filled

### 6. Advanced Settings Tab
**Purpose**: Advanced configuration options

**Social Media Links**:
- **Facebook URL**: Link to Facebook page
- **Instagram URL**: Link to Instagram profile  
- **Twitter URL**: Link to Twitter profile
- **LinkedIn URL**: Link to LinkedIn profile
- **YouTube URL**: Link to YouTube channel

**Asset URLs**:
- **Logo URL**: Link to website logo image
- **Favicon URL**: Link to favicon (browser tab icon)
- **Terms URL**: Link to terms of service page
- **Privacy URL**: Link to privacy policy page

**Custom Code**:
- **Custom CSS**: Additional styling code
- **Custom JavaScript**: Additional functionality code
- Use the code editor with syntax highlighting

**Security & Backup**:
- **Enable Auto Backup**: Automatic settings backup
- **Backup Frequency**: How often to backup (daily/weekly/monthly)
- **Enable Two-Factor Auth**: 2FA for admin accounts
- **Session Timeout**: Auto-logout time in minutes

## 🔧 Using Connection Testing

### Square API Testing
1. Fill in Application ID and Access Token
2. Select Environment (Sandbox for testing)
3. Click "Test Square Connection"
4. Look for status indicator:
   - ✅ Green = Connected successfully
   - ❌ Red = Connection failed
   - ⏳ Yellow = Testing in progress

### Email SMTP Testing  
1. Configure all SMTP settings
2. Click "Test Email Connection"
3. System will attempt to connect to mail server
4. Status shows connection result

### Google Analytics Testing
1. Enter Measurement ID in correct format
2. System validates ID format automatically
3. Format: G-XXXXXXXXXX for GA4

## 💾 Saving Settings

1. Make changes in any tab
2. Click "Save Settings" button at bottom
3. All settings across all tabs are saved together
4. Success message confirms save completed

## 🚨 Important Notes

- **Admin Access Required**: Only admin users can access these settings
- **Live Changes**: Settings take effect immediately after saving
- **Backup Recommended**: Export/backup settings before major changes
- **API Keys Security**: Keep API keys and passwords secure
- **Testing Environment**: Use sandbox/test modes before going live

## 🛠️ Troubleshooting

### Connection Tests Fail
- **Square**: Check Application ID and Access Token are correct
- **Email**: Verify SMTP host, port, and credentials
- **Google**: Ensure API keys have proper permissions

### Settings Don't Save
- Check you have admin privileges
- Verify no browser console errors
- Try refreshing page and saving again

### Interface Issues
- Clear browser cache
- Check JavaScript is enabled
- Try different browser

## 📞 Support

If you encounter issues with the enhanced admin panel:

1. Check the browser console for error messages
2. Verify all required fields are filled
3. Test connections individually
4. Contact technical support with specific error details

---

**Pro Tip**: Use the connection testing feature to verify integrations before going live. This prevents issues with payments, emails, and analytics tracking.
