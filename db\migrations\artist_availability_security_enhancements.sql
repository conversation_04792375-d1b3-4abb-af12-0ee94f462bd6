-- =============================================
-- ARTIST AVAILABILITY SECURITY ENHANCEMENTS
-- =============================================
-- This migration implements additional security features for the artist availability system
-- following the security analysis of commit e34aa0ba51bf9080196add1e40e0395786df137e

-- =============================================
-- RATE LIMITING TABLE FOR API ENDPOINTS
-- =============================================

-- Create table to track API usage for rate limiting
CREATE TABLE IF NOT EXISTS public.api_rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  endpoint_path TEXT NOT NULL,
  request_count INTEGER DEFAULT 1,
  window_start TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, endpoint_path, window_start)
);

-- Index for efficient rate limit queries
CREATE INDEX IF NOT EXISTS idx_api_rate_limits_user_endpoint_window 
ON public.api_rate_limits(user_id, endpoint_path, window_start);

-- Enable RLS on rate limits table
ALTER TABLE public.api_rate_limits ENABLE ROW LEVEL SECURITY;

-- Only allow users to see their own rate limit data
CREATE POLICY "Users can view own rate limits" ON public.api_rate_limits
  FOR SELECT USING (auth.uid() = user_id);

-- System can insert/update rate limit data
CREATE POLICY "System can manage rate limits" ON public.api_rate_limits
  FOR ALL USING ((select auth.role()) IN ('service_role', 'supabase_admin'));

-- =============================================
-- ARTIST ACTIVITY AUDIT LOG
-- =============================================

-- Create table to track artist availability changes for audit trail
CREATE TABLE IF NOT EXISTS public.artist_availability_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL CHECK (action_type IN ('create', 'update', 'delete', 'bulk_update')),
  table_name TEXT NOT NULL CHECK (table_name IN ('artist_profiles', 'artist_availability_schedule', 'artist_availability_exceptions')),
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  change_description TEXT,
  ip_address INET,
  user_agent TEXT,
  api_endpoint TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for efficient audit log queries
CREATE INDEX IF NOT EXISTS idx_artist_availability_audit_log_artist_id 
ON public.artist_availability_audit_log(artist_id);

CREATE INDEX IF NOT EXISTS idx_artist_availability_audit_log_user_id 
ON public.artist_availability_audit_log(user_id);

CREATE INDEX IF NOT EXISTS idx_artist_availability_audit_log_created_at 
ON public.artist_availability_audit_log(created_at);

CREATE INDEX IF NOT EXISTS idx_artist_availability_audit_log_action_type 
ON public.artist_availability_audit_log(action_type);

-- Enable RLS on audit log
ALTER TABLE public.artist_availability_audit_log ENABLE ROW LEVEL SECURITY;

-- Admin/Dev can read all audit logs
CREATE POLICY "Admin and dev can read all audit logs" ON public.artist_availability_audit_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles ur 
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- Artists can read their own audit logs
CREATE POLICY "Artists can read own audit logs" ON public.artist_availability_audit_log
  FOR SELECT USING (
    user_id = auth.uid() AND 
    EXISTS (
      SELECT 1 FROM public.user_roles ur 
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    )
  );

-- System can insert audit logs
CREATE POLICY "System can insert audit logs" ON public.artist_availability_audit_log
  FOR INSERT WITH CHECK ((select auth.role()) IN ('service_role', 'supabase_admin'));

-- =============================================
-- BUSINESS LOGIC VALIDATION FUNCTIONS
-- =============================================

-- Function to validate availability exception dates
CREATE OR REPLACE FUNCTION public.validate_availability_exception_date(
  exception_date DATE,
  artist_user_id UUID DEFAULT auth.uid()
) RETURNS BOOLEAN AS $$
DECLARE
  current_date_local DATE;
  max_future_date DATE;
BEGIN
  -- Get current date (assuming UTC, adjust timezone as needed)
  current_date_local := CURRENT_DATE;
  
  -- Set maximum future date (1 year from now)
  max_future_date := current_date_local + INTERVAL '1 year';
  
  -- Validate date is not in the past (allow today)
  IF exception_date < current_date_local THEN
    RAISE EXCEPTION 'Cannot set availability exceptions for past dates. Date: %, Current: %', 
      exception_date, current_date_local;
  END IF;
  
  -- Validate date is not too far in the future
  IF exception_date > max_future_date THEN
    RAISE EXCEPTION 'Cannot set availability exceptions more than 1 year in advance. Date: %, Max: %', 
      exception_date, max_future_date;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check rate limits for API endpoints
CREATE OR REPLACE FUNCTION public.check_api_rate_limit(
  endpoint_path TEXT,
  max_requests INTEGER DEFAULT 10,
  window_minutes INTEGER DEFAULT 1
) RETURNS BOOLEAN AS $$
DECLARE
  current_window_start TIMESTAMPTZ;
  request_count INTEGER;
  user_id_param UUID;
BEGIN
  -- Get current user
  user_id_param := auth.uid();
  
  IF user_id_param IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;
  
  -- Calculate current window start (truncate to minute boundary)
  current_window_start := date_trunc('minute', NOW()) - 
    (EXTRACT(minute FROM NOW())::INTEGER % window_minutes) * INTERVAL '1 minute';
  
  -- Get current request count for this window
  SELECT COALESCE(rl.request_count, 0) INTO request_count
  FROM public.api_rate_limits rl
  WHERE rl.user_id = user_id_param 
    AND rl.endpoint_path = endpoint_path
    AND rl.window_start = current_window_start;
  
  -- Check if rate limit exceeded
  IF request_count >= max_requests THEN
    RAISE EXCEPTION 'Rate limit exceeded for endpoint %. Max % requests per % minutes.', 
      endpoint_path, max_requests, window_minutes;
  END IF;
  
  -- Update or insert rate limit record
  INSERT INTO public.api_rate_limits (user_id, endpoint_path, request_count, window_start)
  VALUES (user_id_param, endpoint_path, 1, current_window_start)
  ON CONFLICT (user_id, endpoint_path, window_start)
  DO UPDATE SET 
    request_count = api_rate_limits.request_count + 1,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log artist availability changes
CREATE OR REPLACE FUNCTION public.log_artist_availability_change(
  p_artist_id UUID,
  p_action_type TEXT,
  p_table_name TEXT,
  p_record_id UUID,
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL,
  p_change_description TEXT DEFAULT NULL,
  p_api_endpoint TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  log_id UUID;
  current_user_id UUID;
BEGIN
  current_user_id := auth.uid();
  
  INSERT INTO public.artist_availability_audit_log (
    artist_id,
    user_id,
    action_type,
    table_name,
    record_id,
    old_values,
    new_values,
    change_description,
    api_endpoint
  ) VALUES (
    p_artist_id,
    current_user_id,
    p_action_type,
    p_table_name,
    p_record_id,
    p_old_values,
    p_new_values,
    p_change_description,
    p_api_endpoint
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.api_rate_limits IS 'Tracks API usage for rate limiting artist availability endpoints';
COMMENT ON TABLE public.artist_availability_audit_log IS 'Audit trail for all artist availability changes';
COMMENT ON FUNCTION public.validate_availability_exception_date IS 'Validates that availability exception dates are within acceptable range';
COMMENT ON FUNCTION public.check_api_rate_limit IS 'Enforces rate limits on API endpoints to prevent abuse';
COMMENT ON FUNCTION public.log_artist_availability_change IS 'Logs artist availability changes for audit trail';

-- =============================================
-- SECURITY ENHANCEMENTS COMPLETE
-- =============================================
-- This migration adds:
-- 1. Rate limiting infrastructure for API endpoints
-- 2. Comprehensive audit logging for availability changes
-- 3. Business logic validation functions
-- 4. Proper RLS policies for new tables
-- 
-- These enhancements provide:
-- - Protection against API abuse
-- - Complete audit trail for compliance
-- - Data validation to prevent invalid operations
-- - Secure access controls
