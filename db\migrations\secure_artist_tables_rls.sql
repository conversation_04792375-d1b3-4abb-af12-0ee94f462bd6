-- =============================================
-- ARTIST PROFILES RLS POLICIES
-- =============================================
-- Updated to support 5-tier role system (DEV, Admin, Artist, Braider, User)
-- and allow artists to access their own data

-- Enable RLS and define policies for artist_profiles
ALTER TABLE public.artist_profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate with correct role references
DROP POLICY IF EXISTS "Admin can read artist_profiles" ON public.artist_profiles;
DROP POLICY IF EXISTS "Admin can insert into artist_profiles" ON public.artist_profiles;
DROP POLICY IF EXISTS "Admin can update artist_profiles" ON public.artist_profiles;
DROP POLICY IF EXISTS "Admin can delete from artist_profiles" ON public.artist_profiles;

-- CRITICAL FIX: Admin/Dev access using public.user_roles table
CREATE POLICY "Admin and dev can read all artist_profiles" ON public.artist_profiles
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- CRITICAL FIX: Artists can read their own profile data
CREATE POLICY "Artists can read own profile" ON public.artist_profiles
  FOR SELECT USING (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    )
  );

-- Admin/Dev can insert new artist profiles
CREATE POLICY "Admin and dev can insert artist_profiles" ON public.artist_profiles
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- Admin/Dev can update all profiles, Artists can update their own
CREATE POLICY "Admin dev and artists can update artist_profiles" ON public.artist_profiles
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (user_id = auth.uid() AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (user_id = auth.uid() AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- Only Admin/Dev can delete artist profiles
CREATE POLICY "Admin and dev can delete artist_profiles" ON public.artist_profiles
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- =============================================
-- ARTIST AVAILABILITY SCHEDULE RLS POLICIES
-- =============================================

-- Enable RLS and define policies for artist_availability_schedule
ALTER TABLE public.artist_availability_schedule ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate with correct role references
DROP POLICY IF EXISTS "Admin can read artist_availability_schedule" ON public.artist_availability_schedule;
DROP POLICY IF EXISTS "Admin can insert into artist_availability_schedule" ON public.artist_availability_schedule;
DROP POLICY IF EXISTS "Admin can update artist_availability_schedule" ON public.artist_availability_schedule;
DROP POLICY IF EXISTS "Admin can delete from artist_availability_schedule" ON public.artist_availability_schedule;

-- Admin/Dev can read all schedules
CREATE POLICY "Admin and dev can read all artist_availability_schedule" ON public.artist_availability_schedule
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- Artists can read their own schedule
CREATE POLICY "Artists can read own availability_schedule" ON public.artist_availability_schedule
  FOR SELECT USING (
    artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    )
  );

-- Admin/Dev can insert schedules, Artists can insert their own
CREATE POLICY "Admin dev and artists can insert artist_availability_schedule" ON public.artist_availability_schedule
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- Admin/Dev can update all schedules, Artists can update their own
CREATE POLICY "Admin dev and artists can update artist_availability_schedule" ON public.artist_availability_schedule
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- Admin/Dev can delete schedules, Artists can delete their own
CREATE POLICY "Admin dev and artists can delete artist_availability_schedule" ON public.artist_availability_schedule
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- =============================================
-- ARTIST AVAILABILITY EXCEPTIONS RLS POLICIES
-- =============================================
-- CRITICAL FIX: This table is used by the new API endpoint
-- /api/artist/availability/set-unavailable-week.js

-- Enable RLS and define policies for artist_availability_exceptions
ALTER TABLE public.artist_availability_exceptions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate with correct role references
DROP POLICY IF EXISTS "Admin can read artist_availability_exceptions" ON public.artist_availability_exceptions;
DROP POLICY IF EXISTS "Admin can insert into artist_availability_exceptions" ON public.artist_availability_exceptions;
DROP POLICY IF EXISTS "Admin can update artist_availability_exceptions" ON public.artist_availability_exceptions;
DROP POLICY IF EXISTS "Admin can delete from artist_availability_exceptions" ON public.artist_availability_exceptions;

-- Admin/Dev can read all availability exceptions
CREATE POLICY "Admin and dev can read all artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- CRITICAL FIX: Artists can read their own availability exceptions
CREATE POLICY "Artists can read own availability_exceptions" ON public.artist_availability_exceptions
  FOR SELECT USING (
    artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    )
  );

-- CRITICAL FIX: Artists can insert their own availability exceptions
-- This is required for the set-unavailable-week API endpoint
CREATE POLICY "Admin dev and artists can insert artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- CRITICAL FIX: Artists can update their own availability exceptions
-- This is required for the upsert operation in the API endpoint
CREATE POLICY "Admin dev and artists can update artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- Artists can delete their own availability exceptions
CREATE POLICY "Admin dev and artists can delete artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    ) OR
    (artist_id IN (
      SELECT ap.id FROM public.artist_profiles ap
      WHERE ap.user_id = auth.uid()
    ) AND EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    ))
  );

-- =============================================
-- SERVICE PRICING TIERS RLS POLICIES
-- =============================================

-- Enable RLS and define policies for service_pricing_tiers
ALTER TABLE public.service_pricing_tiers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate with correct role references
DROP POLICY IF EXISTS "Admin can read service_pricing_tiers" ON public.service_pricing_tiers;
DROP POLICY IF EXISTS "Admin can insert into service_pricing_tiers" ON public.service_pricing_tiers;
DROP POLICY IF EXISTS "Admin can update service_pricing_tiers" ON public.service_pricing_tiers;
DROP POLICY IF EXISTS "Admin can delete from service_pricing_tiers" ON public.service_pricing_tiers;

-- Admin/Dev can read all service pricing tiers
CREATE POLICY "Admin and dev can read service_pricing_tiers" ON public.service_pricing_tiers
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- Artists/Braiders can read service pricing tiers (needed for POS operations)
CREATE POLICY "Staff can read service_pricing_tiers" ON public.service_pricing_tiers
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('artist', 'braider')
    )
  );

-- Only Admin/Dev can modify service pricing tiers
CREATE POLICY "Admin and dev can insert service_pricing_tiers" ON public.service_pricing_tiers
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

CREATE POLICY "Admin and dev can update service_pricing_tiers" ON public.service_pricing_tiers
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

CREATE POLICY "Admin and dev can delete service_pricing_tiers" ON public.service_pricing_tiers
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = (select auth.uid()) AND ur.role IN ('dev', 'admin')
    )
  );

-- =============================================
-- SECURITY AUDIT COMPLETE
-- =============================================
-- All RLS policies have been updated to:
-- 1. Use public.user_roles table instead of auth.users.role
-- 2. Support 5-tier role system (dev, admin, artist, braider, user)
-- 3. Allow artists to access their own data
-- 4. Maintain proper security boundaries
--
-- CRITICAL FIXES IMPLEMENTED:
-- - Artists can read their own artist_profiles (required for API)
-- - Artists can insert/update their own availability_exceptions (required for set-unavailable-week API)
-- - All policies use consistent role checking via public.user_roles
--
-- This enables the new /api/artist/availability/set-unavailable-week.js endpoint
-- while maintaining security isolation between artists.
