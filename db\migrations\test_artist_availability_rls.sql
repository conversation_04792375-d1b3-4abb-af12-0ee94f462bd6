-- =============================================
-- TEST SCRIPT FOR ARTIST AVAILABILITY RLS POLICIES
-- =============================================
-- This script tests the updated RLS policies to ensure the new API endpoint
-- /api/artist/availability/set-unavailable-week.js will work correctly

-- =============================================
-- TEST DATA SETUP
-- =============================================

-- Create test users (these would normally be created through Supabase Auth)
-- Note: In production, these would be actual auth.users records

-- Test Artist User
DO $$
DECLARE
  test_artist_user_id UUID := '11111111-1111-1111-1111-111111111111';
  test_artist_profile_id UUID := '*************-2222-2222-************';
  test_admin_user_id UUID := '*************-3333-3333-************';
BEGIN
  -- Insert test user role for artist
  INSERT INTO public.user_roles (id, role) 
  VALUES (test_artist_user_id, 'artist')
  ON CONFLICT (id) DO UPDATE SET role = 'artist';
  
  -- Insert test user role for admin
  INSERT INTO public.user_roles (id, role) 
  VALUES (test_admin_user_id, 'admin')
  ON CONFLICT (id) DO UPDATE SET role = 'admin';
  
  -- Insert test artist profile
  INSERT INTO public.artist_profiles (
    id, 
    user_id, 
    artist_name, 
    display_name, 
    bio, 
    specializations, 
    is_active, 
    is_available_today
  ) VALUES (
    test_artist_profile_id,
    test_artist_user_id,
    'Test Artist',
    'Test A',
    'Test artist for RLS policy validation',
    ARRAY['painting', 'glitter'],
    true,
    true
  ) ON CONFLICT (id) DO UPDATE SET
    user_id = test_artist_user_id,
    artist_name = 'Test Artist',
    display_name = 'Test A',
    bio = 'Test artist for RLS policy validation',
    specializations = ARRAY['painting', 'glitter'],
    is_active = true,
    is_available_today = true;
END $$;

-- =============================================
-- RLS POLICY TESTS
-- =============================================

-- Test 1: Verify artist can read their own profile
-- This simulates the query in set-unavailable-week.js line 47-51
DO $$
DECLARE
  test_artist_user_id UUID := '11111111-1111-1111-1111-111111111111';
  profile_count INTEGER;
BEGIN
  -- Set the current user context to the test artist
  PERFORM set_config('request.jwt.claims', json_build_object('sub', test_artist_user_id)::text, true);
  
  -- Test if artist can read their own profile
  SELECT COUNT(*) INTO profile_count
  FROM public.artist_profiles 
  WHERE user_id = test_artist_user_id;
  
  IF profile_count = 0 THEN
    RAISE EXCEPTION 'FAILED: Artist cannot read their own profile. RLS policy issue.';
  ELSE
    RAISE NOTICE 'PASSED: Artist can read their own profile (count: %)', profile_count;
  END IF;
END $$;

-- Test 2: Verify artist can insert availability exceptions
-- This simulates the upsert operation in set-unavailable-week.js line 74-80
DO $$
DECLARE
  test_artist_user_id UUID := '11111111-1111-1111-1111-111111111111';
  test_artist_profile_id UUID := '*************-2222-2222-************';
  exception_id UUID;
BEGIN
  -- Set the current user context to the test artist
  PERFORM set_config('request.jwt.claims', json_build_object('sub', test_artist_user_id)::text, true);
  
  -- Test if artist can insert availability exception
  INSERT INTO public.artist_availability_exceptions (
    artist_id,
    exception_date,
    exception_type,
    notes
  ) VALUES (
    test_artist_profile_id,
    CURRENT_DATE + 1,
    'Unavailable',
    'Test exception for RLS validation'
  ) RETURNING id INTO exception_id;
  
  IF exception_id IS NULL THEN
    RAISE EXCEPTION 'FAILED: Artist cannot insert availability exception. RLS policy issue.';
  ELSE
    RAISE NOTICE 'PASSED: Artist can insert availability exception (id: %)', exception_id;
  END IF;
END $$;

-- Test 3: Verify artist can update/upsert availability exceptions
-- This tests the upsert conflict resolution
DO $$
DECLARE
  test_artist_user_id UUID := '11111111-1111-1111-1111-111111111111';
  test_artist_profile_id UUID := '*************-2222-2222-************';
  exception_id UUID;
BEGIN
  -- Set the current user context to the test artist
  PERFORM set_config('request.jwt.claims', json_build_object('sub', test_artist_user_id)::text, true);
  
  -- Test upsert operation (should update existing record)
  INSERT INTO public.artist_availability_exceptions (
    artist_id,
    exception_date,
    exception_type,
    notes
  ) VALUES (
    test_artist_profile_id,
    CURRENT_DATE + 1,
    'Unavailable',
    'Updated test exception for RLS validation'
  ) 
  ON CONFLICT (artist_id, exception_date) 
  DO UPDATE SET 
    notes = EXCLUDED.notes,
    updated_at = NOW()
  RETURNING id INTO exception_id;
  
  IF exception_id IS NULL THEN
    RAISE EXCEPTION 'FAILED: Artist cannot upsert availability exception. RLS policy issue.';
  ELSE
    RAISE NOTICE 'PASSED: Artist can upsert availability exception (id: %)', exception_id;
  END IF;
END $$;

-- Test 4: Verify artist cannot access other artists' data
DO $$
DECLARE
  test_artist_user_id UUID := '11111111-1111-1111-1111-111111111111';
  other_artist_profile_id UUID := '*************-9999-9999-************';
  profile_count INTEGER;
  exception_inserted BOOLEAN := FALSE;
BEGIN
  -- Set the current user context to the test artist
  PERFORM set_config('request.jwt.claims', json_build_object('sub', test_artist_user_id)::text, true);
  
  -- Create another artist profile for testing isolation
  INSERT INTO public.user_roles (id, role) 
  VALUES ('*************-9999-9999-************', 'artist')
  ON CONFLICT (id) DO NOTHING;
  
  INSERT INTO public.artist_profiles (
    id, 
    user_id, 
    artist_name, 
    display_name
  ) VALUES (
    other_artist_profile_id,
    '*************-9999-9999-************',
    'Other Artist',
    'Other A'
  ) ON CONFLICT (id) DO NOTHING;
  
  -- Test if artist can see other artist's profile
  SELECT COUNT(*) INTO profile_count
  FROM public.artist_profiles 
  WHERE id = other_artist_profile_id;
  
  IF profile_count > 0 THEN
    RAISE EXCEPTION 'FAILED: Artist can see other artists profiles. Security breach!';
  ELSE
    RAISE NOTICE 'PASSED: Artist cannot see other artists profiles';
  END IF;
  
  -- Test if artist can insert exceptions for other artists
  BEGIN
    INSERT INTO public.artist_availability_exceptions (
      artist_id,
      exception_date,
      exception_type,
      notes
    ) VALUES (
      other_artist_profile_id,
      CURRENT_DATE + 2,
      'Unavailable',
      'Unauthorized exception'
    );
    exception_inserted := TRUE;
  EXCEPTION
    WHEN insufficient_privilege THEN
      RAISE NOTICE 'PASSED: Artist cannot insert exceptions for other artists';
    WHEN OTHERS THEN
      RAISE NOTICE 'PASSED: Artist cannot insert exceptions for other artists (error: %)', SQLERRM;
  END;
  
  IF exception_inserted THEN
    RAISE EXCEPTION 'FAILED: Artist can insert exceptions for other artists. Security breach!';
  END IF;
END $$;

-- Test 5: Verify admin can access all data
DO $$
DECLARE
  test_admin_user_id UUID := '*************-3333-3333-************';
  profile_count INTEGER;
  exception_count INTEGER;
BEGIN
  -- Set the current user context to the test admin
  PERFORM set_config('request.jwt.claims', json_build_object('sub', test_admin_user_id)::text, true);
  
  -- Test if admin can see all artist profiles
  SELECT COUNT(*) INTO profile_count
  FROM public.artist_profiles;
  
  IF profile_count = 0 THEN
    RAISE EXCEPTION 'FAILED: Admin cannot see artist profiles. RLS policy issue.';
  ELSE
    RAISE NOTICE 'PASSED: Admin can see all artist profiles (count: %)', profile_count;
  END IF;
  
  -- Test if admin can see all availability exceptions
  SELECT COUNT(*) INTO exception_count
  FROM public.artist_availability_exceptions;
  
  IF exception_count = 0 THEN
    RAISE NOTICE 'INFO: No availability exceptions found (this is normal for fresh test)';
  ELSE
    RAISE NOTICE 'PASSED: Admin can see all availability exceptions (count: %)', exception_count;
  END IF;
END $$;

-- =============================================
-- CLEANUP TEST DATA
-- =============================================

-- Clean up test data
DELETE FROM public.artist_availability_exceptions 
WHERE notes LIKE '%RLS validation%' OR notes LIKE '%test%';

DELETE FROM public.artist_profiles 
WHERE artist_name IN ('Test Artist', 'Other Artist');

DELETE FROM public.user_roles 
WHERE id IN (
  '11111111-1111-1111-1111-111111111111',
  '*************-3333-3333-************',
  '*************-9999-9999-************'
);

-- Reset user context
PERFORM set_config('request.jwt.claims', NULL, true);

-- =============================================
-- TEST SUMMARY
-- =============================================

RAISE NOTICE '==============================================';
RAISE NOTICE 'RLS POLICY TEST SUMMARY';
RAISE NOTICE '==============================================';
RAISE NOTICE 'If all tests passed, the new API endpoint should work correctly.';
RAISE NOTICE 'Key validations completed:';
RAISE NOTICE '1. Artists can read their own profiles';
RAISE NOTICE '2. Artists can insert their own availability exceptions';
RAISE NOTICE '3. Artists can upsert their own availability exceptions';
RAISE NOTICE '4. Artists cannot access other artists data';
RAISE NOTICE '5. Admins can access all data';
RAISE NOTICE '==============================================';
RAISE NOTICE 'The /api/artist/availability/set-unavailable-week.js endpoint';
RAISE NOTICE 'should now work without RLS policy violations.';
RAISE NOTICE '==============================================';
