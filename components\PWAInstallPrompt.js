/**
 * PWA Install Prompt Component
 * Ocean Soul Sparkles - Handles PWA installation prompts and status
 */

import { useState, useEffect } from 'react'
import { usePWA } from '@/lib/hooks/usePWA'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import styles from '@/styles/components/PWAInstallPrompt.module.css'

export default function PWAInstallPrompt({ 
  showOnMobile = true, 
  showOnDesktop = false,
  position = 'bottom-right',
  autoShow = true,
  onInstall = null,
  onDismiss = null 
}) {
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)
  const [isInstalling, setIsInstalling] = useState(false)
  
  const { 
    isInstallable, 
    isInstalled, 
    installApp, 
    isOnline,
    getDeviceCapabilities 
  } = usePWA()
  
  const { 
    isMobile, 
    isTablet, 
    isTouchDevice,
    hapticFeedback 
  } = useMobileOptimization()

  // Check if we should show the prompt
  useEffect(() => {
    if (!autoShow || isDismissed || isInstalled || !isOnline) {
      setIsVisible(false)
      return
    }

    // Check device type preferences
    const shouldShowOnDevice = (isMobile && showOnMobile) || (!isMobile && showOnDesktop)
    
    if (isInstallable && shouldShowOnDevice) {
      // Delay showing the prompt to avoid interrupting user flow
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, 3000) // Show after 3 seconds

      return () => clearTimeout(timer)
    }
  }, [isInstallable, isInstalled, isDismissed, isMobile, showOnMobile, showOnDesktop, autoShow, isOnline])

  const handleInstall = async () => {
    if (isTouchDevice) {
      hapticFeedback('medium')
    }

    setIsInstalling(true)
    
    try {
      const success = await installApp()
      
      if (success) {
        setIsVisible(false)
        if (onInstall) {
          onInstall()
        }
        if (isTouchDevice) {
          hapticFeedback('success')
        }
      }
    } catch (error) {
      console.error('Installation failed:', error)
      if (isTouchDevice) {
        hapticFeedback('error')
      }
    } finally {
      setIsInstalling(false)
    }
  }

  const handleDismiss = () => {
    if (isTouchDevice) {
      hapticFeedback('light')
    }
    
    setIsVisible(false)
    setIsDismissed(true)
    
    // Remember dismissal for this session
    sessionStorage.setItem('pwa-install-dismissed', 'true')
    
    if (onDismiss) {
      onDismiss()
    }
  }

  // Check if user previously dismissed
  useEffect(() => {
    const dismissed = sessionStorage.getItem('pwa-install-dismissed')
    if (dismissed) {
      setIsDismissed(true)
    }
  }, [])

  if (!isVisible || isInstalled) {
    return null
  }

  const capabilities = getDeviceCapabilities()

  return (
    <div className={`${styles.container} ${styles[position]}`}>
      <div className={styles.prompt}>
        {/* Close button */}
        <button 
          className={styles.closeButton}
          onClick={handleDismiss}
          aria-label="Dismiss install prompt"
        >
          ×
        </button>

        {/* Icon */}
        <div className={styles.icon}>
          <img 
            src="/images/icons/icon-96x96.png" 
            alt="Ocean Soul Sparkles" 
            className={styles.appIcon}
          />
        </div>

        {/* Content */}
        <div className={styles.content}>
          <h3 className={styles.title}>Install Ocean Soul Sparkles</h3>
          <p className={styles.description}>
            {isMobile 
              ? 'Add to your home screen for quick access and offline features!'
              : 'Install our app for a better experience with offline access!'
            }
          </p>

          {/* Features list */}
          <ul className={styles.features}>
            <li>
              <span className={styles.featureIcon}>📱</span>
              Quick access from home screen
            </li>
            <li>
              <span className={styles.featureIcon}>🔄</span>
              Works offline
            </li>
            <li>
              <span className={styles.featureIcon}>🔔</span>
              Push notifications
            </li>
            {capabilities.features.camera && (
              <li>
                <span className={styles.featureIcon}>📸</span>
                Camera integration
              </li>
            )}
          </ul>
        </div>

        {/* Actions */}
        <div className={styles.actions}>
          <button 
            className={styles.installButton}
            onClick={handleInstall}
            disabled={isInstalling}
          >
            {isInstalling ? (
              <>
                <span className={styles.spinner}></span>
                Installing...
              </>
            ) : (
              <>
                <span className={styles.installIcon}>⬇️</span>
                Install App
              </>
            )}
          </button>
          
          <button 
            className={styles.laterButton}
            onClick={handleDismiss}
          >
            Maybe Later
          </button>
        </div>

        {/* Platform-specific instructions */}
        {isMobile && (
          <div className={styles.instructions}>
            <p className={styles.instructionText}>
              {navigator.userAgent.includes('iPhone') || navigator.userAgent.includes('iPad')
                ? 'Tap the share button and "Add to Home Screen"'
                : 'Tap "Add to Home Screen" when prompted'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

// Status indicator component
export function PWAStatusIndicator({ showDetails = false }) {
  const { isInstalled, isOnline, updateAvailable, updateApp } = usePWA()
  const { isTouchDevice, hapticFeedback } = useMobileOptimization()

  if (!isInstalled) {
    return null
  }

  const handleUpdate = () => {
    if (isTouchDevice) {
      hapticFeedback('medium')
    }
    updateApp()
  }

  return (
    <div className={styles.statusIndicator}>
      <div className={`${styles.statusDot} ${isOnline ? styles.online : styles.offline}`}></div>
      
      {showDetails && (
        <div className={styles.statusDetails}>
          <span className={styles.statusText}>
            {isOnline ? 'Online' : 'Offline'}
          </span>
          
          {updateAvailable && (
            <button 
              className={styles.updateButton}
              onClick={handleUpdate}
            >
              Update Available
            </button>
          )}
        </div>
      )}
    </div>
  )
}

// Installation instructions component
export function PWAInstallInstructions({ platform = 'auto' }) {
  const { isMobile } = useMobileOptimization()
  
  const detectedPlatform = platform === 'auto' 
    ? (isMobile ? 'mobile' : 'desktop')
    : platform

  const instructions = {
    mobile: {
      ios: [
        'Open this page in Safari',
        'Tap the Share button at the bottom',
        'Scroll down and tap "Add to Home Screen"',
        'Tap "Add" to confirm'
      ],
      android: [
        'Open this page in Chrome',
        'Tap the menu (three dots)',
        'Tap "Add to Home screen"',
        'Tap "Add" to confirm'
      ]
    },
    desktop: {
      chrome: [
        'Click the install icon in the address bar',
        'Or go to Settings > Install Ocean Soul Sparkles',
        'Click "Install" to confirm'
      ],
      edge: [
        'Click the install icon in the address bar',
        'Or go to Settings > Apps > Install this site as an app',
        'Click "Install" to confirm'
      ]
    }
  }

  const isIOS = navigator.userAgent.includes('iPhone') || navigator.userAgent.includes('iPad')
  const currentInstructions = detectedPlatform === 'mobile' 
    ? (isIOS ? instructions.mobile.ios : instructions.mobile.android)
    : instructions.desktop.chrome

  return (
    <div className={styles.installInstructions}>
      <h4>How to Install:</h4>
      <ol>
        {currentInstructions.map((step, index) => (
          <li key={index}>{step}</li>
        ))}
      </ol>
    </div>
  )
}
