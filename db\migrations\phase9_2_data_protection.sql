-- Phase 9.2: Data Protection & Privacy Compliance Database Schema
-- Ocean Soul Sparkles - Security & Compliance Enhancement
-- Database schema for GDPR compliance, privacy management, and enhanced audit logging

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- GDPR COMPLIANCE TABLES
-- =====================================================

-- Data subject requests (GDPR Article 15-22)
CREATE TABLE IF NOT EXISTS public.gdpr_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
    request_type VARCHAR(50) NOT NULL CHECK (request_type IN (
        'access', 'portability', 'rectification', 'erasure', 'restriction', 'objection'
    )),
    request_status VARCHAR(30) NOT NULL CHECK (request_status IN (
        'pending', 'in_progress', 'completed', 'rejected', 'cancelled'
    )) DEFAULT 'pending',
    requester_email TEXT NOT NULL,
    requester_name TEXT,
    verification_method VARCHAR(50), -- 'email', 'identity_document', 'account_access'
    verification_status VARCHAR(30) DEFAULT 'pending' CHECK (verification_status IN (
        'pending', 'verified', 'failed', 'expired'
    )),
    verification_token TEXT UNIQUE,
    verification_expires_at TIMESTAMP WITH TIME ZONE,
    request_details JSONB DEFAULT '{}',
    response_data JSONB DEFAULT '{}',
    processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    processed_at TIMESTAMP WITH TIME ZONE,
    completion_deadline TIMESTAMP WITH TIME ZONE, -- 30 days from request
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Consent management for GDPR compliance
CREATE TABLE IF NOT EXISTS public.user_consents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL, -- 'marketing', 'analytics', 'cookies', 'data_processing'
    consent_given BOOLEAN NOT NULL,
    consent_version VARCHAR(20) NOT NULL, -- Version of privacy policy/terms
    consent_method VARCHAR(50), -- 'explicit', 'opt_in', 'pre_checked', 'implied'
    consent_source VARCHAR(100), -- 'registration', 'booking', 'newsletter', 'cookie_banner'
    ip_address INET,
    user_agent TEXT,
    consent_data JSONB DEFAULT '{}', -- Additional consent metadata
    withdrawn_at TIMESTAMP WITH TIME ZONE,
    withdrawal_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, consent_type, consent_version),
    UNIQUE(customer_id, consent_type, consent_version)
);

-- Privacy preferences management
CREATE TABLE IF NOT EXISTS public.privacy_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    preference_category VARCHAR(50) NOT NULL, -- 'communication', 'data_sharing', 'analytics', 'marketing'
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, preference_category, preference_key),
    UNIQUE(customer_id, preference_category, preference_key)
);

-- Cookie consent and tracking preferences
CREATE TABLE IF NOT EXISTS public.cookie_consents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id TEXT, -- For anonymous users
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    essential_cookies BOOLEAN DEFAULT TRUE, -- Always true, required for functionality
    functional_cookies BOOLEAN DEFAULT FALSE,
    analytics_cookies BOOLEAN DEFAULT FALSE,
    marketing_cookies BOOLEAN DEFAULT FALSE,
    consent_version VARCHAR(20) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    consent_banner_shown BOOLEAN DEFAULT FALSE,
    consent_given_at TIMESTAMP WITH TIME ZONE,
    last_updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE, -- Cookie consent expiry (typically 1 year)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED AUDIT LOGGING TABLES
-- =====================================================

-- Comprehensive data access audit trail
CREATE TABLE IF NOT EXISTS public.data_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    access_type VARCHAR(50) NOT NULL, -- 'read', 'create', 'update', 'delete', 'export'
    data_classification VARCHAR(50), -- 'public', 'internal', 'confidential', 'restricted'
    fields_accessed TEXT[], -- Array of field names accessed
    query_type VARCHAR(50), -- 'select', 'insert', 'update', 'delete'
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    api_endpoint TEXT,
    request_method VARCHAR(10),
    response_status INTEGER,
    processing_time_ms INTEGER,
    data_volume_bytes INTEGER,
    purpose VARCHAR(200), -- Business purpose for data access
    legal_basis VARCHAR(100), -- GDPR legal basis
    retention_period INTERVAL, -- How long this data should be kept
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data modification audit trail with before/after values
CREATE TABLE IF NOT EXISTS public.data_modification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB, -- Previous values (for UPDATE/DELETE)
    new_values JSONB, -- New values (for INSERT/UPDATE)
    changed_fields TEXT[], -- Array of fields that changed
    change_reason VARCHAR(500), -- Business reason for change
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Data retention and deletion tracking
CREATE TABLE IF NOT EXISTS public.data_retention_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    retention_policy VARCHAR(100) NOT NULL,
    retention_period INTERVAL NOT NULL,
    deletion_scheduled_at TIMESTAMP WITH TIME ZONE,
    deletion_executed_at TIMESTAMP WITH TIME ZONE,
    deletion_method VARCHAR(50), -- 'hard_delete', 'soft_delete', 'anonymization'
    deletion_reason VARCHAR(200),
    data_backup_location TEXT, -- Where data was backed up before deletion
    executed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED ENCRYPTION METADATA
-- =====================================================

-- Track encrypted fields and encryption methods
CREATE TABLE IF NOT EXISTS public.encryption_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    encryption_algorithm VARCHAR(50) NOT NULL, -- 'aes-256-gcm', 'aes-256-cbc'
    key_version VARCHAR(20) NOT NULL,
    encryption_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_rotation_date TIMESTAMP WITH TIME ZONE,
    next_rotation_due TIMESTAMP WITH TIME ZONE,
    data_classification VARCHAR(50) NOT NULL, -- 'pii', 'financial', 'health', 'biometric'
    compliance_requirements TEXT[], -- Array of compliance standards
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(table_name, field_name)
);

-- =====================================================
-- PRIVACY POLICY AND TERMS VERSIONING
-- =====================================================

-- Track privacy policy and terms versions
CREATE TABLE IF NOT EXISTS public.policy_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    policy_type VARCHAR(50) NOT NULL, -- 'privacy_policy', 'terms_of_service', 'cookie_policy'
    version VARCHAR(20) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT, -- Brief summary of changes
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT FALSE,
    requires_consent BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(policy_type, version)
);

-- Track user acceptance of policy versions
CREATE TABLE IF NOT EXISTS public.policy_acceptances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    policy_version_id UUID REFERENCES public.policy_versions(id) ON DELETE CASCADE,
    accepted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    acceptance_method VARCHAR(50), -- 'explicit', 'continued_use', 'registration'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, policy_version_id),
    UNIQUE(customer_id, policy_version_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- GDPR requests indexes
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_user_id ON public.gdpr_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_customer_id ON public.gdpr_requests(customer_id);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_status ON public.gdpr_requests(request_status);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_type ON public.gdpr_requests(request_type);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_deadline ON public.gdpr_requests(completion_deadline);
CREATE INDEX IF NOT EXISTS idx_gdpr_requests_created_at ON public.gdpr_requests(created_at);

-- User consents indexes
CREATE INDEX IF NOT EXISTS idx_user_consents_user_id ON public.user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_customer_id ON public.user_consents(customer_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_type ON public.user_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_user_consents_given ON public.user_consents(consent_given);
CREATE INDEX IF NOT EXISTS idx_user_consents_created_at ON public.user_consents(created_at);

-- Privacy preferences indexes
CREATE INDEX IF NOT EXISTS idx_privacy_preferences_user_id ON public.privacy_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_preferences_customer_id ON public.privacy_preferences(customer_id);
CREATE INDEX IF NOT EXISTS idx_privacy_preferences_category ON public.privacy_preferences(preference_category);
CREATE INDEX IF NOT EXISTS idx_privacy_preferences_active ON public.privacy_preferences(is_active) WHERE is_active = TRUE;

-- Cookie consents indexes
CREATE INDEX IF NOT EXISTS idx_cookie_consents_session_id ON public.cookie_consents(session_id);
CREATE INDEX IF NOT EXISTS idx_cookie_consents_user_id ON public.cookie_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_cookie_consents_customer_id ON public.cookie_consents(customer_id);
CREATE INDEX IF NOT EXISTS idx_cookie_consents_expires_at ON public.cookie_consents(expires_at);

-- Data access logs indexes
CREATE INDEX IF NOT EXISTS idx_data_access_logs_user_id ON public.data_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_table_name ON public.data_access_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_access_type ON public.data_access_logs(access_type);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_created_at ON public.data_access_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_classification ON public.data_access_logs(data_classification);

-- Data modification logs indexes
CREATE INDEX IF NOT EXISTS idx_data_modification_logs_user_id ON public.data_modification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_data_modification_logs_table_record ON public.data_modification_logs(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_data_modification_logs_operation ON public.data_modification_logs(operation);
CREATE INDEX IF NOT EXISTS idx_data_modification_logs_created_at ON public.data_modification_logs(created_at);

-- Data retention logs indexes
CREATE INDEX IF NOT EXISTS idx_data_retention_logs_table_name ON public.data_retention_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_data_retention_logs_scheduled ON public.data_retention_logs(deletion_scheduled_at);
CREATE INDEX IF NOT EXISTS idx_data_retention_logs_executed ON public.data_retention_logs(deletion_executed_at);

-- Encryption metadata indexes
CREATE INDEX IF NOT EXISTS idx_encryption_metadata_table_field ON public.encryption_metadata(table_name, field_name);
CREATE INDEX IF NOT EXISTS idx_encryption_metadata_classification ON public.encryption_metadata(data_classification);
CREATE INDEX IF NOT EXISTS idx_encryption_metadata_rotation_due ON public.encryption_metadata(next_rotation_due);

-- Policy versions indexes
CREATE INDEX IF NOT EXISTS idx_policy_versions_type ON public.policy_versions(policy_type);
CREATE INDEX IF NOT EXISTS idx_policy_versions_active ON public.policy_versions(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_policy_versions_effective_date ON public.policy_versions(effective_date);

-- Policy acceptances indexes
CREATE INDEX IF NOT EXISTS idx_policy_acceptances_user_id ON public.policy_acceptances(user_id);
CREATE INDEX IF NOT EXISTS idx_policy_acceptances_customer_id ON public.policy_acceptances(customer_id);
CREATE INDEX IF NOT EXISTS idx_policy_acceptances_policy_version ON public.policy_acceptances(policy_version_id);

-- Migration completed successfully
SELECT 'Phase 9.2 Data Protection & Privacy Compliance migration completed successfully' AS status;
