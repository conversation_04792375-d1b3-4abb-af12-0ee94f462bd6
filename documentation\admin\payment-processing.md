# Payment Processing System

This document provides detailed implementation instructions for the OceanSoulSparkles admin panel payment processing system.

## Overview

The payment processing system integrates PayPal and Square payment gateways to handle online payments for both product sales and service bookings. It includes features for tracking payment status, generating invoices, and managing financial records.

## Features

- Integration with PayPal and Square payment gateways
- Financial dashboard with revenue tracking
- Payment status monitoring (paid, pending, refunded)
- Transaction history with search and filter capabilities
- Invoicing system with customizable templates
- Automated payment reminders

## Database Schema

Create the following tables in Supabase:

```sql
-- Payments table
CREATE TABLE public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES public.orders(id),
  booking_id UUID REFERENCES public.bookings(id),
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'AUD',
  payment_method TEXT NOT NULL,
  payment_status TEXT NOT NULL,
  transaction_id TEXT,
  payment_date TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK (
    (order_id IS NOT NULL AND booking_id IS NULL) OR
    (order_id IS NULL AND booking_id IS NOT NULL)
  )
);

-- Invoices table
CREATE TABLE public.invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number TEXT NOT NULL UNIQUE,
  customer_id UUID REFERENCES public.customers(id) NOT NULL,
  order_id UUID REFERENCES public.orders(id),
  booking_id UUID REFERENCES public.bookings(id),
  amount DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2) DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'AUD',
  issue_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK (
    (order_id IS NOT NULL AND booking_id IS NULL) OR
    (order_id IS NULL AND booking_id IS NOT NULL)
  )
);

-- Enable RLS
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Staff can view all payments" ON public.payments
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can insert payments" ON public.payments
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

CREATE POLICY "Staff can update payments" ON public.payments
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'staff')
  );

-- Similar policies for invoices table
```

## Implementation Steps

### 1. Configure Payment Gateway Integration

#### PayPal Integration

1. Set up a PayPal Business account
2. Create a PayPal REST API app in the PayPal Developer Dashboard
3. Configure environment variables:

```
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_ENVIRONMENT=sandbox (or 'production' for live)
```

#### Square Integration

1. Create a Square Developer account
2. Create a Square application
3. Configure environment variables:

```
SQUARE_APPLICATION_ID=your-square-application-id
SQUARE_ACCESS_TOKEN=your-square-access-token
SQUARE_LOCATION_ID=your-square-location-id
SQUARE_ENVIRONMENT=sandbox (or 'production' for live)
```

### 2. Create Payment API Endpoints

Create the following API endpoints:

```javascript
// pages/api/payments/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getPayments(req, res)
    case 'POST':
      return createPayment(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get payments with optional filters
async function getPayments(req, res) {
  const { 
    start_date, 
    end_date, 
    payment_method, 
    payment_status, 
    order_id, 
    booking_id,
    limit,
    offset
  } = req.query

  let query = supabase
    .from('payments')
    .select('*')

  // Apply filters
  if (start_date) {
    query = query.gte('payment_date', start_date)
  }
  if (end_date) {
    query = query.lte('payment_date', end_date)
  }
  if (payment_method) {
    query = query.eq('payment_method', payment_method)
  }
  if (payment_status) {
    query = query.eq('payment_status', payment_status)
  }
  if (order_id) {
    query = query.eq('order_id', order_id)
  }
  if (booking_id) {
    query = query.eq('booking_id', booking_id)
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  // Order by payment date
  query = query.order('payment_date', { ascending: false })

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    payments: data,
    total: count
  })
}

// Create a new payment record
async function createPayment(req, res) {
  const { 
    order_id, 
    booking_id, 
    amount, 
    currency, 
    payment_method, 
    payment_status,
    transaction_id,
    payment_date,
    notes
  } = req.body

  // Validate required fields
  if (!amount || !payment_method || !payment_status) {
    return res.status(400).json({ error: 'Missing required fields' })
  }

  // Validate that either order_id or booking_id is provided, but not both
  if ((!order_id && !booking_id) || (order_id && booking_id)) {
    return res.status(400).json({ error: 'Either order_id or booking_id must be provided, but not both' })
  }

  // Create payment record
  const { data, error } = await supabase
    .from('payments')
    .insert([
      { 
        order_id, 
        booking_id, 
        amount, 
        currency: currency || 'AUD', 
        payment_method, 
        payment_status,
        transaction_id,
        payment_date: payment_date || new Date(),
        notes
      }
    ])
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  // Update order or booking status if payment is successful
  if (payment_status === 'completed' || payment_status === 'succeeded') {
    if (order_id) {
      await supabase
        .from('orders')
        .update({ payment_status: 'paid' })
        .eq('id', order_id)
    } else if (booking_id) {
      await supabase
        .from('bookings')
        .update({ status: 'confirmed' })
        .eq('id', booking_id)
    }
  }

  return res.status(201).json(data[0])
}
```

Create additional API endpoints for individual payment operations:

```javascript
// pages/api/payments/[id].js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin, isAdmin } from '@/lib/auth'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getPayment(id, res)
    case 'PUT':
      return updatePayment(id, req, res)
    case 'DELETE':
      // Only admins can delete payments
      const isAdminUser = await isAdmin()
      if (!isAdminUser) {
        return res.status(403).json({ error: 'Forbidden' })
      }
      return deletePayment(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single payment
async function getPayment(id, res) {
  const { data, error } = await supabase
    .from('payments')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data) {
    return res.status(404).json({ error: 'Payment not found' })
  }

  return res.status(200).json(data)
}

// Update a payment
async function updatePayment(id, req, res) {
  const { 
    payment_status,
    transaction_id,
    payment_date,
    notes
  } = req.body

  // Validate required fields
  if (!payment_status) {
    return res.status(400).json({ error: 'Payment status is required' })
  }

  // Update payment
  const { data, error } = await supabase
    .from('payments')
    .update({
      payment_status,
      transaction_id,
      payment_date,
      notes,
      updated_at: new Date()
    })
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Payment not found' })
  }

  // If payment status changed to completed/succeeded, update related records
  if (payment_status === 'completed' || payment_status === 'succeeded') {
    if (data[0].order_id) {
      await supabase
        .from('orders')
        .update({ payment_status: 'paid' })
        .eq('id', data[0].order_id)
    } else if (data[0].booking_id) {
      await supabase
        .from('bookings')
        .update({ status: 'confirmed' })
        .eq('id', data[0].booking_id)
    }
  }

  return res.status(200).json(data[0])
}

// Delete a payment
async function deletePayment(id, res) {
  const { data, error } = await supabase
    .from('payments')
    .delete()
    .eq('id', id)
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  if (!data || data.length === 0) {
    return res.status(404).json({ error: 'Payment not found' })
  }

  return res.status(200).json({ message: 'Payment deleted successfully' })
}
```

### 3. Create Invoice API Endpoints

Create API endpoints for invoice management:

```javascript
// pages/api/invoices/index.js
import { supabase } from '@/lib/supabase'
import { isAuthenticated, isStaffOrAdmin } from '@/lib/auth'
import { generateInvoiceNumber } from '@/lib/invoice'

export default async function handler(req, res) {
  // Check authentication
  const authenticated = await isStaffOrAdmin()
  if (!authenticated) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getInvoices(req, res)
    case 'POST':
      return createInvoice(req, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get invoices with optional filters
async function getInvoices(req, res) {
  const { 
    customer_id, 
    status, 
    start_date, 
    end_date,
    limit,
    offset
  } = req.query

  let query = supabase
    .from('invoices')
    .select(`
      *,
      customers:customer_id (name, email)
    `)

  // Apply filters
  if (customer_id) {
    query = query.eq('customer_id', customer_id)
  }
  if (status) {
    query = query.eq('status', status)
  }
  if (start_date) {
    query = query.gte('issue_date', start_date)
  }
  if (end_date) {
    query = query.lte('issue_date', end_date)
  }

  // Apply pagination
  if (limit) {
    query = query.limit(limit)
  }
  if (offset) {
    query = query.offset(offset)
  }

  // Order by issue date
  query = query.order('issue_date', { ascending: false })

  const { data, error, count } = await query.select('*', { count: 'exact' })

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(200).json({
    invoices: data,
    total: count
  })
}

// Create a new invoice
async function createInvoice(req, res) {
  const { 
    customer_id, 
    order_id, 
    booking_id, 
    amount, 
    tax_amount, 
    currency, 
    issue_date, 
    due_date, 
    status, 
    notes 
  } = req.body

  // Validate required fields
  if (!customer_id || !amount || !issue_date || !due_date || !status) {
    return res.status(400).json({ error: 'Missing required fields' })
  }

  // Validate that either order_id or booking_id is provided, but not both
  if ((!order_id && !booking_id) || (order_id && booking_id)) {
    return res.status(400).json({ error: 'Either order_id or booking_id must be provided, but not both' })
  }

  // Generate invoice number
  const invoiceNumber = await generateInvoiceNumber()

  // Create invoice
  const { data, error } = await supabase
    .from('invoices')
    .insert([
      { 
        invoice_number: invoiceNumber,
        customer_id, 
        order_id, 
        booking_id, 
        amount, 
        tax_amount: tax_amount || 0, 
        currency: currency || 'AUD', 
        issue_date, 
        due_date, 
        status, 
        notes 
      }
    ])
    .select()

  if (error) {
    return res.status(500).json({ error: error.message })
  }

  return res.status(201).json(data[0])
}
```

### 4. Create Financial Dashboard Component

Create a component for the financial dashboard:

```javascript
// components/admin/FinancialDashboard.js
import { useState, useEffect } from 'react'
import styles from '@/styles/admin/FinancialDashboard.module.css'

export default function FinancialDashboard() {
  const [summary, setSummary] = useState({
    totalRevenue: 0,
    pendingPayments: 0,
    completedPayments: 0,
    refundedPayments: 0
  })
  const [recentTransactions, setRecentTransactions] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [dateRange, setDateRange] = useState('month') // 'week', 'month', 'year'

  // Fetch financial data
  useEffect(() => {
    const fetchFinancialData = async () => {
      setLoading(true)
      setError(null)

      try {
        // Calculate date range
        const endDate = new Date()
        let startDate = new Date()
        
        if (dateRange === 'week') {
          startDate.setDate(startDate.getDate() - 7)
        } else if (dateRange === 'month') {
          startDate.setMonth(startDate.getMonth() - 1)
        } else if (dateRange === 'year') {
          startDate.setFullYear(startDate.getFullYear() - 1)
        }

        // Fetch payment summary
        const summaryResponse = await fetch(
          `/api/payments/summary?start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`
        )

        if (!summaryResponse.ok) {
          throw new Error('Failed to fetch payment summary')
        }

        const summaryData = await summaryResponse.json()
        setSummary(summaryData)

        // Fetch recent transactions
        const transactionsResponse = await fetch(
          `/api/payments?limit=10&offset=0&start_date=${startDate.toISOString()}&end_date=${endDate.toISOString()}`
        )

        if (!transactionsResponse.ok) {
          throw new Error('Failed to fetch recent transactions')
        }

        const transactionsData = await transactionsResponse.json()
        setRecentTransactions(transactionsData.payments)
      } catch (error) {
        console.error('Error fetching financial data:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchFinancialData()
  }, [dateRange])

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range)
  }

  if (loading) {
    return <div className={styles.loading}>Loading financial data...</div>
  }

  if (error) {
    return <div className={styles.error}>{error}</div>
  }

  return (
    <div className={styles.dashboardContainer}>
      <div className={styles.dateRangeSelector}>
        <button
          className={`${styles.rangeButton} ${dateRange === 'week' ? styles.active : ''}`}
          onClick={() => handleDateRangeChange('week')}
        >
          Last 7 Days
        </button>
        <button
          className={`${styles.rangeButton} ${dateRange === 'month' ? styles.active : ''}`}
          onClick={() => handleDateRangeChange('month')}
        >
          Last 30 Days
        </button>
        <button
          className={`${styles.rangeButton} ${dateRange === 'year' ? styles.active : ''}`}
          onClick={() => handleDateRangeChange('year')}
        >
          Last Year
        </button>
      </div>

      <div className={styles.summaryCards}>
        <div className={styles.summaryCard}>
          <h3>Total Revenue</h3>
          <p className={styles.amount}>${summary.totalRevenue.toFixed(2)}</p>
        </div>
        <div className={styles.summaryCard}>
          <h3>Pending</h3>
          <p className={styles.amount}>${summary.pendingPayments.toFixed(2)}</p>
        </div>
        <div className={styles.summaryCard}>
          <h3>Completed</h3>
          <p className={styles.amount}>${summary.completedPayments.toFixed(2)}</p>
        </div>
        <div className={styles.summaryCard}>
          <h3>Refunded</h3>
          <p className={styles.amount}>${summary.refundedPayments.toFixed(2)}</p>
        </div>
      </div>

      <div className={styles.recentTransactions}>
        <h3>Recent Transactions</h3>
        <table className={styles.transactionsTable}>
          <thead>
            <tr>
              <th>Date</th>
              <th>Amount</th>
              <th>Method</th>
              <th>Status</th>
              <th>Type</th>
            </tr>
          </thead>
          <tbody>
            {recentTransactions.map((transaction) => (
              <tr key={transaction.id}>
                <td>{new Date(transaction.payment_date).toLocaleDateString()}</td>
                <td>${transaction.amount.toFixed(2)}</td>
                <td>{transaction.payment_method}</td>
                <td>
                  <span className={styles[transaction.payment_status]}>
                    {transaction.payment_status}
                  </span>
                </td>
                <td>{transaction.order_id ? 'Order' : 'Booking'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
```

## Testing

1. Test payment gateway integration
2. Test payment creation and processing
3. Test invoice generation
4. Test financial dashboard
5. Test payment status updates
6. Test integration with orders and bookings

## Security Considerations

- Use HTTPS for all payment-related communications
- Implement proper authentication and authorization
- Never store sensitive payment information (credit card numbers, etc.)
- Follow PCI DSS compliance guidelines
- Implement proper error handling for payment failures
- Log all payment transactions for audit purposes
