/**
 * Camera Capture Component for Ocean Soul Sparkles
 * Handles photo capture, compression, and cloud storage integration
 */

import { useState, useRef, useEffect, useCallback } from 'react'
import { usePWA } from '@/lib/hooks/usePWA'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { offlineStorage } from '@/lib/offline-storage'
import { toast } from 'react-toastify'
import styles from '@/styles/components/CameraCapture.module.css'

export default function CameraCapture({
  onCapture,
  onClose,
  type = 'portfolio', // 'before', 'after', 'portfolio', 'receipt'
  bookingId = null,
  maxWidth = 1920,
  maxHeight = 1080,
  quality = 0.8,
  enableFlash = true,
  enableFrontCamera = true
}) {
  const [isActive, setIsActive] = useState(false)
  const [stream, setStream] = useState(null)
  const [capturedImage, setCapturedImage] = useState(null)
  const [isCapturing, setIsCapturing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [facingMode, setFacingMode] = useState('environment') // 'user' or 'environment'
  const [error, setError] = useState(null)
  const [flashEnabled, setFlashEnabled] = useState(false)

  const videoRef = useRef(null)
  const canvasRef = useRef(null)
  const fileInputRef = useRef(null)

  const { isFeatureSupported, isOnline, cacheForOffline } = usePWA()
  const { isMobile, hapticFeedback, isTouchDevice } = useMobileOptimization()

  // Check camera support
  const cameraSupported = isFeatureSupported('camera')

  // Initialize camera
  const initializeCamera = useCallback(async () => {
    if (!cameraSupported) {
      setError('Camera not supported on this device')
      return
    }

    try {
      setError(null)
      
      const constraints = {
        video: {
          facingMode: facingMode,
          width: { ideal: maxWidth },
          height: { ideal: maxHeight }
        }
      }

      // Add flash support if available
      if (flashEnabled && 'torch' in navigator.mediaDevices.getSupportedConstraints()) {
        constraints.video.torch = true
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      setStream(mediaStream)
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
      }
      
      setIsActive(true)
    } catch (err) {
      console.error('Camera initialization failed:', err)
      setError('Failed to access camera. Please check permissions.')
    }
  }, [cameraSupported, facingMode, maxWidth, maxHeight, flashEnabled])

  // Stop camera
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    setIsActive(false)
  }, [stream])

  // Capture photo
  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current) return

    if (isTouchDevice) {
      hapticFeedback('medium')
    }

    setIsCapturing(true)

    try {
      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      // Set canvas dimensions
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Convert to blob with compression
      const blob = await new Promise(resolve => {
        canvas.toBlob(resolve, 'image/jpeg', quality)
      })

      // Create image URL for preview
      const imageUrl = URL.createObjectURL(blob)
      setCapturedImage({
        blob,
        url: imageUrl,
        timestamp: new Date().toISOString(),
        type,
        bookingId
      })

      if (isTouchDevice) {
        hapticFeedback('success')
      }

    } catch (err) {
      console.error('Photo capture failed:', err)
      setError('Failed to capture photo')
      
      if (isTouchDevice) {
        hapticFeedback('error')
      }
    } finally {
      setIsCapturing(false)
    }
  }, [quality, type, bookingId, isTouchDevice, hapticFeedback])

  // Save photo
  const savePhoto = useCallback(async () => {
    if (!capturedImage) return

    setIsSaving(true)

    try {
      // Convert blob to base64 for storage
      const base64 = await blobToBase64(capturedImage.blob)
      
      const photoData = {
        ...capturedImage,
        data: base64,
        size: capturedImage.blob.size,
        compressed: true,
        deviceInfo: {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      }

      if (isOnline) {
        // Upload to cloud storage
        await uploadToCloud(photoData)
      } else {
        // Store offline
        await offlineStorage.saveOfflinePhoto(photoData)
        toast.info('Photo saved offline. Will sync when online.')
      }

      if (onCapture) {
        onCapture(photoData)
      }

      // Clean up
      URL.revokeObjectURL(capturedImage.url)
      setCapturedImage(null)
      
      if (isTouchDevice) {
        hapticFeedback('success')
      }

      toast.success('Photo saved successfully!')

    } catch (err) {
      console.error('Failed to save photo:', err)
      setError('Failed to save photo')
      
      if (isTouchDevice) {
        hapticFeedback('error')
      }
    } finally {
      setIsSaving(false)
    }
  }, [capturedImage, isOnline, onCapture, isTouchDevice, hapticFeedback])

  // Upload to cloud storage
  const uploadToCloud = async (photoData) => {
    const formData = new FormData()
    formData.append('photo', photoData.blob)
    formData.append('type', photoData.type)
    formData.append('bookingId', photoData.bookingId || '')
    formData.append('timestamp', photoData.timestamp)

    const response = await fetch('/api/admin/photos/upload', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error('Upload failed')
    }

    return response.json()
  }

  // Handle file input (fallback)
  const handleFileInput = (event) => {
    const file = event.target.files[0]
    if (file) {
      const imageUrl = URL.createObjectURL(file)
      setCapturedImage({
        blob: file,
        url: imageUrl,
        timestamp: new Date().toISOString(),
        type,
        bookingId
      })
    }
  }

  // Switch camera
  const switchCamera = () => {
    if (enableFrontCamera) {
      setFacingMode(prev => prev === 'user' ? 'environment' : 'user')
    }
  }

  // Toggle flash
  const toggleFlash = () => {
    if (enableFlash) {
      setFlashEnabled(prev => !prev)
    }
  }

  // Retake photo
  const retakePhoto = () => {
    if (capturedImage) {
      URL.revokeObjectURL(capturedImage.url)
      setCapturedImage(null)
    }
  }

  // Initialize camera on mount
  useEffect(() => {
    if (cameraSupported) {
      initializeCamera()
    }

    return () => {
      stopCamera()
    }
  }, [initializeCamera, stopCamera, cameraSupported])

  // Update camera when facing mode changes
  useEffect(() => {
    if (isActive) {
      stopCamera()
      setTimeout(initializeCamera, 100)
    }
  }, [facingMode, flashEnabled, isActive, stopCamera, initializeCamera])

  // Utility function
  const blobToBase64 = (blob) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  if (!cameraSupported) {
    return (
      <div className={styles.container}>
        <div className={styles.fallback}>
          <h3>Camera Not Available</h3>
          <p>Your device doesn't support camera access. You can still upload photos:</p>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInput}
            style={{ display: 'none' }}
          />
          
          <button 
            className={styles.uploadButton}
            onClick={() => fileInputRef.current?.click()}
          >
            Choose Photo
          </button>
          
          <button 
            className={styles.cancelButton}
            onClick={onClose}
          >
            Cancel
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      {error && (
        <div className={styles.error}>
          <p>{error}</p>
          <button onClick={() => setError(null)}>Dismiss</button>
        </div>
      )}

      {capturedImage ? (
        // Photo preview
        <div className={styles.preview}>
          <img 
            src={capturedImage.url} 
            alt="Captured photo" 
            className={styles.capturedImage}
          />
          
          <div className={styles.previewActions}>
            <button 
              className={styles.retakeButton}
              onClick={retakePhoto}
            >
              Retake
            </button>
            
            <button 
              className={styles.saveButton}
              onClick={savePhoto}
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Photo'}
            </button>
          </div>
        </div>
      ) : (
        // Camera view
        <div className={styles.camera}>
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className={styles.video}
          />
          
          <canvas
            ref={canvasRef}
            style={{ display: 'none' }}
          />
          
          {/* Camera controls */}
          <div className={styles.controls}>
            <div className={styles.topControls}>
              {enableFlash && (
                <button 
                  className={`${styles.controlButton} ${flashEnabled ? styles.active : ''}`}
                  onClick={toggleFlash}
                >
                  ⚡
                </button>
              )}
              
              <button 
                className={styles.closeButton}
                onClick={onClose}
              >
                ✕
              </button>
            </div>
            
            <div className={styles.bottomControls}>
              {enableFrontCamera && (
                <button 
                  className={styles.switchButton}
                  onClick={switchCamera}
                >
                  🔄
                </button>
              )}
              
              <button 
                className={styles.captureButton}
                onClick={capturePhoto}
                disabled={isCapturing}
              >
                {isCapturing ? '📸' : '⚪'}
              </button>
              
              <div className={styles.spacer}></div>
            </div>
          </div>
          
          {/* Type indicator */}
          <div className={styles.typeIndicator}>
            {type.charAt(0).toUpperCase() + type.slice(1)} Photo
          </div>
        </div>
      )}
    </div>
  )
}
