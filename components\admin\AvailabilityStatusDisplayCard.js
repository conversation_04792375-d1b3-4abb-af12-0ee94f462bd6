import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/AvailabilityStatusDisplayCard.module.css';

export default function AvailabilityStatusDisplayCard({ artistProfile, onAvailabilityUpdate }) {
  const { user } = useAuth(); // For API calls
  const [currentStatusLoading, setCurrentStatusLoading] = useState(false);

  // Directly use props for display, assuming they are passed correctly
  // artistProfile.is_available_today
  // artistProfile.todays_bookings_count
  // artistProfile.max_daily_bookings

  const handleAvailabilityToggle = async () => {
    if (!artistProfile || typeof artistProfile.is_available_today === 'undefined') {
      toast.error('Profile data is not available to toggle status.');
      return;
    }

    setCurrentStatusLoading(true);
    const newStatus = !artistProfile.is_available_today;

    try {
      const response = await fetch('/api/artist/availability', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.access_token}`, // Assuming access_token is available on user object
        },
        body: JSON.stringify({ is_available_today: newStatus }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message || `Availability updated to ${newStatus ? 'Available' : 'Unavailable'}.`);
        if (onAvailabilityUpdate) {
          onAvailabilityUpdate(); // Trigger dashboard data refresh
        }
      } else {
        throw new Error(data.error || data.message || 'Failed to update availability.');
      }
    } catch (error) {
      console.error('Error toggling availability:', error);
      toast.error(error.message || 'An error occurred while updating availability.');
    } finally {
      setCurrentStatusLoading(false);
    }
  };

  if (!artistProfile) {
    return <div className={styles.card}><p>Loading status...</p></div>;
  }

  const isAvailable = artistProfile.is_available_today;
  const todaysBookings = artistProfile.todays_bookings_count ?? 0; // Fallback to 0 if undefined
  const maxBookings = artistProfile.max_daily_bookings;

  return (
    <div className={styles.card}>
      <h4 className={styles.title}>Today's Status</h4>
      <p className={styles.statusText}>
        Currently: <span className={isAvailable ? styles.available : styles.unavailable}>
          {isAvailable ? 'Available' : 'Unavailable'}
        </span>
      </p>

      {typeof maxBookings === 'number' && maxBookings > 0 && (
        <p className={styles.bookingsInfo}>
          Bookings Today: {todaysBookings} / {maxBookings}
        </p>
      )}
       {typeof maxBookings === 'number' && maxBookings === 0 && (
        <p className={styles.bookingsInfo}>
          Not accepting bookings today (max set to 0).
        </p>
      )}


      <button
        onClick={handleAvailabilityToggle}
        disabled={currentStatusLoading}
        className={`${styles.toggleButton} ${isAvailable ? styles.setUnavailableButton : styles.setAvailableButton}`}
      >
        {currentStatusLoading ? (
          <>
            Updating...
            <span className={styles.loadingSpinner}></span>
          </>
        ) : (
          isAvailable ? 'Set to Unavailable' : 'Set to Available'
        )}
      </button>
    </div>
  );
}
