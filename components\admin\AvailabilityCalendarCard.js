import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/AvailabilityCalendarCard.module.css'

export default function AvailabilityCalendarCard({ profile, onAvailabilityUpdate }) {
  const { user } = useAuth()
  const [availabilityStatus, setAvailabilityStatus] = useState(profile?.is_available_today ? 'available' : 'unavailable')
  const [loading, setLoading] = useState(false)
  const [todaysSchedule, setTodaysSchedule] = useState([])
  const [weeklySchedule, setWeeklySchedule] = useState([])

  useEffect(() => {
    fetchTodaysSchedule()
    fetchWeeklySchedule()
  }, [])

  const fetchTodaysSchedule = async () => {
    try {
      const response = await fetch('/api/artist/todays-schedule', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTodaysSchedule(data.schedule || [])
      }
    } catch (error) {
      console.error('Error fetching today\'s schedule:', error)
    }
  }

  const fetchWeeklySchedule = async () => {
    try {
      const response = await fetch('/api/artist/weekly-schedule', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setWeeklySchedule(data.schedule || [])
      }
    } catch (error) {
      console.error('Error fetching weekly schedule:', error)
    }
  }

  const handleAvailabilityToggle = async (newStatus) => {
    try {
      setLoading(true)

      const response = await fetch('/api/artist/availability', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_available_today: newStatus === 'available'
        })
      })

      if (response.ok) {
        setAvailabilityStatus(newStatus)
        onAvailabilityUpdate?.(newStatus)
        toast.success(`Availability updated to ${newStatus}`)
      } else {
        throw new Error('Failed to update availability')
      }
    } catch (error) {
      console.error('Error updating availability:', error)
      toast.error('Failed to update availability')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#10b981'
      case 'busy': return '#f59e0b'
      case 'unavailable': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const formatTime = (timeString) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getDayName = (date) => {
    return new Date(date).toLocaleDateString('en-US', { weekday: 'short' })
  }

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3>Availability & Schedule</h3>
        <div className={styles.statusIndicator}>
          <div
            className={styles.statusDot}
            style={{ backgroundColor: getStatusColor(availabilityStatus) }}
          />
          <span className={styles.statusText}>
            {availabilityStatus.charAt(0).toUpperCase() + availabilityStatus.slice(1)}
          </span>
        </div>
      </div>

      <div className={styles.content}>
        {/* Availability Toggle */}
        <div className={styles.availabilitySection}>
          <h4>Today's Availability</h4>
          <div className={styles.toggleGroup}>
            <button
              onClick={() => handleAvailabilityToggle('available')}
              disabled={loading}
              className={`${styles.toggleButton} ${availabilityStatus === 'available' ? styles.active : ''}`}
            >
              Available
            </button>
            <button
              onClick={() => handleAvailabilityToggle('unavailable')}
              disabled={loading}
              className={`${styles.toggleButton} ${availabilityStatus === 'unavailable' ? styles.active : ''}`}
            >
              Unavailable
            </button>
          </div>
        </div>

        {/* Today's Schedule */}
        <div className={styles.scheduleSection}>
          <h4>Today's Schedule</h4>
          {todaysSchedule.length > 0 ? (
            <div className={styles.scheduleList}>
              {todaysSchedule.map((booking, index) => (
                <div key={index} className={styles.scheduleItem}>
                  <div className={styles.timeSlot}>
                    {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                  </div>
                  <div className={styles.bookingDetails}>
                    <span className={styles.serviceName}>{booking.service_name}</span>
                    <span className={styles.customerName}>{booking.customer_name}</span>
                  </div>
                  <div
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(booking.status) }}
                  >
                    {booking.status}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>
              <span>No bookings scheduled for today</span>
            </div>
          )}
        </div>

        {/* Weekly Overview */}
        <div className={styles.weeklySection}>
          <h4>This Week</h4>
          <div className={styles.weeklyGrid}>
            {weeklySchedule.map((day, index) => (
              <div key={index} className={styles.dayCard}>
                <div className={styles.dayHeader}>
                  <span className={styles.dayName}>{getDayName(day.date)}</span>
                  <span className={styles.dayDate}>{new Date(day.date).getDate()}</span>
                </div>
                <div className={styles.dayBookings}>
                  {day.bookings?.length > 0 ? (
                    <span className={styles.bookingCount}>
                      {day.bookings.length} booking{day.bookings.length !== 1 ? 's' : ''}
                    </span>
                  ) : (
                    <span className={styles.noBookings}>Free</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className={styles.statsSection}>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>Daily Limit</span>
            <span className={styles.statValue}>
              {todaysSchedule.length} / {profile?.max_daily_bookings || 8}
            </span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statLabel}>Buffer Time</span>
            <span className={styles.statValue}>
              {profile?.booking_buffer_time || 15} min
            </span>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={styles.actionsSection}>
          <button
            className={styles.actionButton}
            onClick={() => window.open('/admin/bookings', '_blank')}
          >
            View All Bookings
          </button>
          <button
            className={styles.actionButton}
            onClick={fetchTodaysSchedule}
          >
            Refresh Schedule
          </button>
        </div>
      </div>
    </div>
  )
}
