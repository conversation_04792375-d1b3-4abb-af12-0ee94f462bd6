import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import formidable from 'formidable';
import fs from 'fs';
import csvParser from 'csv-parser';
import { v4 as uuidv4 } from 'uuid';

// Disable default body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

/**
 * API endpoint for importing products data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response with import results
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error, user, role } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    // Parse form data
    const form = new formidable.IncomingForm({
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
    });

    const { fields, files } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) reject(err);
        resolve({ fields, files });
      });
    });

    // Check if file exists
    if (!files.file) {
      return res.status(400).json({ error: 'No file provided' });
    }

    const file = files.file;
    const updateMode = fields.updateMode?.[0] || 'create'; // 'create', 'update', 'upsert'
    
    // Validate file type
    const allowedTypes = ['text/csv', 'application/json'];
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only CSV and JSON files are allowed.' 
      });
    }

    let importData = [];

    // Parse file based on type
    if (file.mimetype === 'text/csv') {
      importData = await parseCSVFile(file.filepath);
    } else if (file.mimetype === 'application/json') {
      importData = await parseJSONFile(file.filepath);
    }

    // Validate import data
    const validationResult = validateProductData(importData);
    if (!validationResult.isValid) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.errors
      });
    }

    // Process import based on update mode
    const importResult = await processProductImport(importData, updateMode, user.id);

    // Clean up uploaded file
    fs.unlinkSync(file.filepath);

    return res.status(200).json({
      success: true,
      message: 'Products imported successfully',
      results: importResult
    });

  } catch (error) {
    console.error('Error importing products:', error);
    return res.status(500).json({
      error: 'Failed to import products',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}

/**
 * Parse CSV file and return array of objects
 */
async function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csvParser())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

/**
 * Parse JSON file and return array of objects
 */
async function parseJSONFile(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const data = JSON.parse(fileContent);
  return Array.isArray(data) ? data : [data];
}

/**
 * Validate product data
 */
function validateProductData(data) {
  const errors = [];
  const requiredFields = ['name', 'price'];

  data.forEach((item, index) => {
    const rowErrors = [];

    // Check required fields
    requiredFields.forEach(field => {
      if (!item[field] || item[field].toString().trim() === '') {
        rowErrors.push(`Missing required field: ${field}`);
      }
    });

    // Validate price
    if (item.price && isNaN(parseFloat(item.price))) {
      rowErrors.push('Price must be a valid number');
    }

    // Validate stock
    if (item.stock && isNaN(parseInt(item.stock))) {
      rowErrors.push('Stock must be a valid integer');
    }

    // Validate status
    if (item.status && !['active', 'inactive'].includes(item.status)) {
      rowErrors.push('Status must be either "active" or "inactive"');
    }

    // Validate featured
    if (item.featured && !['true', 'false', true, false].includes(item.featured)) {
      rowErrors.push('Featured must be true or false');
    }

    if (rowErrors.length > 0) {
      errors.push({
        row: index + 1,
        errors: rowErrors
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Process product import based on update mode
 */
async function processProductImport(data, updateMode, userId) {
  const results = {
    created: 0,
    updated: 0,
    skipped: 0,
    errors: []
  };

  for (let i = 0; i < data.length; i++) {
    const item = data[i];

    try {
      // Prepare product data
      const productData = {
        name: item.name?.trim(),
        description: item.description?.trim() || '',
        short_description: item.short_description?.trim() || '',
        sku: item.sku?.trim() || '',
        price: parseFloat(item.price) || 0,
        sale_price: item.sale_price ? parseFloat(item.sale_price) : null,
        cost_price: item.cost_price ? parseFloat(item.cost_price) : null,
        category: item.category?.trim() || '',
        stock: parseInt(item.stock) || 0,
        low_stock_threshold: parseInt(item.low_stock_threshold) || 5,
        image_url: item.image_url?.trim() || '',
        gallery_images: item.gallery_images ? item.gallery_images.split(';').filter(url => url.trim()) : [],
        status: item.status?.trim() || 'active',
        featured: item.featured === 'true' || item.featured === true,
        is_active: item.is_active !== 'false' && item.is_active !== false,
        meta_title: item.meta_title?.trim() || '',
        meta_description: item.meta_description?.trim() || '',
        updated_at: new Date().toISOString()
      };

      // Check if product exists (by name or SKU)
      let existingProduct = null;
      if (item.id) {
        const { data: productById } = await supabaseAdmin
          .from('products')
          .select('id, name, sku')
          .eq('id', item.id)
          .single();
        existingProduct = productById;
      } else if (productData.sku) {
        const { data: productBySku } = await supabaseAdmin
          .from('products')
          .select('id, name, sku')
          .eq('sku', productData.sku)
          .single();
        existingProduct = productBySku;
      } else {
        const { data: productByName } = await supabaseAdmin
          .from('products')
          .select('id, name, sku')
          .eq('name', productData.name)
          .single();
        existingProduct = productByName;
      }

      if (existingProduct) {
        // Product exists
        if (updateMode === 'create') {
          results.skipped++;
          continue;
        } else if (updateMode === 'update' || updateMode === 'upsert') {
          // Update existing product
          const { error } = await supabaseAdmin
            .from('products')
            .update(productData)
            .eq('id', existingProduct.id);

          if (error) {
            results.errors.push({
              row: i + 1,
              error: `Failed to update product: ${error.message}`
            });
          } else {
            results.updated++;
          }
        }
      } else {
        // Product doesn't exist
        if (updateMode === 'update') {
          results.skipped++;
          continue;
        } else if (updateMode === 'create' || updateMode === 'upsert') {
          // Create new product
          if (!productData.id) {
            productData.id = uuidv4();
          }
          productData.created_at = new Date().toISOString();

          const { error } = await supabaseAdmin
            .from('products')
            .insert([productData]);

          if (error) {
            results.errors.push({
              row: i + 1,
              error: `Failed to create product: ${error.message}`
            });
          } else {
            results.created++;
          }
        }
      }
    } catch (error) {
      results.errors.push({
        row: i + 1,
        error: `Processing error: ${error.message}`
      });
    }
  }

  return results;
}
