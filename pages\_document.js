import { Html, Head, Main, NextScript } from 'next/document'

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Extension Error Suppression - Load first to catch early errors */}
        <script src="/js/extension-error-suppression.js"></script>

        {/* Square Performance Monitor - Load early to catch performance issues */}
        <script src="/js/square-performance-monitor.js"></script>

        {/* Font Loading Error Handler */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Handle font loading errors gracefully
              (function() {
                // Suppress font-related CSP errors in console
                const originalError = console.error;
                console.error = function(...args) {
                  const message = args.join(' ');
                  // Filter out font-related CSP errors to reduce noise
                  if (message.includes('fonts.googleapis.com') &&
                      (message.includes('Content Security Policy') ||
                       message.includes('MIME type') ||
                       message.includes('Refused to apply style'))) {
                    // Log a simplified message instead
                    console.warn('[Font Loading] External font request filtered by CSP - using fallback fonts');
                    return;
                  }
                  originalError.apply(console, args);
                };

                // Add font loading detection
                if ('fonts' in document) {
                  document.fonts.ready.then(function() {
                    console.log('[Font Loading] Fonts loaded successfully');
                  }).catch(function(error) {
                    console.warn('[Font Loading] Some fonts failed to load, using fallbacks:', error);
                  });
                }
              })();
            `
          }}
        />

        {/* Font preloading with CSP-safe attributes */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap"
          rel="stylesheet"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800&display=swap"
          rel="stylesheet"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap"
          rel="stylesheet"
          crossOrigin="anonymous"
        />

        {/* PWA Manifest */}
        <link rel="manifest" href="/manifest.json" />

        {/* PWA Meta Tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Ocean Soul Sparkles" />
        <meta name="application-name" content="Ocean Soul Sparkles" />
        <meta name="msapplication-TileColor" content="#4A90E2" />
        <meta name="theme-color" content="#4A90E2" />

        {/* PWA Icons */}
        <link rel="apple-touch-icon" href="/images/icons/icon-192x192.svg" />
        <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-152x152.svg" />
        <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.svg" />
        <link rel="apple-touch-icon" sizes="167x167" href="/images/icons/icon-192x192.svg" />

        {/* OneSignal Web Push Notifications - Only loaded in production */}
        {process.env.NODE_ENV === 'production' && (
          <>
            {/* Store OneSignal configuration as global variables */}
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  // Store OneSignal configuration
                  window.__ONESIGNAL_APP_ID__ = "${process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID || '************************************'}";
                  window.__ONESIGNAL_SAFARI_WEB_ID__ = "${process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID || 'web.onesignal.auto.************************************'}";

                  // Prevent hydration errors by defining a mock OneSignal object
                  // This will be replaced by the real OneSignal when it loads
                  try {
                    // Only create mock if OneSignal doesn't exist yet
                    if (!window.OneSignal) {
                      window.OneSignal = {
                        _mock: true,
                        getNotificationPermission: function() { return Promise.resolve('default'); },
                        isPushNotificationsEnabled: function() { return Promise.resolve(false); },
                        showNativePrompt: function() { return Promise.resolve(); },
                        showHttpPrompt: function() { return Promise.resolve(); },
                        showCategorySlidedown: function() { return Promise.resolve(); },
                        getUserId: function() { return Promise.resolve('mock-user-id'); },
                        setExternalUserId: function() { return Promise.resolve(); },
                        removeExternalUserId: function() { return Promise.resolve(); },
                        setEmail: function() { return Promise.resolve(); },
                        sendTag: function() { return Promise.resolve(); },
                        getTags: function() { return Promise.resolve({}); },
                        on: function() {},
                        once: function() {},
                        off: function() {},
                        init: function() { return Promise.resolve(); }
                      };
                      console.log('Created OneSignal mock object');
                    }
                  } catch (e) {
                    console.error('Error creating OneSignal mock:', e);
                  }
                `
              }}
            />            {/* Load OneSignal SDK */}
            <script src="https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js" defer></script>

            {/* Load our robust initialization script */}
            <script src="/js/onesignal-robust-init.js" defer></script>
          </>
        )}

        {/* Structured Data - Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "OceanSoulSparkles",
              "url": "https://www.oceansoulsparkles.com.au",
              "logo": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
              "sameAs": [
                "https://www.instagram.com/oceansoulsparkles",
                "https://www.facebook.com/OceanSoulSparkles/"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+61-XXX-XXX-XXX",
                "contactType": "customer service",
                "email": "<EMAIL>"
              }
            })
          }}
        />

        {/* Structured Data - LocalBusiness */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "EntertainmentBusiness",
              "name": "OceanSoulSparkles",
              "image": "https://www.oceansoulsparkles.com.au/images/bannerlogo.PNG",
              "url": "https://www.oceansoulsparkles.com.au",
              "telephone": "+61-XXX-XXX-XXX",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "addressLocality": "Melbourne",
                "addressRegion": "Victoria",
                "addressCountry": "AU"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": "-37.8136",
                "longitude": "144.9631"
              },
              "priceRange": "$$"
            })
          }}
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  )
}
