import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { hasBookingPermission, BOOKING_PERMISSIONS } from '@/lib/artist-booking-permissions'
import ProfileManagementCard from './ProfileManagementCard'
import AvailabilityCalendarCard from './AvailabilityCalendarCard'
import BookingListCard from './BookingListCard'
import EarningsCard from './EarningsCard'
import QuickActionsCard from './QuickActionsCard'
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css'
import { toast } from 'react-toastify' // Added for notifications

export default function ArtistBraiderDashboard() {
  const { user, role } = useAuth()
  const [isCompletingOnboarding, setIsCompletingOnboarding] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    profile: null,
    upcomingBookings: [],
    recentPayments: [],
    todaysSchedule: [],
    availabilityStatus: 'available',
    stats: {
      totalBookings: 0,
      totalEarnings: 0,
      averageRating: 0,
      completedServices: 0,
      thisWeekBookings: 0,
      thisMonthEarnings: 0
    }
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (user && (role === 'artist' || role === 'braider')) {
      fetchDashboardData()
    }
  }, [user, role])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch enhanced artist/braider profile and dashboard data
      const response = await fetch('/api/artist/dashboard-enhanced', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data')
      }

      const data = await response.json()
      setDashboardData(data)
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      setError(err.message)

      // Fallback to basic dashboard data
      try {
        const fallbackResponse = await fetch('/api/artist/dashboard', {
          headers: {
            'Authorization': `Bearer ${user.access_token}`,
            'Content-Type': 'application/json'
          }
        })

        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json()
          setDashboardData(prev => ({ ...prev, ...fallbackData }))
        }
      } catch (fallbackErr) {
        console.error('Fallback dashboard fetch failed:', fallbackErr)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleAvailabilityToggle = async (newStatus) => {
    try {
      const response = await fetch('/api/artist/availability', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_available_today: newStatus === 'available'
        })
      })

      if (response.ok) {
        setDashboardData(prev => ({
          ...prev,
          availabilityStatus: newStatus,
          profile: {
            ...prev.profile,
            is_available_today: newStatus === 'available'
          }
        }))
      }
    } catch (err) {
      console.error('Error updating availability:', err)
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading your dashboard...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.error}>
        <h2>Unable to Load Dashboard</h2>
        <p>{error}</p>
        <button onClick={fetchDashboardData} className={styles.retryButton}>
          Try Again
        </button>
      </div>
    )
  }

  const { profile, upcomingBookings, recentPayments, stats } = dashboardData

  const handleMarkOnboardingComplete = async () => {
    setIsCompletingOnboarding(true);
    try {
      const response = await fetch('/api/artist/me/onboarding-status', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`, // Assuming user.access_token is available
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to mark onboarding as complete.');
      }
      const result = await response.json();
      if (result.success && result.is_profile_complete) {
        setDashboardData(prev => ({
          ...prev,
          profile: {
            ...prev.profile,
            is_profile_complete: true,
          }
        }));
        toast.success('Onboarding marked as complete! Welcome aboard fully!');
      } else {
          throw new Error('Failed to update onboarding status on the client.');
      }
    } catch (err) {
      console.error('Error marking onboarding complete:', err);
      toast.error(`Error: ${err.message}`);
    } finally {
      setIsCompletingOnboarding(false);
    }
  };

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1>Welcome back, {profile?.artist_name || user?.email}!</h1>
        <p className={styles.subtitle}>
          {role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} Dashboard
        </p>
      </div>

      {profile && profile.is_profile_complete === false && (
        <div className={styles.onboardingSection}>
          <h2>Welcome! Let's get you started:</h2>
          <ul className={styles.onboardingList}>
            <li>
              <Link href="/artist/my-profile" legacyBehavior>
                <a className={styles.onboardingLink}>1. Complete Your Profile</a>
              </Link>
              <p>Fill in your display name, bio, portfolio, and contact details.</p>
            </li>
            <li>
              <Link href="/artist/policies" legacyBehavior>
                <a className={styles.onboardingLink}>2. Review Induction Policies</a>
              </Link>
              <p>Please read through our team policies and procedures.</p>
            </li>
          </ul>
          <button
            className={styles.completeOnboardingButton}
            onClick={handleMarkOnboardingComplete}
            disabled={isCompletingOnboarding}
          >
            {isCompletingOnboarding ? 'Completing...' : 'Mark Onboarding as Complete'}
          </button>
        </div>
      )}

      {/* Stats Overview */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>📅</div>
          <div className={styles.statContent}>
            <h3>{stats.totalBookings}</h3>
            <p>Total Bookings</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>💰</div>
          <div className={styles.statContent}>
            <h3>${stats.totalEarnings?.toFixed(2) || '0.00'}</h3>
            <p>Total Earnings</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⭐</div>
          <div className={styles.statContent}>
            <h3>{stats.averageRating?.toFixed(1) || 'N/A'}</h3>
            <p>Average Rating</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <h3>{stats.completedServices}</h3>
            <p>Completed Services</p>
          </div>
        </div>
      </div>

      <div className={styles.contentGrid}>
        {/* Upcoming Bookings */}
        <div className={styles.section}>
          <h2>Upcoming Bookings</h2>
          {upcomingBookings.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No upcoming bookings scheduled</p>
            </div>
          ) : (
            <div className={styles.bookingsList}>
              {upcomingBookings.slice(0, 5).map(booking => (
                <div key={booking.id} className={styles.bookingCard}>
                  <div className={styles.bookingDate}>
                    {new Date(booking.start_time).toLocaleDateString('en-AU', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className={styles.bookingDetails}>
                    <h4>{booking.service_name}</h4>
                    <p>{booking.customer_name}</p>
                    <span className={styles.bookingTime}>
                      {new Date(booking.start_time).toLocaleTimeString('en-AU', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <div className={styles.bookingStatus}>
                    <span className={`${styles.statusBadge} ${styles[booking.status]}`}>
                      {booking.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Recent Payments */}
        <div className={styles.section}>
          <h2>Recent Payments</h2>
          {recentPayments.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No recent payments</p>
            </div>
          ) : (
            <div className={styles.paymentsList}>
              {recentPayments.slice(0, 5).map(payment => (
                <div key={payment.id} className={styles.paymentCard}>
                  <div className={styles.paymentDate}>
                    {new Date(payment.created_at).toLocaleDateString('en-AU')}
                  </div>
                  <div className={styles.paymentDetails}>
                    <h4>{payment.service_name}</h4>
                    <p>{payment.customer_name}</p>
                  </div>
                  <div className={styles.paymentAmount}>
                    <span className={styles.amount}>
                      ${payment.amount?.toFixed(2)}
                    </span>
                    <span className={styles.commission}>
                      (${payment.commission?.toFixed(2)} commission)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.quickActions}>
        <h2>Quick Actions</h2>
        <div className={styles.actionButtons}>
          <Link href="/artist/my-profile" passHref legacyBehavior>
            <a className={styles.actionButton}> {/* Apply the button's styling to the anchor tag */}
              <span className={styles.actionIcon}>👤</span>
              Update Profile
            </a>
          </Link>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>📅</span>
            View Schedule
          </button>
          <button className={styles.actionButton}>
            <span className={styles.actionIcon}>💰</span>
            Payment History
          </button>
          <Link href="/artist/my-profile#availability-schedule" passHref legacyBehavior>
            <a className={styles.actionButton}>
              <span className={styles.actionIcon}>⚙️</span>
              Availability Settings
            </a>
          </Link>
        </div>
      </div>
    </div>
  )
}
