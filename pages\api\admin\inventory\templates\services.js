import { Parser } from 'json2csv';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for downloading service import template
 * Returns a CSV template with sample data and proper headers
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - CSV template file
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request
  const { authorized, error } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    // Sample service data for template
    const sampleData = [
      {
        name: 'Sample Face Painting',
        description: 'Professional face painting service for events and parties',
        duration: 30,
        price: 25.00,
        color: '#ff6b6b',
        category: 'Face Painting',
        category_id: '',
        image_url: 'https://example.com/face-painting.jpg',
        status: 'active',
        featured: true,
        visible_on_public: true,
        visible_on_pos: true,
        visible_on_events: true,
        meta_title: 'Professional Face Painting Services',
        meta_description: 'High-quality face painting for all ages and events',
        booking_requirements: 'Please arrive 5 minutes early',
        availability_notes: 'Available weekends and holidays'
      },
      {
        name: 'Sample Hair Braiding',
        description: 'Beautiful hair braiding and styling service',
        duration: 60,
        price: 45.00,
        color: '#4ecdc4',
        category: 'Hair & Braiding',
        category_id: '',
        image_url: 'https://example.com/hair-braiding.jpg',
        status: 'active',
        featured: false,
        visible_on_public: true,
        visible_on_pos: true,
        visible_on_events: false,
        meta_title: 'Professional Hair Braiding Services',
        meta_description: 'Expert hair braiding and styling for special occasions',
        booking_requirements: 'Hair should be clean and dry',
        availability_notes: 'Available by appointment only'
      }
    ];

    // Define CSV fields
    const csvFields = [
      'name', 'description', 'duration', 'price', 'color', 'category', 
      'category_id', 'image_url', 'status', 'featured', 'visible_on_public', 
      'visible_on_pos', 'visible_on_events', 'meta_title', 'meta_description', 
      'booking_requirements', 'availability_notes'
    ];

    // Generate CSV
    const json2csvParser = new Parser({ fields: csvFields });
    const csv = json2csvParser.parse(sampleData);

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="services_import_template.csv"');
    
    return res.status(200).send(csv);

  } catch (error) {
    console.error('Error generating service template:', error);
    return res.status(500).json({
      error: 'Failed to generate template',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
