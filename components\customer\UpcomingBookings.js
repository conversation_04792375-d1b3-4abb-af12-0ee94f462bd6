/**
 * Upcoming Bookings Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - Customer Upcoming Bookings Display
 */

import { useState } from 'react'
import Link from 'next/link'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/UpcomingBookings.module.css'

export default function UpcomingBookings({ bookings, customer, onBookingUpdate }) {
  const { isMobile, viewport } = useMobileOptimization()
  
  const [loading, setLoading] = useState({})

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTimeUntilBooking = (dateString) => {
    const now = new Date()
    const bookingDate = new Date(dateString)
    const diffInHours = Math.ceil((bookingDate - now) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Starting soon'
    } else if (diffInHours < 24) {
      return `In ${diffInHours} hour${diffInHours > 1 ? 's' : ''}`
    } else {
      const diffInDays = Math.ceil(diffInHours / 24)
      return `In ${diffInDays} day${diffInDays > 1 ? 's' : ''}`
    }
  }

  const handleQuickAction = async (action, bookingId) => {
    setLoading(prev => ({ ...prev, [`${action}_${bookingId}`]: true }))

    try {
      switch (action) {
        case 'reschedule':
          // Navigate to reschedule page
          window.location.href = `/customer/bookings/${bookingId}/reschedule`
          break

        case 'cancel':
          if (window.confirm('Are you sure you want to cancel this booking?')) {
            const response = await fetch(`/api/customer/bookings/${bookingId}/cancel`, {
              method: 'POST'
            })
            
            const data = await response.json()
            
            if (data.success) {
              toast.success('Booking cancelled successfully')
              if (onBookingUpdate) {
                onBookingUpdate()
              }
            } else {
              throw new Error(data.error || 'Failed to cancel booking')
            }
          }
          break

        case 'modify':
          // Navigate to modify page
          window.location.href = `/customer/bookings/${bookingId}/modify`
          break

        default:
          console.warn('Unknown action:', action)
      }
    } catch (error) {
      console.error('Error handling booking action:', error)
      toast.error('Action failed. Please try again.')
    } finally {
      setLoading(prev => ({ ...prev, [`${action}_${bookingId}`]: false }))
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return '#28a745'
      case 'pending':
        return '#ffc107'
      case 'in_progress':
        return '#17a2b8'
      case 'completed':
        return '#6c757d'
      case 'cancelled':
        return '#dc3545'
      default:
        return '#6c757d'
    }
  }

  if (!bookings || bookings.length === 0) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>📅</div>
        <h3 className={styles.emptyTitle}>No Upcoming Bookings</h3>
        <p className={styles.emptyDescription}>
          You don't have any upcoming appointments scheduled.
        </p>
        <Link href="/book-online" className={styles.bookNowButton}>
          Book Your Next Appointment
        </Link>
      </div>
    )
  }

  return (
    <div className={styles.upcomingBookings}>
      <div className={styles.header}>
        <h2 className={styles.title}>Upcoming Appointments</h2>
        <span className={styles.count}>
          {bookings.length} appointment{bookings.length > 1 ? 's' : ''}
        </span>
      </div>

      <div className={styles.bookingsList}>
        {bookings.map(booking => (
          <div key={booking.id} className={styles.bookingCard}>
            {/* Booking Header */}
            <div className={styles.bookingHeader}>
              <div className={styles.serviceInfo}>
                <h3 className={styles.serviceName}>
                  {booking.service?.name || 'Service'}
                </h3>
                <div 
                  className={styles.statusBadge}
                  style={{ backgroundColor: getStatusColor(booking.status) }}
                >
                  {booking.status.replace('_', ' ').toUpperCase()}
                </div>
              </div>
              
              <div className={styles.timeUntil}>
                {getTimeUntilBooking(booking.start_time)}
              </div>
            </div>

            {/* Booking Details */}
            <div className={styles.bookingDetails}>
              <div className={styles.detailRow}>
                <span className={styles.detailIcon}>📅</span>
                <span className={styles.detailLabel}>Date:</span>
                <span className={styles.detailValue}>
                  {formatDate(booking.start_time)}
                </span>
              </div>

              <div className={styles.detailRow}>
                <span className={styles.detailIcon}>⏰</span>
                <span className={styles.detailLabel}>Time:</span>
                <span className={styles.detailValue}>
                  {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                </span>
              </div>

              {booking.artist && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>👤</span>
                  <span className={styles.detailLabel}>Artist:</span>
                  <span className={styles.detailValue}>
                    {booking.artist.display_name || booking.artist.artist_name}
                  </span>
                </div>
              )}

              <div className={styles.detailRow}>
                <span className={styles.detailIcon}>📍</span>
                <span className={styles.detailLabel}>Location:</span>
                <span className={styles.detailValue}>
                  {booking.location || 'Studio'}
                </span>
              </div>

              {booking.notes && (
                <div className={styles.detailRow}>
                  <span className={styles.detailIcon}>📝</span>
                  <span className={styles.detailLabel}>Notes:</span>
                  <span className={styles.detailValue}>
                    {booking.notes}
                  </span>
                </div>
              )}
            </div>

            {/* Booking Actions */}
            <div className={styles.bookingActions}>
              <Link 
                href={`/customer/bookings/${booking.id}`}
                className={styles.viewButton}
              >
                View Details
              </Link>

              <button
                onClick={() => handleQuickAction('reschedule', booking.id)}
                disabled={loading[`reschedule_${booking.id}`]}
                className={styles.rescheduleButton}
              >
                {loading[`reschedule_${booking.id}`] ? 'Loading...' : 'Reschedule'}
              </button>

              <button
                onClick={() => handleQuickAction('modify', booking.id)}
                disabled={loading[`modify_${booking.id}`]}
                className={styles.modifyButton}
              >
                {loading[`modify_${booking.id}`] ? 'Loading...' : 'Modify'}
              </button>

              <button
                onClick={() => handleQuickAction('cancel', booking.id)}
                disabled={loading[`cancel_${booking.id}`]}
                className={styles.cancelButton}
              >
                {loading[`cancel_${booking.id}`] ? 'Loading...' : 'Cancel'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className={styles.quickActions}>
        <Link href="/book-online" className={styles.quickActionButton}>
          <span className={styles.quickActionIcon}>➕</span>
          <span className={styles.quickActionLabel}>Book Another</span>
        </Link>

        <Link href="/customer/services/multi-booking" className={styles.quickActionButton}>
          <span className={styles.quickActionIcon}>✨</span>
          <span className={styles.quickActionLabel}>Multi-Service</span>
        </Link>

        <Link href="/customer/history" className={styles.quickActionButton}>
          <span className={styles.quickActionIcon}>📋</span>
          <span className={styles.quickActionLabel}>View History</span>
        </Link>
      </div>
    </div>
  )
}
