-- Secure Application Tokens System
-- This migration adds secure one-time tokens for artist/braider application access
-- Removes password requirement and implements token-based authentication

-- =============================================
-- APPLICATION TOKENS TABLE
-- =============================================

-- Create application tokens table for secure one-time access
CREATE TABLE IF NOT EXISTS public.application_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  application_id UUID REFERENCES public.artist_braider_applications(id) ON DELETE CASCADE,
  token TEXT NOT NULL UNIQUE,
  token_type TEXT CHECK (token_type IN ('application_access', 'password_reset')) DEFAULT 'application_access',
  expires_at TIMESTAMPTZ NOT NULL,
  used_at TIMESTAMPTZ,
  is_used BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id), -- Admin who created the token
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IF NOT EXISTS idx_application_tokens_token ON public.application_tokens(token);
CREATE INDEX IF NOT EXISTS idx_application_tokens_user_id ON public.application_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_application_tokens_application_id ON public.application_tokens(application_id);
CREATE INDEX IF NOT EXISTS idx_application_tokens_expires_at ON public.application_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_application_tokens_used ON public.application_tokens(is_used, expires_at);

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS
ALTER TABLE public.application_tokens ENABLE ROW LEVEL SECURITY;

-- Combined policy for viewing application tokens
DROP POLICY IF EXISTS "Admins and devs can view all application tokens" ON public.application_tokens;
DROP POLICY IF EXISTS "Users can view their own application tokens" ON public.application_tokens;
CREATE POLICY "Users can view application tokens based on role or ownership" ON public.application_tokens
  FOR SELECT USING (
    (EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = (select auth.uid())
      AND role IN ('dev', 'admin')
    ))
    OR ((select auth.uid()) = user_id)
  );

-- Only admins and devs can create tokens
CREATE POLICY "Admins and devs can create application tokens" ON public.application_tokens
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = (select auth.uid())
      AND role IN ('dev', 'admin')
    )
  );

-- Only admins and devs can update tokens (for marking as used)
CREATE POLICY "Admins and devs can update application tokens" ON public.application_tokens
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = (select auth.uid())
      AND role IN ('dev', 'admin')
    )
  );

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to generate secure random token
CREATE OR REPLACE FUNCTION public.generate_application_token()
RETURNS TEXT AS $$
BEGIN
  -- Generate a secure random token (32 characters)
  RETURN encode(gen_random_bytes(24), 'base64url');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate application token
CREATE OR REPLACE FUNCTION public.validate_application_token(token_value TEXT)
RETURNS TABLE (
  is_valid BOOLEAN,
  user_id UUID,
  application_id UUID,
  error_message TEXT
) AS $$
DECLARE
  token_record RECORD;
BEGIN
  -- Find the token
  SELECT * INTO token_record
  FROM public.application_tokens
  WHERE token = token_value;

  -- Check if token exists
  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::UUID, 'Invalid token'::TEXT;
    RETURN;
  END IF;

  -- Check if token is already used
  IF token_record.is_used THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::UUID, 'Token has already been used'::TEXT;
    RETURN;
  END IF;

  -- Check if token is expired
  IF token_record.expires_at < NOW() THEN
    RETURN QUERY SELECT FALSE, NULL::UUID, NULL::UUID, 'Token has expired'::TEXT;
    RETURN;
  END IF;

  -- Token is valid
  RETURN QUERY SELECT TRUE, token_record.user_id, token_record.application_id, NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark token as used
CREATE OR REPLACE FUNCTION public.mark_token_as_used(token_value TEXT, client_ip INET DEFAULT NULL, client_user_agent TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.application_tokens
  SET 
    is_used = TRUE,
    used_at = NOW(),
    ip_address = COALESCE(client_ip, ip_address),
    user_agent = COALESCE(client_user_agent, user_agent),
    updated_at = NOW()
  WHERE token = token_value
    AND is_used = FALSE
    AND expires_at > NOW();

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- CLEANUP FUNCTIONS
-- =============================================

-- Function to clean up expired tokens (run periodically)
CREATE OR REPLACE FUNCTION public.cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.application_tokens
  WHERE expires_at < NOW() - INTERVAL '30 days'; -- Keep expired tokens for 30 days for audit

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to update updated_at timestamp
CREATE TRIGGER update_application_tokens_updated_at
  BEFORE UPDATE ON public.application_tokens
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =============================================
-- COMMENTS
-- =============================================

COMMENT ON TABLE public.application_tokens IS 'Secure one-time tokens for artist/braider application access';
COMMENT ON FUNCTION public.generate_application_token() IS 'Generates a secure random token for application access';
COMMENT ON FUNCTION public.validate_application_token(TEXT) IS 'Validates an application token and returns user/application info';
COMMENT ON FUNCTION public.mark_token_as_used(TEXT, INET, TEXT) IS 'Marks a token as used to prevent reuse';
COMMENT ON FUNCTION public.cleanup_expired_tokens() IS 'Cleans up expired tokens (run periodically)';
