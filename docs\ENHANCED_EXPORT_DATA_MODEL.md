# Enhanced Export Data Model
**Ocean Soul Sparkles - Services Export Enhancement**

## 🎯 **Current vs Enhanced Data Structure**

### **Current Export Structure (Limited)**
```json
{
  "id": "service-uuid",
  "name": "Body Painting",
  "description": "Professional body painting",
  "duration": 10,
  "price": 65,
  "category": "Body Painting",
  "category_id": "uuid",
  // ... other basic fields
}
```

### **Enhanced Export Structure (Complete)**
```json
{
  "id": "service-uuid",
  "name": "Body Painting",
  "description": "Professional body painting",
  "base_duration": 10,
  "base_price": 65,
  "color": "#6a0dad",
  "status": "active",
  "featured": false,
  "visibility": {
    "public": true,
    "pos": true,
    "events": true
  },
  "category": {
    "id": "category-uuid",
    "name": "Body Painting",
    "description": "Professional body painting services",
    "parent_id": null,
    "parent_name": null
  },
  "pricing_tiers": [
    {
      "id": "tier-uuid",
      "name": "Small",
      "description": "Small area body painting",
      "duration": 5,
      "price": 40,
      "is_default": false,
      "sort_order": 1
    },
    {
      "id": "tier-uuid",
      "name": "Medium", 
      "description": "Medium area body painting",
      "duration": 10,
      "price": 65,
      "is_default": true,
      "sort_order": 2
    }
    // ... more tiers
  ],
  "tier_count": 5,
  "price_range": {
    "min": 40,
    "max": 150,
    "default": 65
  },
  "duration_range": {
    "min": 5,
    "max": 30,
    "default": 10
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 📊 **Export Format Options**

### **Option 1: Hierarchical JSON (Recommended)**
- **Pros**: Preserves full data relationships, easy to parse programmatically
- **Cons**: More complex structure
- **Use Case**: API integrations, data analysis tools

### **Option 2: Flattened CSV with Tier Expansion**
- **Pros**: Excel-friendly, familiar format
- **Cons**: Repetitive data for services with multiple tiers
- **Structure**: One row per service-tier combination

```csv
service_id,service_name,tier_id,tier_name,tier_price,tier_duration,category_name,...
uuid1,Body Painting,tier1,Small,40,5,Body Painting,...
uuid1,Body Painting,tier2,Medium,65,10,Body Painting,...
uuid1,Body Painting,tier3,Large,90,15,Body Painting,...
```

### **Option 3: Hybrid CSV with Tier Summary**
- **Pros**: Compact, includes tier summary data
- **Cons**: Tier details in JSON strings within CSV
- **Structure**: One row per service with tier data as JSON

```csv
service_id,service_name,category_name,tier_count,price_range_min,price_range_max,pricing_tiers_json,...
uuid1,Body Painting,Body Painting,5,40,150,"[{""name"":""Small"",""price"":40}...]",...
```

## 🔧 **Implementation Strategy**

### **Database Query Enhancement**
```sql
-- Enhanced query with JOINs
SELECT 
  s.*,
  sc.name as category_name,
  sc.description as category_description,
  sc.parent_id as category_parent_id,
  parent_sc.name as parent_category_name,
  json_agg(
    json_build_object(
      'id', spt.id,
      'name', spt.name,
      'description', spt.description,
      'duration', spt.duration,
      'price', spt.price,
      'is_default', spt.is_default,
      'sort_order', spt.sort_order
    ) ORDER BY spt.sort_order
  ) as pricing_tiers
FROM services s
LEFT JOIN service_categories sc ON s.category_id = sc.id
LEFT JOIN service_categories parent_sc ON sc.parent_id = parent_sc.id
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
GROUP BY s.id, sc.id, parent_sc.id
ORDER BY s.name;
```

### **Data Processing Logic**
1. **Aggregate Tier Data**: Group tiers by service
2. **Calculate Ranges**: Min/max price and duration
3. **Identify Defaults**: Mark default tier for each service
4. **Build Hierarchy**: Construct category relationships

### **Export Format Selection**
- **JSON**: Use hierarchical structure (Option 1)
- **CSV**: Use flattened tier expansion (Option 2) with format parameter `?format=csv&style=expanded`
- **CSV Compact**: Use tier summary (Option 3) with format parameter `?format=csv&style=compact`

## 📋 **New Export Fields**

### **Service Level Fields**
- `tier_count`: Number of pricing tiers
- `price_range.min/max/default`: Price range summary
- `duration_range.min/max/default`: Duration range summary
- `category.parent_name`: Parent category if exists

### **Category Level Fields**
- `category.id`: Category UUID
- `category.name`: Category name
- `category.description`: Category description
- `category.parent_id`: Parent category UUID (for subcategories)
- `category.parent_name`: Parent category name

### **Tier Level Fields** (per tier)
- `pricing_tiers[].id`: Tier UUID
- `pricing_tiers[].name`: Tier name (Small, Medium, Large, etc.)
- `pricing_tiers[].description`: Tier-specific description
- `pricing_tiers[].duration`: Tier-specific duration
- `pricing_tiers[].price`: Tier-specific price
- `pricing_tiers[].is_default`: Whether this is the default tier
- `pricing_tiers[].sort_order`: Display order

## 🎯 **Expected Benefits**

### **Business Value**
- **Complete Service Analysis**: Full view of all pricing options
- **Category Insights**: Understanding of service categorization
- **Pricing Strategy**: Analysis of tier distribution and pricing ranges
- **Operational Planning**: Duration planning across all service levels

### **Technical Benefits**
- **Data Completeness**: No missing business-critical information
- **Flexible Formats**: Multiple export options for different use cases
- **Scalable Structure**: Supports future enhancements (subcategories, etc.)
- **API-Ready**: JSON format suitable for system integrations

---

**Next Steps**: Implement the enhanced query and data processing logic to support this new data model.
