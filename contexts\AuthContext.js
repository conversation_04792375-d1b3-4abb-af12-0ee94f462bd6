import { createContext, useContext, useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { useRouter } from 'next/router'
import supabase, { getCurrentUser } from '../lib/supabase'
import { toast } from 'react-toastify'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  // Maintain existing state structure
  // Maintain patched method references
  const [user, setUser] = useState(null)
  const [role, setRole] = useState(null)
  const authInstanceRef = useRef(null);
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const router = useRouter()

  // Admin route detection
  const isAdminRoute = () => {
    return router.pathname.startsWith('/admin')
  }

  // Application route detection (special case for token-based onboarding)
  const isApplicationRoute = () => {
    return router.pathname.startsWith('/apply/')
  }

  // Public admin paths that don't require authentication
  const publicAdminPaths = [
    '/admin/login',
    '/admin/reset-password',
    '/admin/forgot-password'
  ]

  // Check if current path requires authentication
  const isProtectedAdminRoute = () => {
    return isAdminRoute() && !publicAdminPaths.some(path => router.pathname.includes(path))
  }

  // Check if user is currently in POS terminal (prevent redirects during payment processing)
  const isPOSOperation = () => {
    return router.pathname.includes('/admin/pos')
  }

  // Enhanced redirect logic with loop prevention
  const redirectToLogin = () => {
    console.log(`[AuthContext] redirectToLogin: START, Current path: ${router.pathname}, Target: /admin/login or /login?redirect=...`);
    if (typeof window !== 'undefined') {
      // Prevent redirect loops
      if (publicAdminPaths.includes(router.pathname)) {
        console.log('[AuthContext] redirectToLogin: Already on a public admin page (e.g., login), skipping redirect.')
        return
      }

      // Check if we're already in a redirect state
      const isRedirecting = sessionStorage.getItem('auth_redirecting')
      if (isRedirecting) {
        console.log('[AuthContext] redirectToLogin: Redirect already in progress (sessionStorage auth_redirecting flag is set), skipping.')
        return
      }

      // Set redirect flag to prevent loops
      sessionStorage.setItem('auth_redirecting', 'true')

      const currentPath = encodeURIComponent(router.asPath)
      let targetPath;
      if (isAdminRoute()) {
        sessionStorage.setItem('redirect_after_login', router.pathname)
        targetPath = '/admin/login';
        console.log(`[AuthContext] redirectToLogin: Admin route. Redirecting to admin login from: ${router.pathname}. Storing redirect_after_login: ${router.pathname}`);

      } else {
        targetPath = `/login?redirect=${currentPath}`;
        console.log(`[AuthContext] redirectToLogin: Non-admin route. Redirecting to public login from: ${router.pathname}. Target: ${targetPath}`);
      }

      console.log(`[AuthContext] redirectToLogin: Executing router.push to ${targetPath}`);
      router.push(targetPath).finally(() => {
        // Clear redirect flag after navigation
        setTimeout(() => {
          sessionStorage.removeItem('auth_redirecting')
          console.log(`[AuthContext] redirectToLogin: Cleared auth_redirecting flag for ${targetPath}`);
        }, 1000)
      })
    } else {
      console.log(`[AuthContext] redirectToLogin: Not in browser environment (typeof window === 'undefined'). Skipping.`);
    }
  }

  // Initialize auth state using Supabase directly with improved session management
  useEffect(() => {
    let authListener
    let mounted = true
    let tokenRefreshInterval
    const authId = Math.random().toString(36).substring(2, 8)

    // Only initialize authentication for admin routes and application routes to prevent unauthorized calls on public pages
    if (!isAdminRoute() && !isApplicationRoute()) {
      console.log(`[Auth ${authId}] Skipping auth initialization for public route:`, router.pathname)
      setLoading(false)
      return
    }

    // Skip re-initialization if we already have a user and we're just navigating
    if (user && !loading && !error && router.isReady) {
      console.log(`[Auth ${authId}] User already authenticated, skipping re-initialization`)
      setLoading(false) // Ensure loading is false
      return
    }

    // Prevent multiple simultaneous initializations
    if (authInstanceRef.current) {
      console.log(`[Auth ${authId}] Auth initialization already in progress, skipping`)
      return
    }
    authInstanceRef.current = authId

    // Declare authTimeout in the outer scope so it's accessible in finally block
    let authTimeout = null;

    async function initializeAuth() {
      console.log(`[AuthContext:${authId}] initializeAuth: START, Path: ${router.pathname}, User: ${user?.email}, Role: ${role}, Loading: ${loading}`);
      try {
        // console.log(`[Auth ${authId}] Initializing auth context`) // Original log, slightly redundant with the one above

        // In production, set a timeout to prevent hanging (reduced for better performance)
        if (process.env.NODE_ENV === 'production') {
          authTimeout = setTimeout(() => {
            console.error(`[Auth ${authId}] Production auth timeout - forcing completion`)
            if (mounted) {
              setLoading(false)
              setError(new Error('Authentication timeout - please refresh the page'))
            }
          }, 5000) // 5 second timeout in production (reduced from 10s)
        }

        // Clear any stale authentication data first
        if (typeof window !== 'undefined') {
          try {
            // Clear potentially corrupted cached data
            const cachedAuth = sessionStorage.getItem('oss_auth_state')
            if (cachedAuth) {
              const { user: cachedUser, role: cachedRole, timestamp } = JSON.parse(cachedAuth)
              // Use cached data if it's less than 15 minutes old (extended for POS operations)
              if (cachedUser && Date.now() - timestamp < 15 * 60 * 1000) {
                console.log(`[Auth ${authId}] Using cached auth state`)
                setUser(cachedUser)
                setRole(cachedRole)
                setLoading(false)
                // Still verify with server in background
              } else {
                console.log(`[Auth ${authId}] Cached auth state expired, clearing`)
                sessionStorage.removeItem('oss_auth_state')
              }
            }
          } catch (cacheError) {
            console.warn(`[Auth ${authId}] Error reading cached auth state, clearing:`, cacheError)
            sessionStorage.removeItem('oss_auth_state')
          }
        }

        // Get initial session and user data
        try {
          console.log(`[AuthContext:${authId}] initializeAuth: Calling getCurrentUser...`);
          const { user: currentUser, role: userRole } = await getCurrentUser()
          console.log(`[AuthContext:${authId}] initializeAuth: getCurrentUser returned - User: ${currentUser?.email}, Role: ${userRole}`);

          if (currentUser && mounted) {
            console.log(`[AuthContext:${authId}] initializeAuth: Setting user to ${currentUser?.email}, role to ${userRole}`);
            setUser(currentUser)
            setRole(userRole)

            // Cache auth state for faster navigation
            if (typeof window !== 'undefined') {
              try {
                const authState = {
                  user: currentUser,
                  role: userRole,
                  timestamp: Date.now()
                }
                sessionStorage.setItem('oss_auth_state', JSON.stringify(authState))
                console.log(`[Auth ${authId}] Cached auth state`)
              } catch (cacheError) {
                console.warn(`[Auth ${authId}] Error caching auth state:`, cacheError)
              }
            }

            // For admin routes, verify admin access with new 5-role system (but not during POS operations)
            const hasAdminAccess = ['dev', 'admin', 'artist', 'braider'].includes(userRole)
            if (isProtectedAdminRoute() && !hasAdminAccess && !isPOSOperation()) {
              console.warn(`[AuthContext:${authId}] User lacks admin privileges for admin route`)
              toast.error('Access denied. Admin privileges required.', {
                autoClose: 5000,
                position: 'top-center'
              })
              console.log(`[AuthContext:${authId}] initializeAuth: Condition met to redirect to login. Path: ${router.pathname}, UserRole: ${userRole}`);
              redirectToLogin()
              return
            }
          } else {
            console.log(`[AuthContext:${authId}] initializeAuth: No active user session found`)

            // Set loading to false immediately when no user is found
            if (mounted) {
              setLoading(false)
            }

            // Redirect to login if on protected route (but not during POS operations)
            if (isProtectedAdminRoute() && !isPOSOperation()) {
              console.log(`[AuthContext:${authId}] initializeAuth: Condition met to redirect to login (no user, protected route). Path: ${router.pathname}, UserRole: ${userRole}`);
              redirectToLogin()
            } else if (isPOSOperation()) {
              console.log(`[AuthContext:${authId}] initializeAuth: POS operation detected, skipping redirect to preserve payment flow`)
            }
          }
        } catch (getUserError) {
          console.error(`[Auth ${authId}] Error getting current user:`, getUserError)
          if (mounted) {
            setError(getUserError)
            setLoading(false)
          }
        }

        // Set up token refresh interval for better session management
        const setupTokenRefresh = () => {
          // Clear any existing interval
          if (tokenRefreshInterval) {
            clearInterval(tokenRefreshInterval)
          }

          // Set up token refresh every 10 minutes (more frequent than default)
          tokenRefreshInterval = setInterval(async () => {
            if (!mounted) return

            try {
              console.log(`[Auth ${authId}] Performing scheduled token refresh`)
              const { data, error } = await supabase.auth.refreshSession()

              if (error) {
                console.warn(`[Auth ${authId}] Scheduled token refresh failed:`, error)
              } else if (data?.session) {
                console.log(`[Auth ${authId}] Scheduled token refresh successful`)
                // Update auth token manager cache
                if (typeof window !== 'undefined') {
                  const { storeToken, setCookieToken } = await import('@/lib/auth-token-manager')
                  storeToken(data.session.access_token)
                  setCookieToken(data.session.access_token)
                }
              }
            } catch (refreshError) {
              console.error(`[Auth ${authId}] Error during scheduled token refresh:`, refreshError)
            }
          }, 10 * 60 * 1000) // 10 minutes
        }

        // Subscribe to auth changes
        console.log(`[Auth ${authId}] Setting up auth subscription`)
        try {
          // Use Supabase's onAuthStateChange method
          const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
              console.log(`[AuthContext:${authId}] onAuthStateChange: Event: ${event}, Session: ${session ? session.user?.email : 'No Session'}`);
              if (!mounted) return

              // console.log(`[Auth ${authId}] Auth event: ${event}`, session ? 'Session exists' : 'No session') // Original log

              if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
                if (session) {
                  try {
                    console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Calling getCurrentUser...`);
                    const { user: updatedUser, role: updatedRole } = await getCurrentUser()
                    console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - getCurrentUser returned - User: ${updatedUser?.email}, Role: ${updatedRole}`);
                    if (mounted) {
                      console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Setting user to ${updatedUser?.email}, role to ${updatedRole}`);
                      setUser(updatedUser)
                      setRole(updatedRole)
                      setError(null)

                      // Update auth token manager with new token
                      if (typeof window !== 'undefined') {
                        try {
                          const { storeToken, setCookieToken } = await import('@/lib/auth-token-manager')
                          storeToken(session.access_token)
                          setCookieToken(session.access_token)
                          console.log(`[Auth ${authId}] Token updated in auth token manager`)
                        } catch (tokenError) {
                          console.warn(`[Auth ${authId}] Error updating token in auth token manager:`, tokenError)
                        }
                      }

                      // Cache auth state for session persistence
                      if (typeof window !== 'undefined') {
                        try {
                          const authState = {
                            user: updatedUser,
                            role: updatedRole,
                            timestamp: Date.now(),
                            sessionId: session.access_token?.substring(0, 10)
                          }
                          sessionStorage.setItem('oss_auth_state', JSON.stringify(authState))
                          console.log(`[Auth ${authId}] Auth state cached successfully`)
                        } catch (cacheError) {
                          console.warn(`[Auth ${authId}] Error caching auth state:`, cacheError)
                        }
                      }

                      // Set up token refresh interval
                      setupTokenRefresh()

                      // Show success notification for sign in
                      if (event === 'SIGNED_IN') {
                        toast.success('Successfully signed in!', {
                          autoClose: 3000,
                          position: 'top-right'
                        })
                      }

                      // For admin routes, verify admin access with new 5-role system
                      const hasAdminAccess = ['dev', 'admin', 'artist', 'braider'].includes(updatedRole)
                      if (isProtectedAdminRoute() && !hasAdminAccess) {
                        console.warn(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - User lacks admin privileges for admin route. UserRole: ${updatedRole}`)
                        toast.error('Access denied. Admin privileges required.', {
                          autoClose: 5000,
                          position: 'top-center'
                        })
                        console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Condition met to redirect to login. Path: ${router.pathname}`);
                        redirectToLogin()
                        return
                      }
                    }
                  } catch (updateError) {
                    console.error(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Error updating user:`, updateError)
                  }
                }
              } else if (event === 'SIGNED_OUT') {
                if (mounted) {
                  console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Clearing user and role after sign out`)
                  setUser(null)
                  setRole(null)
                  setError(null)

                  // Clear all cached auth data
                  if (typeof window !== 'undefined') {
                    try {
                      sessionStorage.removeItem('oss_auth_state')
                      sessionStorage.removeItem('oss_auth_token_cache')
                      localStorage.removeItem('oss_auth_token')
                      console.log(`[Auth ${authId}] Cleared all cached auth data`)
                    } catch (cacheError) {
                      console.warn(`[Auth ${authId}] Error clearing cached auth state:`, cacheError)
                    }
                  }

                  // Show notification
                  toast.info('You have been signed out', {
                    autoClose: 3000,
                    position: 'top-right'
                  })

                  // Redirect to login if on protected route
                  if (isProtectedAdminRoute()) {
                    console.log(`[AuthContext:${authId}] onAuthStateChange: Event ${event} - Condition met to redirect to login (signed out, protected route). Path: ${router.pathname}`);
                    redirectToLogin()
                  }
                }
              } else if (event === 'USER_UPDATED') {
                if (session && mounted) {
                  try {
                    const { user: updatedUser, role: updatedRole } = await getCurrentUser()
                    setUser(updatedUser)
                    setRole(updatedRole)
                  } catch (updateError) {
                    console.error(`[Auth ${authId}] Error updating user after USER_UPDATED:`, updateError)
                  }
                }
              }
            }
          )

          authListener = subscription
          console.log(`[Auth ${authId}] Auth subscription set up successfully`)
        } catch (subscriptionError) {
          console.error(`[Auth ${authId}] Error setting up auth subscription:`, subscriptionError)
          if (mounted) setError(subscriptionError)
        }
      } catch (err) {
        console.error(`[Auth ${authId}] Failed to initialize auth:`, err)
        if (mounted) setError(err)
      } finally {
        // Clear the production timeout if it exists
        if (authTimeout) {
          clearTimeout(authTimeout)
        }

        // Always set loading to false to prevent UI from being stuck
        if (mounted) {
          // console.log(`[Auth ${authId}] Auth initialization complete, setting loading to false`) // Original log
          setLoading(false)
          // Mark auth as complete for performance monitoring
          if (typeof window !== 'undefined') {
            window.__AUTH_COMPLETE__ = true
          }
          console.log(`[AuthContext:${authId}] initializeAuth: END, Path: ${router.pathname}, User: ${user?.email}, Role: ${role}, Loading: ${loading}`);
        }
      }
    }

    // Start the initialization process
    initializeAuth()

    // Cleanup subscription on unmount
    return () => {
      console.log(`[Auth ${authId}] Cleaning up auth context`)
      mounted = false

      // Clear the production timeout if it exists
      if (authTimeout) {
        clearTimeout(authTimeout)
        console.log(`[Auth ${authId}] Cleared auth timeout`)
      }

      // Clear token refresh interval
      if (tokenRefreshInterval) {
        clearInterval(tokenRefreshInterval)
        console.log(`[Auth ${authId}] Cleared token refresh interval`)
      }

      if (authListener) {
        try {
          console.log(`[Auth ${authId}] Unsubscribing from auth changes`)
          authListener.unsubscribe()
        } catch (cleanupError) {
          console.error(`[Auth ${authId}] Error during auth subscription cleanup:`, cleanupError)
        }
      }

      // Clear the auth instance reference
      if (authInstanceRef.current === authId) {
        authInstanceRef.current = null
      }

      // Clear any cached auth state on cleanup to prevent stale data
      if (typeof window !== 'undefined') {
        try {
          // Only clear if this is the active auth instance
          const cachedAuth = sessionStorage.getItem('oss_auth_state')
          if (cachedAuth) {
            const { sessionId } = JSON.parse(cachedAuth)
            // Clear if this matches our session or if no sessionId
            if (!sessionId || sessionId === authId.substring(0, 10)) {
              sessionStorage.removeItem('oss_auth_state')
              console.log(`[Auth ${authId}] Cleared cached auth state on cleanup`)
            }
          }
        } catch (cacheError) {
          console.warn(`[Auth ${authId}] Error clearing cached auth state on cleanup:`, cacheError)
        }
      }
    }
  }, [router.pathname, router.isReady]) // More stable dependencies

  // Sign in handler using Supabase directly (memoized for performance)
  const signIn = useCallback(async (email, password) => {
    console.log(`[AuthContext] signIn: Attempting for ${email}`);
    setLoading(true)
    setError(null)

    // console.log('[AuthContext] Starting sign in process for:', email) // Original log

    try {
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (signInError) {
        console.error('[AuthContext] signIn: Supabase signInError:', signInError);
        throw signInError
      } else {
        console.log('[AuthContext] signIn: Supabase signIn successful.');
      }

      // console.log('[AuthContext] Authentication successful, fetching user role...') // Original log

      // Get user role after successful sign in with retry logic
      let retryCount = 0
      const maxRetries = 3
      let currentUser, userRole

      console.log('[AuthContext] signIn: Calling getCurrentUser in loop...');
      while (retryCount < maxRetries) {
        try {
          const result = await getCurrentUser()
          currentUser = result.user
          userRole = result.role
          console.log(`[AuthContext] signIn loop: getCurrentUser attempt ${retryCount + 1} - User: ${currentUser?.email}, Role: ${userRole}`);

          if (currentUser && userRole) {
            // console.log(`[AuthContext] User role fetched successfully: ${userRole}`) // Original log
            break
          } else if (retryCount < maxRetries - 1) {
            // console.log(`[AuthContext] Role not found, retrying... (${retryCount + 1}/${maxRetries})`) // Original log
            await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second before retry
            retryCount++
          } else {
            throw new Error('Failed to fetch user role after multiple attempts')
          }
        } catch (roleError) {
          // console.error(`[AuthContext] Error fetching user role (attempt ${retryCount + 1}):`, roleError) // Original log
          if (retryCount < maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000))
            retryCount++
          } else {
            throw roleError
          }
        }
      }

      // console.log('[AuthContext] Sign in completed successfully') // Original log

      // Update the context state immediately
      console.log(`[AuthContext] signIn: Setting user to ${currentUser?.email}, role to ${userRole} after successful sign in.`);
      setUser(currentUser)
      setRole(userRole)
      setError(null)

      return {
        data: {
          ...data,
          user: currentUser,
          role: userRole
        },
        error: null
      }
    } catch (err) {
      console.error('[AuthContext] signIn: CATCH block error:', err);
      setError(err)
      return { data: null, error: err }
    } finally {
      setLoading(false)
      console.log('[AuthContext] signIn: FINALLY block.');
    }
  }, []) // Empty dependency array since signIn doesn't depend on state

  // Sign out handler using Supabase directly (memoized for performance)
  const signOut = useCallback(async () => {
    setLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error

      setUser(null)
      setRole(null)
      setError(null)

      // Clear cached auth state
      if (typeof window !== 'undefined') {
        try {
          sessionStorage.removeItem('oss_auth_state')
          sessionStorage.removeItem('oss_auth_token_cache')
          console.log('Cleared cached auth state on sign out')
        } catch (cacheError) {
          console.warn('Error clearing cached auth state:', cacheError)
        }
      }
    } catch (err) {
      console.error('Sign out error:', err)
      setError(err)
    } finally {
      setLoading(false)
    }
  }, []) // Empty dependency array since signOut doesn't depend on state

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    role,
    loading,
    error,
    isAuthenticated: !!user,
    // Enhanced role checking functions
    isDev: role === 'dev',
    isAdmin: role === 'admin',
    isArtist: role === 'artist',
    isBraider: role === 'braider',
    isUser: role === 'user',
    // Legacy compatibility
    isStaff: ['artist', 'braider'].includes(role),
    // Permission checking functions
    hasAdminAccess: ['dev', 'admin'].includes(role),
    hasStaffAccess: ['dev', 'admin', 'artist', 'braider'].includes(role),
    hasFullAccess: ['dev', 'admin'].includes(role),
    signIn,
    signOut,
  }), [user, role, loading, error, signIn, signOut])

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
