/**
 * Privacy Dashboard Component for Ocean Soul Sparkles
 * Customer privacy controls and consent management interface
 * 
 * Phase 9.2: Data Protection & Privacy Compliance
 */

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import { 
  ShieldCheckIcon, 
  CogIcon, 
  EyeIcon, 
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

export default function PrivacyDashboard({ user, customer }) {
  const [privacyData, setPrivacyData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [activeTab, setActiveTab] = useState('consents')

  useEffect(() => {
    fetchPrivacyData()
  }, [user, customer])

  const fetchPrivacyData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/compliance/privacy/dashboard', {
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        method: 'POST',
        body: JSON.stringify({
          userId: user?.id,
          customerId: customer?.id
        })
      })

      if (response.ok) {
        const data = await response.json()
        setPrivacyData(data)
      } else {
        toast.error('Failed to load privacy data')
      }
    } catch (error) {
      console.error('Error fetching privacy data:', error)
      toast.error('Failed to load privacy data')
    } finally {
      setLoading(false)
    }
  }

  const updateConsent = async (consentType, consentGiven) => {
    try {
      setUpdating(true)
      const response = await fetch('/api/compliance/consent/update', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user?.id,
          customerId: customer?.id,
          consentType,
          consentGiven,
          consentMethod: 'explicit',
          consentSource: 'privacy_dashboard'
        })
      })

      if (response.ok) {
        toast.success(`${consentGiven ? 'Granted' : 'Withdrawn'} consent for ${consentType}`)
        await fetchPrivacyData()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update consent')
      }
    } catch (error) {
      console.error('Error updating consent:', error)
      toast.error('Failed to update consent')
    } finally {
      setUpdating(false)
    }
  }

  const updateCookieConsent = async (cookieData) => {
    try {
      setUpdating(true)
      const response = await fetch('/api/compliance/cookies/update', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user?.id,
          customerId: customer?.id,
          ...cookieData
        })
      })

      if (response.ok) {
        toast.success('Cookie preferences updated')
        await fetchPrivacyData()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update cookie preferences')
      }
    } catch (error) {
      console.error('Error updating cookie consent:', error)
      toast.error('Failed to update cookie preferences')
    } finally {
      setUpdating(false)
    }
  }

  const updatePrivacyPreference = async (category, key, value) => {
    try {
      setUpdating(true)
      const response = await fetch('/api/compliance/privacy/preference', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user?.id,
          customerId: customer?.id,
          preferenceCategory: category,
          preferenceKey: key,
          preferenceValue: value
        })
      })

      if (response.ok) {
        toast.success('Privacy preference updated')
        await fetchPrivacyData()
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to update preference')
      }
    } catch (error) {
      console.error('Error updating privacy preference:', error)
      toast.error('Failed to update preference')
    } finally {
      setUpdating(false)
    }
  }

  const requestDataExport = async () => {
    try {
      const response = await fetch('/api/compliance/gdpr/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          requestType: 'access',
          requesterEmail: user?.email || customer?.email,
          requesterName: user?.user_metadata?.name || customer?.name,
          userId: user?.id,
          customerId: customer?.id
        })
      })

      if (response.ok) {
        const data = await response.json()
        toast.success('Data export request submitted. You will receive a verification email.')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to request data export')
      }
    } catch (error) {
      console.error('Error requesting data export:', error)
      toast.error('Failed to request data export')
    }
  }

  const requestDataDeletion = async () => {
    if (!confirm('Are you sure you want to request deletion of your personal data? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch('/api/compliance/gdpr/delete', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          requestType: 'erasure',
          requesterEmail: user?.email || customer?.email,
          requesterName: user?.user_metadata?.name || customer?.name,
          userId: user?.id,
          customerId: customer?.id
        })
      })

      if (response.ok) {
        toast.success('Data deletion request submitted. You will receive a verification email.')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to request data deletion')
      }
    } catch (error) {
      console.error('Error requesting data deletion:', error)
      toast.error('Failed to request data deletion')
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!privacyData) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Privacy Data Unavailable</h3>
          <p className="text-gray-600">Unable to load your privacy settings. Please try again later.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center">
          <ShieldCheckIcon className="h-6 w-6 text-blue-500 mr-2" />
          <h2 className="text-lg font-medium text-gray-900">Privacy & Data Control</h2>
        </div>
        <p className="text-sm text-gray-600 mt-1">
          Manage your privacy preferences and data consent settings
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {[
            { id: 'consents', name: 'Consent Management', icon: CheckCircleIcon },
            { id: 'cookies', name: 'Cookie Preferences', icon: CogIcon },
            { id: 'preferences', name: 'Privacy Settings', icon: EyeIcon },
            { id: 'rights', name: 'Data Rights', icon: DocumentTextIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'consents' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Consent Management</h3>
              <p className="text-sm text-gray-600 mb-6">
                Control how we use your personal data. You can withdraw consent at any time.
              </p>
            </div>

            <div className="space-y-4">
              {Object.entries(privacyData.consentTypes).map(([type, config]) => {
                const consent = privacyData.consents[type]
                return (
                  <div key={type} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{config.name}</h4>
                      <p className="text-sm text-gray-600">{config.description}</p>
                      {config.required && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                          Required
                        </span>
                      )}
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => updateConsent(type, !consent.consent_given)}
                        disabled={updating || config.required}
                        className={`${
                          consent.consent_given
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        } ${
                          config.required ? 'opacity-50 cursor-not-allowed' : 'hover:bg-opacity-80'
                        } px-3 py-1 rounded-full text-sm font-medium transition-colors`}
                      >
                        {consent.consent_given ? 'Granted' : 'Withdrawn'}
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {activeTab === 'cookies' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Cookie Preferences</h3>
              <p className="text-sm text-gray-600 mb-6">
                Choose which types of cookies you want to allow on our website.
              </p>
            </div>

            <div className="space-y-4">
              {Object.entries(privacyData.cookieCategories).map(([category, config]) => {
                const isEnabled = privacyData.cookieConsent[`${category}_cookies`]
                return (
                  <div key={category} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{config.name}</h4>
                      <p className="text-sm text-gray-600 mb-2">{config.description}</p>
                      <div className="text-xs text-gray-500">
                        Examples: {config.examples.join(', ')}
                      </div>
                      {config.required && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                          Required
                        </span>
                      )}
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => updateCookieConsent({
                          [`${category}_cookies`]: !isEnabled,
                          essential_cookies: true, // Always keep essential
                          functional_cookies: category === 'functional' ? !isEnabled : privacyData.cookieConsent.functional_cookies,
                          analytics_cookies: category === 'analytics' ? !isEnabled : privacyData.cookieConsent.analytics_cookies,
                          marketing_cookies: category === 'marketing' ? !isEnabled : privacyData.cookieConsent.marketing_cookies
                        })}
                        disabled={updating || config.required}
                        className={`${
                          isEnabled
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        } ${
                          config.required ? 'opacity-50 cursor-not-allowed' : 'hover:bg-opacity-80'
                        } px-3 py-1 rounded-full text-sm font-medium transition-colors`}
                      >
                        {isEnabled ? 'Enabled' : 'Disabled'}
                      </button>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {activeTab === 'preferences' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Privacy Settings</h3>
              <p className="text-sm text-gray-600 mb-6">
                Customize how we use your data to personalize your experience.
              </p>
            </div>

            {Object.entries(privacyData.preferences).map(([category, categoryData]) => (
              <div key={category} className="space-y-4">
                <h4 className="text-md font-medium text-gray-900">{categoryData.name}</h4>
                <div className="space-y-3">
                  {Object.entries(categoryData.preferences).map(([key, prefData]) => (
                    <div key={key} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{prefData.description}</p>
                        {prefData.updated_at && (
                          <p className="text-xs text-gray-500 mt-1">
                            Last updated: {new Date(prefData.updated_at).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <div className="ml-4">
                        <button
                          onClick={() => updatePrivacyPreference(category, key, !prefData.value)}
                          disabled={updating}
                          className={`${
                            prefData.value
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          } hover:bg-opacity-80 px-3 py-1 rounded-full text-sm font-medium transition-colors`}
                        >
                          {prefData.value ? 'Enabled' : 'Disabled'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'rights' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Your Data Rights</h3>
              <p className="text-sm text-gray-600 mb-6">
                Under data protection laws, you have rights regarding your personal data.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Export Your Data</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Download a copy of all your personal data we have on file.
                </p>
                <button
                  onClick={requestDataExport}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Request Data Export
                </button>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Delete Your Data</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Request deletion of your personal data (subject to legal requirements).
                </p>
                <button
                  onClick={requestDataDeletion}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"
                >
                  Request Data Deletion
                </button>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Important Information</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Data requests require email verification and may take up to 30 days to process. 
                    Some data may be retained for legal or business purposes even after deletion requests.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
