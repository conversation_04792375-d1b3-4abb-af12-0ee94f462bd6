-- Migration: Create artist_weekly_availability table
-- Timestamp: 2024-07-01 12:00:00 UTC

BEGIN;

-- Table to store recurring weekly availability for artists
CREATE TABLE IF NOT EXISTS public.artist_weekly_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID NOT NULL REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  day_of_week INT NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, ..., 6=Saturday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT check_end_time_after_start_time CHECK (end_time > start_time)
);

-- Add comments for clarity
COMMENT ON TABLE public.artist_weekly_availability IS 'Stores recurring weekly availability time slots for artists (e.g., Mondays 9-5, Wednesdays 10-2).';
COMMENT ON COLUMN public.artist_weekly_availability.artist_id IS 'Foreign key referencing the artist_profiles table.';
COMMENT ON COLUMN public.artist_weekly_availability.day_of_week IS 'Day of the week: 0 for Sunday, 1 for Monday, ..., 6 for Saturday.';
COMMENT ON COLUMN public.artist_weekly_availability.start_time IS 'Start time of the availability slot.';
COMMENT ON COLUMN public.artist_weekly_availability.end_time IS 'End time of the availability slot.';

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_artist_weekly_availability_artist_id ON public.artist_weekly_availability(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_weekly_availability_artist_day ON public.artist_weekly_availability(artist_id, day_of_week);

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row update
CREATE TRIGGER set_artist_weekly_availability_updated_at
BEFORE UPDATE ON public.artist_weekly_availability
FOR EACH ROW
EXECUTE FUNCTION public.trigger_set_timestamp();

COMMIT;
