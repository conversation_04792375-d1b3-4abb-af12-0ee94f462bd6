import { useState, useEffect } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import LoadingButton from '@/components/admin/LoadingButton';
import styles from '@/styles/admin/PaymentEditForm.module.css';

/**
 * PaymentEditForm Component
 * 
 * Form for editing payment details following Ocean Soul Sparkles admin patterns
 * 
 * @param {Object} props - Component props
 * @param {Object} props.payment - Payment object to edit
 * @param {Function} props.onSave - Callback when payment is successfully saved
 * @param {Function} props.onCancel - Callback when form is cancelled
 */
export default function PaymentEditForm({ payment, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    payment_status: '',
    payment_method: '',
    amount: '',
    payment_date: '',
    notes: '',
    transaction_id: ''
  });

  const [formErrors, setFormErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Payment status options
  const paymentStatusOptions = [
    { value: 'completed', label: 'Completed' },
    { value: 'pending', label: 'Pending' },
    { value: 'failed', label: 'Failed' },
    { value: 'cancelled', label: 'Cancelled' },
    { value: 'refunded', label: 'Refunded' }
  ];

  // Payment method options
  const paymentMethodOptions = [
    { value: 'cash', label: 'Cash' },
    { value: 'square_terminal', label: 'Square Terminal' },
    { value: 'square_reader', label: 'Square Reader' },
    { value: 'square_manual', label: 'Square Manual Entry' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'other', label: 'Other' }
  ];

  // Initialize form data when payment prop changes
  useEffect(() => {
    if (payment) {
      // Format payment date for datetime-local input
      const paymentDate = payment.payment_date 
        ? new Date(payment.payment_date).toISOString().slice(0, 16)
        : '';

      setFormData({
        payment_status: payment.payment_status || '',
        payment_method: payment.payment_method || '',
        amount: payment.amount ? payment.amount.toString() : '',
        payment_date: paymentDate,
        notes: payment.notes || '',
        transaction_id: payment.transaction_id || ''
      });
    }
  }, [payment]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form fields
  const validateForm = () => {
    const errors = {};

    // Required fields
    if (!formData.payment_status?.trim()) {
      errors.payment_status = 'Payment status is required';
    }

    if (!formData.payment_method?.trim()) {
      errors.payment_method = 'Payment method is required';
    }

    if (!formData.amount?.trim()) {
      errors.amount = 'Payment amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        errors.amount = 'Please enter a valid amount greater than 0';
      }
    }

    if (!formData.payment_date?.trim()) {
      errors.payment_date = 'Payment date is required';
    }

    // Optional field validation
    if (formData.transaction_id && formData.transaction_id.length > 255) {
      errors.transaction_id = 'Transaction ID is too long (max 255 characters)';
    }

    if (formData.notes && formData.notes.length > 1000) {
      errors.notes = 'Notes are too long (max 1000 characters)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setSaving(true);

    try {
      // Prepare update data
      const updateData = {
        payment_status: formData.payment_status,
        payment_method: formData.payment_method,
        amount: parseFloat(formData.amount),
        payment_date: new Date(formData.payment_date).toISOString(),
        notes: formData.notes || null,
        transaction_id: formData.transaction_id || null
      };

      console.log('Updating payment:', payment.id, updateData);

      // Send update request
      const response = await authenticatedFetch(`/api/admin/payments/${payment.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.payment) {
        toast.success('Payment updated successfully');
        onSave(response.payment);
      } else {
        throw new Error('Invalid response from server');
      }

    } catch (error) {
      console.error('Error updating payment:', error);
      toast.error(error.message || 'Failed to update payment');
    } finally {
      setSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    onCancel();
  };

  return (
    <div className={styles.paymentEditForm}>
      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Payment Status */}
        <div className={styles.formGroup}>
          <label htmlFor="payment_status" className={styles.label}>
            Payment Status *
          </label>
          <select
            id="payment_status"
            name="payment_status"
            value={formData.payment_status}
            onChange={handleChange}
            className={`${styles.select} ${formErrors.payment_status ? styles.fieldError : ''}`}
            disabled={saving}
          >
            <option value="">Select payment status</option>
            {paymentStatusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {formErrors.payment_status && (
            <span className={styles.errorText}>{formErrors.payment_status}</span>
          )}
        </div>

        {/* Payment Method */}
        <div className={styles.formGroup}>
          <label htmlFor="payment_method" className={styles.label}>
            Payment Method *
          </label>
          <select
            id="payment_method"
            name="payment_method"
            value={formData.payment_method}
            onChange={handleChange}
            className={`${styles.select} ${formErrors.payment_method ? styles.fieldError : ''}`}
            disabled={saving}
          >
            <option value="">Select payment method</option>
            {paymentMethodOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {formErrors.payment_method && (
            <span className={styles.errorText}>{formErrors.payment_method}</span>
          )}
        </div>

        {/* Amount and Date Row */}
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="amount" className={styles.label}>
              Payment Amount *
            </label>
            <input
              type="number"
              id="amount"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              placeholder="0.00"
              className={`${styles.input} ${formErrors.amount ? styles.fieldError : ''}`}
              disabled={saving}
            />
            {formErrors.amount && (
              <span className={styles.errorText}>{formErrors.amount}</span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="payment_date" className={styles.label}>
              Payment Date *
            </label>
            <input
              type="datetime-local"
              id="payment_date"
              name="payment_date"
              value={formData.payment_date}
              onChange={handleChange}
              className={`${styles.input} ${formErrors.payment_date ? styles.fieldError : ''}`}
              disabled={saving}
            />
            {formErrors.payment_date && (
              <span className={styles.errorText}>{formErrors.payment_date}</span>
            )}
          </div>
        </div>

        {/* Transaction ID */}
        <div className={styles.formGroup}>
          <label htmlFor="transaction_id" className={styles.label}>
            External Transaction ID
          </label>
          <input
            type="text"
            id="transaction_id"
            name="transaction_id"
            value={formData.transaction_id}
            onChange={handleChange}
            placeholder="External payment system transaction ID"
            className={`${styles.input} ${formErrors.transaction_id ? styles.fieldError : ''}`}
            disabled={saving}
          />
          {formErrors.transaction_id && (
            <span className={styles.errorText}>{formErrors.transaction_id}</span>
          )}
          <small className={styles.helpText}>
            Transaction ID from Square, bank, or other payment processor
          </small>
        </div>

        {/* Notes */}
        <div className={styles.formGroup}>
          <label htmlFor="notes" className={styles.label}>
            Payment Notes
          </label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={4}
            placeholder="Additional notes about this payment..."
            className={`${styles.textarea} ${formErrors.notes ? styles.fieldError : ''}`}
            disabled={saving}
          />
          {formErrors.notes && (
            <span className={styles.errorText}>{formErrors.notes}</span>
          )}
          <small className={styles.helpText}>
            Internal notes about the payment (max 1000 characters)
          </small>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleCancel}
            className={styles.cancelButton}
            disabled={saving}
          >
            Cancel
          </button>
          <LoadingButton
            type="submit"
            loading={saving}
            className={styles.saveButton}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </LoadingButton>
        </div>
      </form>
    </div>
  );
}
