-- Migration: Create artist_availability_exceptions table
-- Timestamp: 20250606090835

BEGIN;

-- Table to store specific date exceptions to an artist's regular availability
CREATE TABLE IF NOT EXISTS public.artist_availability_exceptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID NOT NULL REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  exception_date DATE NOT NULL,
  exception_type TEXT NOT NULL CHECK (exception_type IN ('Unavailable', 'Custom Hours', 'Additional Break')), -- Type of exception
  start_time TIME, -- Required if type is 'Custom Hours' or 'Additional Break'
  end_time TIME,   -- Required if type is 'Custom Hours' or 'Additional Break'
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (artist_id, exception_date) -- Assuming one exception entry per artist per day. If multiple distinct exceptions per day are needed, this constraint needs adjustment.
);

-- Add a comment to the new table
COMMENT ON TABLE public.artist_availability_exceptions IS 'Stores date-specific exceptions to an artist''s regular weekly availability, such as days off, custom hours for a specific date, or additional break times.';

-- Add comments to columns for clarity
COMMENT ON COLUMN public.artist_availability_exceptions.artist_id IS 'Foreign key referencing the artist_profiles table.';
COMMENT ON COLUMN public.artist_availability_exceptions.exception_date IS 'The specific date for which this exception applies.';
COMMENT ON COLUMN public.artist_availability_exceptions.exception_type IS 'Type of exception: "Unavailable", "Custom Hours", "Additional Break".';
COMMENT ON COLUMN public.artist_availability_exceptions.start_time IS 'Start time for the exception, if applicable (e.g., for Custom Hours or Additional Break).';
COMMENT ON COLUMN public.artist_availability_exceptions.end_time IS 'End time for the exception, if applicable (e.g., for Custom Hours or Additional Break).';
COMMENT ON COLUMN public.artist_availability_exceptions.notes IS 'Optional notes regarding the exception.';

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_artist_availability_exceptions_artist_id ON public.artist_availability_exceptions(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_availability_exceptions_date ON public.artist_availability_exceptions(exception_date);
CREATE INDEX IF NOT EXISTS idx_artist_availability_exceptions_artist_date ON public.artist_availability_exceptions(artist_id, exception_date);

COMMIT;
