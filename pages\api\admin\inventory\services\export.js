import { supabaseAdmin, getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';

/**
 * API endpoint for exporting services data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response or file download
 */
export default async function handler(req, res) {
  // Generate request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Services export request started`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log(`[${requestId}] Invalid method: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Log environment and configuration
  console.log(`[${requestId}] Environment: ${process.env.NODE_ENV}`);
  console.log(`[${requestId}] Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing'}`);
  console.log(`[${requestId}] Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing'}`);

  // Authenticate request with enhanced error handling
  let authorized = false;
  let user = null;
  let role = null;
  let authError = null;

  try {
    // Development bypass for testing
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${requestId}] Development mode: bypassing authentication`);
      authorized = true;
      user = { id: 'dev-user', email: '<EMAIL>' };
      role = 'admin';
    } else {
      // Normal authentication
      console.log(`[${requestId}] Attempting authentication...`);
      const authResult = await authenticateAdminRequest(req);
      authorized = authResult.authorized;
      user = authResult.user;
      role = authResult.role;
      authError = authResult.error;

      console.log(`[${requestId}] Authentication result: authorized=${authorized}, role=${role}, user=${user?.email}`);
    }
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    authError = error;
    authorized = false;
  }

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, authError?.message);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authError?.message || 'Authentication failed',
      requestId
    });
  }

  try {
    const { format = 'csv', category, status, featured } = req.query;
    console.log(`[${requestId}] Export parameters: format=${format}, category=${category}, status=${status}, featured=${featured}`);

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      console.log(`[${requestId}] Invalid format: ${format}`);
      return res.status(400).json({
        error: 'Invalid format. Supported formats: csv, json',
        requestId
      });
    }

    // Initialize admin client with enhanced error handling
    let adminClient;
    try {
      console.log(`[${requestId}] Initializing admin client...`);
      adminClient = getAdminClient();
      console.log(`[${requestId}] Admin client initialized successfully`);
    } catch (clientError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, clientError);

      // Fallback to supabaseAdmin if available
      if (supabaseAdmin) {
        console.log(`[${requestId}] Falling back to supabaseAdmin`);
        adminClient = supabaseAdmin;
      } else {
        console.error(`[${requestId}] No admin client available`);
        return res.status(500).json({
          error: 'Database connection failed',
          message: 'Unable to initialize database client',
          requestId
        });
      }
    }

    // Build query with enhanced logging - only selecting existing columns
    console.log(`[${requestId}] Building services query...`);
    let query = adminClient
      .from('services')
      .select(`
        id,
        name,
        description,
        duration,
        price,
        color,
        category,
        category_id,
        image_url,
        status,
        featured,
        visible_on_public,
        visible_on_pos,
        visible_on_events,
        created_at,
        updated_at
      `)
      .order('name');

    // Apply filters with logging
    if (category && category !== 'all') {
      console.log(`[${requestId}] Applying category filter: ${category}`);
      query = query.eq('category', category);
    }

    if (status && status !== 'all') {
      console.log(`[${requestId}] Applying status filter: ${status}`);
      query = query.eq('status', status);
    }

    if (featured === 'true') {
      console.log(`[${requestId}] Applying featured filter: true`);
      query = query.eq('featured', true);
    } else if (featured === 'false') {
      console.log(`[${requestId}] Applying featured filter: false`);
      query = query.eq('featured', false);
    }

    // Execute query with timeout and enhanced error handling
    console.log(`[${requestId}] Executing services query...`);
    const queryStartTime = Date.now();

    const { data: services, error: queryError } = await Promise.race([
      query,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout after 30 seconds')), 30000)
      )
    ]);

    const queryDuration = Date.now() - queryStartTime;
    console.log(`[${requestId}] Query completed in ${queryDuration}ms`);

    if (queryError) {
      console.error(`[${requestId}] Database query error:`, {
        message: queryError.message,
        code: queryError.code,
        details: queryError.details,
        hint: queryError.hint
      });

      return res.status(500).json({
        error: 'Failed to fetch services data',
        message: queryError.message,
        code: queryError.code,
        requestId
      });
    }

    if (!services) {
      console.warn(`[${requestId}] Query returned null/undefined data`);
      return res.status(500).json({
        error: 'No data returned from query',
        requestId
      });
    }

    console.log(`[${requestId}] Successfully fetched ${services.length} services`);


    // Process data for export - only including existing columns
    const exportData = services.map(service => ({
      id: service.id,
      name: service.name || '',
      description: service.description || '',
      duration: service.duration || 0,
      price: service.price || 0,
      color: service.color || '#6a0dad',
      category: service.category || '',
      category_id: service.category_id || '',
      image_url: service.image_url || '',
      status: service.status || 'active',
      featured: service.featured || false,
      visible_on_public: service.visible_on_public !== false,
      visible_on_pos: service.visible_on_pos !== false,
      visible_on_events: service.visible_on_events !== false,
      created_at: service.created_at,
      updated_at: service.updated_at
    }));

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `services_export_${timestamp}`;

    if (format === 'json') {
      // Return JSON format with proper headers
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).json(exportData);
    } else {
      // Return CSV format with proper headers - only existing columns
      const csvFields = [
        'id', 'name', 'description', 'duration', 'price', 'color', 'category',
        'category_id', 'image_url', 'status', 'featured', 'visible_on_public',
        'visible_on_pos', 'visible_on_events', 'created_at', 'updated_at'
      ];

      const json2csvParser = new Parser({ fields: csvFields });
      const csv = json2csvParser.parse(exportData);

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).send(csv);
    }

  } catch (error) {
    console.error(`[${requestId}] Critical error in services export:`, {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    return res.status(500).json({
      error: 'Failed to export services',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      requestId
    });
  }
}
