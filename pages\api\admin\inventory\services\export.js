import { supabaseAdmin, getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';

/**
 * API endpoint for exporting services data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response or file download
 */
export default async function handler(req, res) {
  // Generate request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Services export request started`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log(`[${requestId}] Invalid method: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Log environment and configuration
  console.log(`[${requestId}] Environment: ${process.env.NODE_ENV}`);
  console.log(`[${requestId}] Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing'}`);
  console.log(`[${requestId}] Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing'}`);

  // Authenticate request with enhanced error handling
  let authorized = false;
  let user = null;
  let role = null;
  let authError = null;

  try {
    // Development bypass for testing
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${requestId}] Development mode: bypassing authentication`);
      authorized = true;
      user = { id: 'dev-user', email: '<EMAIL>' };
      role = 'admin';
    } else {
      // Normal authentication
      console.log(`[${requestId}] Attempting authentication...`);
      const authResult = await authenticateAdminRequest(req);
      authorized = authResult.authorized;
      user = authResult.user;
      role = authResult.role;
      authError = authResult.error;

      console.log(`[${requestId}] Authentication result: authorized=${authorized}, role=${role}, user=${user?.email}`);
    }
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    authError = error;
    authorized = false;
  }

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, authError?.message);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authError?.message || 'Authentication failed',
      requestId
    });
  }

  try {
    const { format = 'csv', category, status, featured } = req.query;
    console.log(`[${requestId}] Export parameters: format=${format}, category=${category}, status=${status}, featured=${featured}`);

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      console.log(`[${requestId}] Invalid format: ${format}`);
      return res.status(400).json({
        error: 'Invalid format. Supported formats: csv, json',
        requestId
      });
    }

    // Initialize admin client with enhanced error handling
    let adminClient;
    try {
      console.log(`[${requestId}] Initializing admin client...`);
      adminClient = getAdminClient();
      console.log(`[${requestId}] Admin client initialized successfully`);
    } catch (clientError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, clientError);

      // Fallback to supabaseAdmin if available
      if (supabaseAdmin) {
        console.log(`[${requestId}] Falling back to supabaseAdmin`);
        adminClient = supabaseAdmin;
      } else {
        console.error(`[${requestId}] No admin client available`);
        return res.status(500).json({
          error: 'Database connection failed',
          message: 'Unable to initialize database client',
          requestId
        });
      }
    }

    // Build enhanced query with JOINs for categories and pricing tiers
    console.log(`[${requestId}] Building enhanced services query with tiers and categories...`);

    // Use the services_with_pricing view which already includes pricing_tiers as JSON
    let query = adminClient
      .from('services_with_pricing')
      .select(`
        id,
        name,
        description,
        duration,
        price,
        color,
        category,
        category_id,
        image_url,
        status,
        featured,
        visible_on_public,
        visible_on_pos,
        visible_on_events,
        pricing_tiers,
        created_at,
        updated_at
      `)
      .order('name');

    // Apply filters with logging
    if (category && category !== 'all') {
      console.log(`[${requestId}] Applying category filter: ${category}`);
      query = query.eq('category', category);
    }

    if (status && status !== 'all') {
      console.log(`[${requestId}] Applying status filter: ${status}`);
      query = query.eq('status', status);
    }

    if (featured === 'true') {
      console.log(`[${requestId}] Applying featured filter: true`);
      query = query.eq('featured', true);
    } else if (featured === 'false') {
      console.log(`[${requestId}] Applying featured filter: false`);
      query = query.eq('featured', false);
    }

    // Execute query with timeout and enhanced error handling
    console.log(`[${requestId}] Executing services query...`);
    const queryStartTime = Date.now();

    const { data: services, error: queryError } = await Promise.race([
      query,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout after 30 seconds')), 30000)
      )
    ]);

    const queryDuration = Date.now() - queryStartTime;
    console.log(`[${requestId}] Query completed in ${queryDuration}ms`);

    if (queryError) {
      console.error(`[${requestId}] Database query error:`, {
        message: queryError.message,
        code: queryError.code,
        details: queryError.details,
        hint: queryError.hint
      });

      return res.status(500).json({
        error: 'Failed to fetch services data',
        message: queryError.message,
        code: queryError.code,
        requestId
      });
    }

    if (!services) {
      console.warn(`[${requestId}] Query returned null/undefined data`);
      return res.status(500).json({
        error: 'No data returned from query',
        requestId
      });
    }

    console.log(`[${requestId}] Successfully fetched ${services.length} services`);

    // Fetch category details for all services
    console.log(`[${requestId}] Fetching category details...`);
    const categoryIds = [...new Set(services.map(s => s.category_id).filter(Boolean))];

    let categories = {};
    if (categoryIds.length > 0) {
      const { data: categoryData, error: categoryError } = await adminClient
        .from('service_categories')
        .select(`
          id,
          name,
          description,
          parent_id
        `)
        .in('id', categoryIds);

      if (categoryError) {
        console.error(`[${requestId}] Category query error:`, categoryError);
        // Continue without category details rather than failing
      } else {
        // Create category lookup map
        categoryData.forEach(cat => {
          categories[cat.id] = cat;
        });

        // Fetch parent category names if any categories have parents
        const parentIds = [...new Set(categoryData.map(c => c.parent_id).filter(Boolean))];
        if (parentIds.length > 0) {
          const { data: parentData, error: parentError } = await adminClient
            .from('service_categories')
            .select('id, name')
            .in('id', parentIds);

          if (!parentError) {
            const parentLookup = {};
            parentData.forEach(parent => {
              parentLookup[parent.id] = parent.name;
            });

            // Add parent names to categories
            Object.values(categories).forEach(cat => {
              if (cat.parent_id && parentLookup[cat.parent_id]) {
                cat.parent_name = parentLookup[cat.parent_id];
              }
            });
          }
        }

        console.log(`[${requestId}] Successfully fetched ${Object.keys(categories).length} categories`);
      }
    }


    // Process data for export with enhanced tier and category information
    console.log(`[${requestId}] Processing enhanced export data...`);

    const exportData = services.map(service => {
      // Parse pricing tiers (comes as JSON from the view)
      let pricingTiers = [];
      try {
        if (service.pricing_tiers) {
          if (typeof service.pricing_tiers === 'string') {
            pricingTiers = JSON.parse(service.pricing_tiers);
          } else if (Array.isArray(service.pricing_tiers)) {
            pricingTiers = service.pricing_tiers;
          }
        }
      } catch (error) {
        console.warn(`[${requestId}] Failed to parse pricing tiers for service ${service.id}:`, error);
        pricingTiers = [];
      }

      // Get category details
      const categoryDetails = categories[service.category_id] || {};

      // Calculate tier statistics
      const tierPrices = pricingTiers.map(t => t.price || 0).filter(p => p > 0);
      const tierDurations = pricingTiers.map(t => t.duration || 0).filter(d => d > 0);
      const defaultTier = pricingTiers.find(t => t.is_default) || pricingTiers[0];

      return {
        // Basic service information
        id: service.id,
        name: service.name || '',
        description: service.description || '',
        base_duration: service.duration || 0,
        base_price: service.price || 0,
        color: service.color || '#6a0dad',
        image_url: service.image_url || '',
        status: service.status || 'active',
        featured: service.featured || false,

        // Visibility settings
        visibility: {
          public: service.visible_on_public !== false,
          pos: service.visible_on_pos !== false,
          events: service.visible_on_events !== false
        },

        // Enhanced category information
        category: {
          id: service.category_id || '',
          name: service.category || categoryDetails.name || '',
          description: categoryDetails.description || '',
          parent_id: categoryDetails.parent_id || null,
          parent_name: categoryDetails.parent_name || null
        },

        // Pricing tier information
        pricing_tiers: pricingTiers.map(tier => ({
          id: tier.id || '',
          name: tier.name || '',
          description: tier.description || '',
          duration: tier.duration || 0,
          price: tier.price || 0,
          is_default: tier.is_default || false,
          sort_order: tier.sort_order || 0
        })),

        // Tier statistics
        tier_count: pricingTiers.length,
        price_range: {
          min: tierPrices.length > 0 ? Math.min(...tierPrices) : (service.price || 0),
          max: tierPrices.length > 0 ? Math.max(...tierPrices) : (service.price || 0),
          default: defaultTier?.price || service.price || 0
        },
        duration_range: {
          min: tierDurations.length > 0 ? Math.min(...tierDurations) : (service.duration || 0),
          max: tierDurations.length > 0 ? Math.max(...tierDurations) : (service.duration || 0),
          default: defaultTier?.duration || service.duration || 0
        },

        // Timestamps
        created_at: service.created_at,
        updated_at: service.updated_at
      };
    });

    console.log(`[${requestId}] Processed ${exportData.length} services with enhanced data`);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `services_export_${timestamp}`;

    // Get CSV style parameter (expanded, compact, or default)
    const csvStyle = req.query.style || 'compact';

    if (format === 'json') {
      // Return hierarchical JSON format with full tier and category data
      console.log(`[${requestId}] Returning JSON format with ${exportData.length} services`);
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      // Calculate total tiers across all services
      const totalTiers = exportData.reduce((sum, service) => sum + (service.tier_count || 0), 0);

      return res.status(200).json({
        export_info: {
          timestamp: new Date().toISOString(),
          total_services: exportData.length,
          total_tiers: totalTiers,
          format: 'hierarchical_json',
          version: '2.0'
        },
        services: exportData
      });
    } else {
      // Handle different CSV formats based on style parameter
      console.log(`[${requestId}] Returning CSV format (style: ${csvStyle})`);

      if (csvStyle === 'expanded') {
        // Expanded CSV: One row per service-tier combination
        const expandedData = [];
        exportData.forEach(service => {
          if (service.pricing_tiers.length === 0) {
            // Service with no tiers - use base service data
            expandedData.push({
              service_id: service.id,
              service_name: service.name,
              service_description: service.description,
              tier_id: '',
              tier_name: 'Base',
              tier_description: 'Base service pricing',
              tier_duration: service.base_duration,
              tier_price: service.base_price,
              tier_is_default: true,
              tier_sort_order: 0,
              category_id: service.category.id,
              category_name: service.category.name,
              category_description: service.category.description,
              parent_category_name: service.category.parent_name || '',
              service_color: service.color,
              service_status: service.status,
              service_featured: service.featured,
              visible_public: service.visibility.public,
              visible_pos: service.visibility.pos,
              visible_events: service.visibility.events,
              image_url: service.image_url,
              created_at: service.created_at,
              updated_at: service.updated_at
            });
          } else {
            // Service with tiers - one row per tier
            service.pricing_tiers.forEach(tier => {
              expandedData.push({
                service_id: service.id,
                service_name: service.name,
                service_description: service.description,
                tier_id: tier.id,
                tier_name: tier.name,
                tier_description: tier.description,
                tier_duration: tier.duration,
                tier_price: tier.price,
                tier_is_default: tier.is_default,
                tier_sort_order: tier.sort_order,
                category_id: service.category.id,
                category_name: service.category.name,
                category_description: service.category.description,
                parent_category_name: service.category.parent_name || '',
                service_color: service.color,
                service_status: service.status,
                service_featured: service.featured,
                visible_public: service.visibility.public,
                visible_pos: service.visibility.pos,
                visible_events: service.visibility.events,
                image_url: service.image_url,
                created_at: service.created_at,
                updated_at: service.updated_at
              });
            });
          }
        });

        const expandedFields = [
          'service_id', 'service_name', 'service_description',
          'tier_id', 'tier_name', 'tier_description', 'tier_duration', 'tier_price', 'tier_is_default', 'tier_sort_order',
          'category_id', 'category_name', 'category_description', 'parent_category_name',
          'service_color', 'service_status', 'service_featured',
          'visible_public', 'visible_pos', 'visible_events',
          'image_url', 'created_at', 'updated_at'
        ];

        const json2csvParser = new Parser({ fields: expandedFields });
        const csv = json2csvParser.parse(expandedData);

        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}_expanded.csv"`);
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        return res.status(200).send(csv);

      } else {
        // Compact CSV: One row per service with tier summary
        const compactData = exportData.map(service => ({
          id: service.id,
          name: service.name,
          description: service.description,
          base_duration: service.base_duration,
          base_price: service.base_price,
          color: service.color,
          category_id: service.category.id,
          category_name: service.category.name,
          category_description: service.category.description,
          parent_category_name: service.category.parent_name || '',
          tier_count: service.tier_count,
          price_min: service.price_range.min,
          price_max: service.price_range.max,
          price_default: service.price_range.default,
          duration_min: service.duration_range.min,
          duration_max: service.duration_range.max,
          duration_default: service.duration_range.default,
          pricing_tiers_json: JSON.stringify(service.pricing_tiers),
          status: service.status,
          featured: service.featured,
          visible_public: service.visibility.public,
          visible_pos: service.visibility.pos,
          visible_events: service.visibility.events,
          image_url: service.image_url,
          created_at: service.created_at,
          updated_at: service.updated_at
        }));

        const compactFields = [
          'id', 'name', 'description', 'base_duration', 'base_price', 'color',
          'category_id', 'category_name', 'category_description', 'parent_category_name',
          'tier_count', 'price_min', 'price_max', 'price_default',
          'duration_min', 'duration_max', 'duration_default', 'pricing_tiers_json',
          'status', 'featured', 'visible_public', 'visible_pos', 'visible_events',
          'image_url', 'created_at', 'updated_at'
        ];

        const json2csvParser = new Parser({ fields: compactFields });
        const csv = json2csvParser.parse(compactData);

        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}_compact.csv"`);
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        return res.status(200).send(csv);
      }
    }

  } catch (error) {
    console.error(`[${requestId}] Critical error in services export:`, {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    return res.status(500).json({
      error: 'Failed to export services',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      requestId
    });
  }
}
