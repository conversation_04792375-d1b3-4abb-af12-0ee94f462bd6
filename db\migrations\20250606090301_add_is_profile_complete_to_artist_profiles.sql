-- Migration: Add is_profile_complete column to artist_profiles table
-- Timestamp: **************

BEGIN;

-- Add the is_profile_complete column to the public.artist_profiles table
-- This column will track whether an artist has filled out their essential profile details
-- after account activation.
-- It defaults to FALSE, meaning profiles are considered incomplete until explicitly updated.
ALTER TABLE public.artist_profiles
ADD COLUMN is_profile_complete BOOLEAN NOT NULL DEFAULT FALSE;

-- Add a comment to the new column for clarity
COMMENT ON COLUMN public.artist_profiles.is_profile_complete IS 'Indicates whether the artist has completed their detailed profile setup after activation. Set to TRUE when they save their profile via the /admin/complete-profile page or equivalent.';

-- Note: If there's a need to retroactively mark some existing profiles as complete based on certain criteria,
-- an UPDATE statement could be added here. For example:
--
-- UPDATE public.artist_profiles
-- SET is_profile_complete = TRUE
-- WHERE artist_name IS NOT NULL AND display_name IS NOT NULL AND bio IS NOT NULL;
--
-- However, for a clean start, all existing profiles will default to FALSE.

COMMIT;
