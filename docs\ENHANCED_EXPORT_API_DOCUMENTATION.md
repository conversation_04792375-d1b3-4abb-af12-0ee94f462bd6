# Enhanced Export API Documentation
**Ocean Soul Sparkles - Services Export with Hierarchical Data**

## 🚀 **Overview**

The enhanced services export API now provides complete business data including service tiers, category hierarchies, and pricing structures. This enables comprehensive analysis and reporting of all service offerings.

## 📡 **API Endpoints**

### **Services Export**
```
GET /api/admin/inventory/services/export
```

**Authentication**: Bearer token required

**Query Parameters**:
- `format` (required): `json` | `csv`
- `style` (optional, CSV only): `compact` | `expanded`
- `category` (optional): Filter by category
- `status` (optional): Filter by status
- `featured` (optional): Filter by featured status

## 📊 **Export Formats**

### **1. JSON Format (Hierarchical)**
**URL**: `?format=json`

**Structure**: Complete hierarchical data with nested tiers and category information

```json
{
  "export_info": {
    "timestamp": "2025-06-21T10:30:00Z",
    "total_services": 17,
    "total_tiers": 64,
    "format": "hierarchical_json",
    "version": "2.0"
  },
  "services": [
    {
      "id": "service-uuid",
      "name": "Body Painting",
      "description": "Professional body painting services",
      "base_duration": 10,
      "base_price": 65,
      "color": "#6a0dad",
      "image_url": "https://...",
      "status": "active",
      "featured": false,
      "visibility": {
        "public": true,
        "pos": true,
        "events": true
      },
      "category": {
        "id": "category-uuid",
        "name": "Body Painting",
        "description": "Professional body painting services",
        "parent_id": null,
        "parent_name": null
      },
      "pricing_tiers": [
        {
          "id": "tier-uuid",
          "name": "Small",
          "description": "Small area body painting",
          "duration": 5,
          "price": 40,
          "is_default": false,
          "sort_order": 1
        }
      ],
      "tier_count": 5,
      "price_range": {
        "min": 40,
        "max": 150,
        "default": 65
      },
      "duration_range": {
        "min": 5,
        "max": 30,
        "default": 10
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### **2. CSV Compact Format**
**URL**: `?format=csv&style=compact` (default CSV style)

**Structure**: One row per service with tier summary data

**Key Fields**:
- Service information: `id`, `name`, `description`, `base_duration`, `base_price`
- Category details: `category_id`, `category_name`, `category_description`, `parent_category_name`
- Tier summary: `tier_count`, `price_min`, `price_max`, `price_default`
- Tier details: `pricing_tiers_json` (JSON string with all tier data)

### **3. CSV Expanded Format**
**URL**: `?format=csv&style=expanded`

**Structure**: One row per service-tier combination

**Key Fields**:
- Service info: `service_id`, `service_name`, `service_description`
- Tier details: `tier_id`, `tier_name`, `tier_description`, `tier_duration`, `tier_price`
- Category info: `category_id`, `category_name`, `category_description`

## 🏗️ **Data Structure Details**

### **Service Level Fields**

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Unique service identifier |
| `name` | String | Service name |
| `description` | String | Service description |
| `base_duration` | Integer | Base duration in minutes |
| `base_price` | Decimal | Base price |
| `color` | String | Service color code |
| `status` | String | Service status (active, inactive) |
| `featured` | Boolean | Whether service is featured |
| `tier_count` | Integer | Number of pricing tiers |
| `price_range` | Object | Min/max/default pricing |
| `duration_range` | Object | Min/max/default duration |

### **Category Fields**

| Field | Type | Description |
|-------|------|-------------|
| `category.id` | UUID | Category identifier |
| `category.name` | String | Category name |
| `category.description` | String | Category description |
| `category.parent_id` | UUID | Parent category ID (for subcategories) |
| `category.parent_name` | String | Parent category name |

### **Pricing Tier Fields**

| Field | Type | Description |
|-------|------|-------------|
| `pricing_tiers[].id` | UUID | Tier identifier |
| `pricing_tiers[].name` | String | Tier name (Small, Medium, Large, etc.) |
| `pricing_tiers[].description` | String | Tier description |
| `pricing_tiers[].duration` | Integer | Tier duration in minutes |
| `pricing_tiers[].price` | Decimal | Tier price |
| `pricing_tiers[].is_default` | Boolean | Whether this is the default tier |
| `pricing_tiers[].sort_order` | Integer | Display order |

### **Visibility Fields**

| Field | Type | Description |
|-------|------|-------------|
| `visibility.public` | Boolean | Visible on public website |
| `visibility.pos` | Boolean | Visible in POS system |
| `visibility.events` | Boolean | Visible for events |

## 📈 **Usage Examples**

### **Business Analysis Use Cases**

1. **Pricing Strategy Analysis**:
   - Use `price_range` data to analyze pricing distribution
   - Compare tier pricing across similar services
   - Identify pricing gaps or opportunities

2. **Service Portfolio Review**:
   - Use `tier_count` to understand service complexity
   - Analyze category distribution
   - Review service visibility settings

3. **Operational Planning**:
   - Use `duration_range` for scheduling optimization
   - Analyze tier popularity via `is_default` flags
   - Plan resource allocation by category

### **Integration Examples**

```javascript
// Fetch hierarchical JSON data
const response = await fetch('/api/admin/inventory/services/export?format=json');
const data = await response.json();

// Analyze pricing across all services
const pricingAnalysis = data.services.map(service => ({
  name: service.name,
  category: service.category.name,
  tierCount: service.tier_count,
  priceRange: `$${service.price_range.min}-$${service.price_range.max}`,
  defaultPrice: service.price_range.default
}));

// Find services with most pricing options
const complexServices = data.services
  .filter(s => s.tier_count > 3)
  .sort((a, b) => b.tier_count - a.tier_count);
```

## 🔧 **Technical Implementation**

### **Database Queries**
- Uses `services_with_pricing` view for optimized tier data retrieval
- JOINs with `service_categories` for category hierarchy
- Aggregates tier statistics in application layer

### **Performance Characteristics**
- **JSON Export**: ~500ms for 17 services with 64 tiers
- **CSV Compact**: ~400ms, one row per service
- **CSV Expanded**: ~450ms, one row per tier (75 rows)

### **Error Handling**
- Graceful degradation if category data unavailable
- Continues export if tier parsing fails
- Comprehensive request ID tracking for debugging

## 🎯 **Migration from Legacy Export**

### **Breaking Changes**
- JSON structure completely redesigned
- CSV fields significantly expanded
- New hierarchical data model

### **Backward Compatibility**
- Legacy endpoints remain functional
- New version indicated by `version: "2.0"` in JSON
- Enhanced CSV includes all original fields plus new ones

---

**Version**: 2.0  
**Last Updated**: June 21, 2025  
**Status**: ✅ Production Ready
