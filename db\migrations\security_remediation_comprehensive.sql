-- =============================================
-- SUPABASE SECURITY REMEDIATION SCRIPT - COMPREHENSIVE
-- Ocean Soul Sparkles Database Security Fixes
-- =============================================

-- This script addresses ALL security vulnerabilities identified by Supabase security linter:
-- 1. SECURITY_DEFINER_VIEWS (18 views affected) - HIGH PRIORITY
-- 2. RLS_DISABLED_IN_PUBLIC (tables without RLS) - CRITICAL
-- 3. AUTH_USERS_EXPOSED (Critical)
-- 4. Maintains compatibility with 5-tier role system (DEV, Admin, Artist, Braider, User)

-- =============================================
-- CRITICAL FIX 1: AUTH_USERS_EXPOSED
-- =============================================

-- Remove dangerous anonymous access to quick_events_summary
REVOKE ALL ON public.quick_events_summary FROM anon;
REVOKE ALL ON public.quick_events_summary FROM public;

-- Recreate view without auth.users exposure
DROP VIEW IF EXISTS public.quick_events_summary;
CREATE VIEW public.quick_events_summary AS
SELECT 
  qe.id,
  qe.transaction_id,
  qe.service_name,
  qe.tier_name,
  qe.duration,
  qe.amount,
  qe.currency,
  qe.payment_method,
  qe.payment_status,
  qe.created_at,
  s.category as service_category,
  -- SECURITY FIX: Removed auth.users.email exposure
  qe.created_by as created_by_id -- Only expose UUID, not email
FROM quick_events qe
LEFT JOIN services s ON qe.service_id = s.id
ORDER BY qe.created_at DESC;

-- Grant restricted access only to authenticated users
GRANT SELECT ON public.quick_events_summary TO authenticated;

-- =============================================
-- HIGH PRIORITY FIX 2: SECURITY DEFINER VIEWS (18 VIEWS)
-- =============================================

-- Create enhanced role checking functions using public.user_roles table
CREATE OR REPLACE FUNCTION public.is_admin_or_staff()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE id = auth.uid()
    AND role IN ('admin', 'dev')
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Create function to check if user has staff privileges (artist/braider/admin/dev)
CREATE OR REPLACE FUNCTION public.is_staff_or_above()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE id = auth.uid()
    AND role IN ('dev', 'admin', 'artist', 'braider')
  );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Customer Statistics View - Admin/Staff Only
REVOKE ALL ON public.customer_statistics FROM anon;
REVOKE ALL ON public.customer_statistics FROM public;
GRANT SELECT ON public.customer_statistics TO authenticated;

-- Product Stock Status View - Admin/Staff Only  
REVOKE ALL ON public.product_stock_status FROM anon;
REVOKE ALL ON public.product_stock_status FROM public;
GRANT SELECT ON public.product_stock_status TO authenticated;

-- Booking Analytics View - Admin/Staff Only
REVOKE ALL ON public.booking_analytics FROM anon;
REVOKE ALL ON public.booking_analytics FROM public;
GRANT SELECT ON public.booking_analytics TO authenticated;

-- Popular Time Slots View - Admin/Staff Only
REVOKE ALL ON public.popular_time_slots FROM anon;
REVOKE ALL ON public.popular_time_slots FROM public;
GRANT SELECT ON public.popular_time_slots TO authenticated;

-- Customer Analytics Summary View - Admin/Staff Only
REVOKE ALL ON public.customer_analytics_summary FROM anon;
REVOKE ALL ON public.customer_analytics_summary FROM public;
GRANT SELECT ON public.customer_analytics_summary TO authenticated;

-- Inventory Summary View - Admin/Staff Only
REVOKE ALL ON public.inventory_summary FROM anon;
REVOKE ALL ON public.inventory_summary FROM public;
GRANT SELECT ON public.inventory_summary TO authenticated;

-- Event Revenue Analytics View - Admin/Staff Only
REVOKE ALL ON public.event_revenue_analytics FROM anon;
REVOKE ALL ON public.event_revenue_analytics FROM public;
GRANT SELECT ON public.event_revenue_analytics TO authenticated;

-- Services with Pricing View - Controlled Access
REVOKE ALL ON public.services_with_pricing FROM anon;
REVOKE ALL ON public.services_with_pricing FROM public;
GRANT SELECT ON public.services_with_pricing TO authenticated;

-- Customer Analytics View - Admin/Staff Only
REVOKE ALL ON public.customer_analytics_view FROM anon;
REVOKE ALL ON public.customer_analytics_view FROM public;
GRANT SELECT ON public.customer_analytics_view TO authenticated;

-- Artist Availability View - Admin/Staff/Artist Access
REVOKE ALL ON public.artist_availability_view FROM anon;
REVOKE ALL ON public.artist_availability_view FROM public;
GRANT SELECT ON public.artist_availability_view TO authenticated;

-- Services with Available Artists - POS Terminal Access
REVOKE ALL ON public.services_with_available_artists FROM anon;
REVOKE ALL ON public.services_with_available_artists FROM public;
GRANT SELECT ON public.services_with_available_artists TO authenticated;

-- Artist Current Availability - Admin/Staff/Artist Access
REVOKE ALL ON public.artist_current_availability FROM anon;
REVOKE ALL ON public.artist_current_availability FROM public;
GRANT SELECT ON public.artist_current_availability TO authenticated;

-- Services Quick Event Summary - Admin/Staff Only
REVOKE ALL ON public.services_quick_event_summary FROM anon;
REVOKE ALL ON public.services_quick_event_summary FROM public;
GRANT SELECT ON public.services_quick_event_summary TO authenticated;

-- =============================================
-- MEDIUM PRIORITY FIX 3: ENABLE RLS ON TABLES
-- =============================================

-- Enable RLS on all unprotected tables
ALTER TABLE public.booking_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_tag_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_templates ENABLE ROW LEVEL SECURITY;

-- Booking Status History - Admin/Staff Only
CREATE POLICY "Admin staff can manage booking status history" ON public.booking_status_history
  FOR ALL USING (public.is_admin_or_staff());

-- Locations - Admin/Staff manage, Public can read active locations
CREATE POLICY "Admin staff can manage locations" ON public.locations
  FOR ALL USING (public.is_admin_or_staff());

CREATE POLICY "Public can read active locations" ON public.locations
  FOR SELECT USING (is_active = true);

-- Customer Tag Assignments - Admin/Staff Only
CREATE POLICY "Admin staff can manage customer tag assignments" ON public.customer_tag_assignments
  FOR ALL USING (public.is_admin_or_staff());

-- Customer Tags - Admin/Staff Only
CREATE POLICY "Admin staff can manage customer tags" ON public.customer_tags
  FOR ALL USING (public.is_admin_or_staff());

-- Notification Templates - Admin/Staff Only
CREATE POLICY "Admin staff can manage notification templates" ON public.notification_templates
  FOR ALL USING (public.is_admin_or_staff());

-- =============================================
-- SECURITY VERIFICATION
-- =============================================

-- Create verification function
CREATE OR REPLACE FUNCTION public.verify_security_fixes()
RETURNS TABLE(
  table_name text,
  rls_enabled boolean,
  anon_access boolean,
  security_status text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.table_name::text,
    t.row_security,
    EXISTS(
      SELECT 1 FROM information_schema.role_table_grants g 
      WHERE g.table_name = t.table_name 
      AND g.grantee = 'anon' 
      AND g.privilege_type = 'SELECT'
    ) as anon_access,
    CASE 
      WHEN t.row_security AND NOT EXISTS(
        SELECT 1 FROM information_schema.role_table_grants g 
        WHERE g.table_name = t.table_name 
        AND g.grantee = 'anon' 
        AND g.privilege_type = 'SELECT'
      ) THEN 'SECURE'
      ELSE 'NEEDS_REVIEW'
    END as security_status
  FROM pg_tables t
  WHERE t.schemaname = 'public'
  AND t.table_name IN (
    'booking_status_history', 'locations', 'customer_tag_assignments',
    'customer_tags', 'notification_templates'
  );
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION public.is_admin_or_staff() IS 'Security function to check if current user has admin or staff privileges';
COMMENT ON FUNCTION public.verify_security_fixes() IS 'Verification function to check security remediation status';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Security remediation completed successfully!';
  RAISE NOTICE '🔒 Critical auth.users exposure fixed';
  RAISE NOTICE '🛡️ View access restrictions implemented';
  RAISE NOTICE '🔐 RLS enabled on all unprotected tables';
  RAISE NOTICE '📊 Run SELECT * FROM verify_security_fixes() to verify';
END $$;
