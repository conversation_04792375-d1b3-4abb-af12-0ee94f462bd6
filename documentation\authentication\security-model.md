# Supabase Security Model

This document describes the security model used in the Ocean Soul Sparkles application with <PERSON>pa<PERSON>.

## Overview

The Ocean Soul Sparkles application uses Supabase for authentication and database operations. The security model is based on:

1. **JWT-based Authentication**: Secure token-based authentication with automatic token refresh
2. **Row Level Security (RLS)**: Fine-grained access control at the database level
3. **Role-Based Access Control (RBAC)**: Different access levels based on user roles
4. **Service Role for Admin Operations**: Secure server-side operations with elevated privileges

## Authentication

### JWT Tokens

Supabase uses JWT (JSON Web Tokens) for authentication. These tokens:

- Are signed with a secret key to prevent tampering
- Contain user information and claims
- Have a limited lifetime (default: 60 minutes)
- Are automatically refreshed before expiration

### Token Storage

Authentication tokens are stored in:

1. **Browser Storage**: For client-side authentication
   - `localStorage` for persistent sessions
   - `sessionStorage` for temporary sessions

2. **Cookies**: For cross-origin requests
   - Secure cookies in production
   - SameSite=Lax to prevent CSRF attacks

### Token Refresh

Tokens are automatically refreshed by:

1. The Supabase client's built-in refresh mechanism
2. Our custom refresh logic in `lib/auth-fetch.js`

## Row Level Security (RLS)

Supabase uses PostgreSQL's Row Level Security to control access to data at the database level.

### RLS Policies

The application uses the following RLS policies:

#### User Roles Table

```sql
-- Enable RLS on the user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to read their own role
CREATE POLICY "Users can read their own role" ON public.user_roles
  FOR SELECT USING (auth.uid() = id);

-- Create policy to allow admins to read all roles
CREATE POLICY "Admins can read all roles" ON public.user_roles
  FOR SELECT USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );

-- Create policy to allow admins to update roles
CREATE POLICY "Admins can update roles" ON public.user_roles
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );
```

#### Customers Table

```sql
-- Enable RLS on the customers table
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;

-- Create policy to allow staff and admins to read all customers
CREATE POLICY "Staff and admins can read all customers" ON public.customers
  FOR SELECT USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role IN ('staff', 'admin')
    )
  );

-- Create policy to allow admins to modify customers
CREATE POLICY "Admins can modify customers" ON public.customers
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );
```

#### Bookings Table

```sql
-- Enable RLS on the bookings table
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Create policy to allow staff and admins to read all bookings
CREATE POLICY "Staff and admins can read all bookings" ON public.bookings
  FOR SELECT USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role IN ('staff', 'admin')
    )
  );

-- Create policy to allow admins to modify bookings
CREATE POLICY "Admins can modify bookings" ON public.bookings
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'admin'
    )
  );

-- Create policy to allow staff to update booking status
CREATE POLICY "Staff can update booking status" ON public.bookings
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT id FROM public.user_roles WHERE role = 'staff'
    )
  ) WITH CHECK (
    -- Only allow updating the status field
    (OLD.customer_id = NEW.customer_id) AND
    (OLD.service_id = NEW.service_id) AND
    (OLD.booking_date = NEW.booking_date) AND
    (OLD.booking_time = NEW.booking_time) AND
    (OLD.duration = NEW.duration) AND
    (OLD.price = NEW.price)
  );
```

## Role-Based Access Control (RBAC)

The application uses a custom role system stored in the `user_roles` table:

```sql
CREATE TABLE public.user_roles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('admin', 'staff')) DEFAULT 'staff',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Roles and Permissions

1. **Admin**
   - Full access to all data and operations
   - Can manage users and roles
   - Can access all admin features

2. **Staff**
   - Read access to customer and booking data
   - Limited write access to booking data
   - Cannot manage users or roles

3. **Customer** (Authenticated users without a role)
   - Access only to their own data
   - Cannot access admin features

4. **Anonymous** (Unauthenticated users)
   - Access only to public data
   - Cannot access protected features

## Service Role for Admin Operations

For server-side operations that require elevated privileges, the application uses Supabase's service role:

```javascript
// lib/supabase.js
export const getAdminClient = () => {
  if (typeof window !== 'undefined') {
    throw new Error('Admin client can only be used server-side')
  }

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not defined')
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}
```

### Security Considerations

1. The service role key is only available on the server side
2. The service role bypasses RLS policies
3. The service role should only be used for specific admin operations
4. All service role operations should be properly authenticated and authorized

## API Security

### Authentication Middleware

API routes are protected by authentication middleware:

```javascript
// lib/admin-auth.js
export const authenticateAdmin = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Missing or invalid Authorization header'
      })
    }

    const token = authHeader.substring(7)

    // Verify token with admin client
    const adminClient = getAdminClient()
    const { data, error } = await adminClient.auth.getUser(token)

    if (error || !data.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      })
    }

    // Check user role
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')
      .eq('id', data.user.id)
      .single()

    if (roleError || !roleData || roleData.role !== 'admin') {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions'
      })
    }

    // Add user and role to request object
    req.user = data.user
    req.role = roleData.role

    // Continue to the next middleware or route handler
    next()
  } catch (error) {
    console.error('Authentication error:', error)
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication process failed'
    })
  }
}
```

## Security Best Practices

1. **Never expose the service role key** in client-side code
2. **Always use RLS policies** to control access to data
3. **Validate all user input** to prevent SQL injection and other attacks
4. **Use HTTPS** for all API requests
5. **Implement rate limiting** to prevent abuse
6. **Log authentication failures** for monitoring
7. **Use secure cookies** in production
8. **Implement CSRF protection** for forms
9. **Keep Supabase libraries updated** to get security fixes
