/* AI Scheduling Assistant Styles */

.aiAssistant {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 1.5rem;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.aiAssistant:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
}

.mobile {
  padding: 1rem;
  border-radius: 8px;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.optimizeButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.optimizeButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.optimizeButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.optimizeButton.loading {
  pointer-events: none;
}

/* Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.errorHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.errorIcon {
  font-size: 1.2rem;
}

.errorTitle {
  font-weight: 600;
}

.errorMessage {
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

/* Optimization Results */
.optimizationResults {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  backdrop-filter: blur(5px);
}

.sectionTitle {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subsectionTitle {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
}

.toggleButton {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.toggleButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Metrics */
.improvements {
  margin-bottom: 1.5rem;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.metric {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metricValue {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: #ffd700;
}

.metricLabel {
  display: block;
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Schedule Comparison */
.scheduleComparison {
  margin-bottom: 1.5rem;
}

.scheduleList {
  max-height: 300px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 0.5rem;
}

.bookingItem {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bookingItem:last-child {
  margin-bottom: 0;
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.bookingTime {
  color: #ffd700;
  font-weight: 600;
}

.customerName {
  opacity: 0.9;
}

.bookingDetails {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.serviceName {
  font-size: 0.875rem;
  opacity: 0.8;
}

.timeChange {
  font-size: 0.75rem;
  color: #90ee90;
  font-style: italic;
}

/* Recommendations */
.recommendations {
  margin-bottom: 1.5rem;
}

.recommendationsList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recommendation {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 1rem;
  border-left: 4px solid;
}

.recommendation.warning {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.recommendation.info {
  border-left-color: #17a2b8;
  background: rgba(23, 162, 184, 0.1);
}

.recommendation.success {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.recommendationHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.recommendationIcon {
  font-size: 1.1rem;
}

.recommendationPriority {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
}

.priorityHigh {
  background: rgba(220, 53, 69, 0.3) !important;
}

.priorityMedium {
  background: rgba(255, 193, 7, 0.3) !important;
}

.priorityLow {
  background: rgba(40, 167, 69, 0.3) !important;
}

.recommendationMessage {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.recommendationSuggestion {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.8;
  font-style: italic;
}

/* Actions */
.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.applyButton {
  background: linear-gradient(135deg, #28a745, #20c997);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
}

.applyButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.applyButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dismissButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.dismissButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.dismissButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Metadata */
.metadata {
  text-align: center;
  opacity: 0.7;
}

.metadataText {
  font-size: 0.75rem;
}

/* Empty State */
.emptyState {
  text-align: center;
  padding: 2rem 1rem;
}

.emptyStateIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.emptyStateTitle {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.emptyStateMessage {
  margin: 0 0 1.5rem 0;
  opacity: 0.8;
  line-height: 1.5;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-width: 300px;
  margin: 0 auto;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 6px;
}

.featureIcon {
  font-size: 1.2rem;
}

.featureText {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .optimizeButton {
    width: 100%;
    justify-content: center;
  }
  
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .applyButton,
  .dismissButton {
    width: 100%;
  }
  
  .features {
    max-width: none;
  }
  
  .bookingHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .aiAssistant {
    background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
  }
  
  .metric {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .bookingItem {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .aiAssistant {
    border: 2px solid white;
  }
  
  .optimizeButton,
  .dismissButton {
    border: 2px solid white;
  }
  
  .applyButton {
    border: 2px solid #28a745;
  }
}
