import { useState, useEffect } from 'react';
import styles from '@/styles/artists/ArtistProfileForm.module.css'; // Create this CSS module later if needed

export default function ArtistProfileForm({ initialData, onSubmit, isSubmitting }) {
  const [formData, setFormData] = useState({
    display_name: '',
    bio: '',
    portfolio_urls_text: '', // Textarea for line-separated URLs
    phone: '',
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (initialData) {
      setFormData({
        display_name: initialData.display_name || '',
        bio: initialData.bio || '',
        portfolio_urls_text: Array.isArray(initialData.portfolio_urls) ? initialData.portfolio_urls.join('\n') : '',
        phone: initialData.phone || '',
      });
    }
  }, [initialData]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear validation error for this field when user types
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.display_name.trim()) {
      newErrors.display_name = 'Display name is required.';
    } else if (formData.display_name.length > 100) {
      newErrors.display_name = 'Display name cannot exceed 100 characters.';
    }

    if (formData.bio.length > 2000) {
      newErrors.bio = 'Bio cannot exceed 2000 characters.';
    }

    // Basic validation for phone if provided (e.g. length or pattern)
    if (formData.phone && formData.phone.length > 20) { // Example: Max length
        newErrors.phone = 'Phone number seems too long.';
    }

    // Portfolio URLs: split by newline, filter empty, basic URL check (optional)
    const urls = formData.portfolio_urls_text.split('\n').map(url => url.trim()).filter(url => url);
    urls.forEach((url, index) => {
      try {
        new URL(url); // Check if it's a valid URL structure
      } catch (_) {
        newErrors.portfolio_urls_text = newErrors.portfolio_urls_text
          ? newErrors.portfolio_urls_text + `, Invalid URL on line ${index + 1}`
          : `Invalid URL on line ${index + 1}`;
      }
    });


    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    // Prepare data for submission
    const portfolio_urls = formData.portfolio_urls_text
      .split('\n')
      .map(url => url.trim())
      .filter(url => url); // Filter out empty lines

    const dataToSubmit = {
      display_name: formData.display_name,
      bio: formData.bio,
      portfolio_urls: portfolio_urls,
      phone: formData.phone,
    };
    onSubmit(dataToSubmit);
  };

  return (
    <form onSubmit={handleSubmit} className={styles.profileForm}>
      <div className={styles.formGroup}>
        <label htmlFor="display_name" className={styles.label}>Display Name:</label>
        <input
          type="text"
          id="display_name"
          name="display_name"
          value={formData.display_name}
          onChange={handleChange}
          className={`${styles.input} ${errors.display_name ? styles.inputError : ''}`}
        />
        {errors.display_name && <p className={styles.errorMessage}>{errors.display_name}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="bio" className={styles.label}>Bio/Description:</label>
        <textarea
          id="bio"
          name="bio"
          value={formData.bio}
          onChange={handleChange}
          rows="5"
          className={`${styles.textarea} ${errors.bio ? styles.inputError : ''}`}
        />
        {errors.bio && <p className={styles.errorMessage}>{errors.bio}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="portfolio_urls_text" className={styles.label}>
          Portfolio URLs (one URL per line):
        </label>
        <textarea
          id="portfolio_urls_text"
          name="portfolio_urls_text"
          value={formData.portfolio_urls_text}
          onChange={handleChange}
          rows="4"
          className={`${styles.textarea} ${errors.portfolio_urls_text ? styles.inputError : ''}`}
          placeholder="e.g., https://www.instagram.com/yourprofile\nhttps://www.yourwebsite.com/portfolio"
        />
        {errors.portfolio_urls_text && <p className={styles.errorMessage}>{errors.portfolio_urls_text}</p>}
      </div>

      <div className={styles.formGroup}>
        <label htmlFor="phone" className={styles.label}>Contact Phone:</label>
        <input
          type="tel"
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          className={`${styles.input} ${errors.phone ? styles.inputError : ''}`}
        />
        {errors.phone && <p className={styles.errorMessage}>{errors.phone}</p>}
      </div>

      {/* Display read-only fields if needed, e.g., email */}
      {initialData?.email && (
        <div className={styles.formGroup}>
            <label className={styles.label}>Email (Read-only):</label>
            <p className={styles.readOnlyField}>{initialData.email}</p>
        </div>
      )}

      <div className={styles.formActions}>
        <button type="submit" className={styles.submitButton} disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save Changes'}
        </button>
        {/* Optional: Add a cancel/reset button here */}
      </div>
    </form>
  );
}
