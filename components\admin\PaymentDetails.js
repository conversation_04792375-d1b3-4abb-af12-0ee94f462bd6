import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import styles from '@/styles/admin/PaymentDetails.module.css';

/**
 * PaymentDetails Component
 * 
 * Displays comprehensive payment information in a tabbed interface
 * Shows transaction details, customer information, service details, and refund history
 */
export default function PaymentDetails({ payment, onEdit, onUpdate, onClose }) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Helper functions
  const formatCurrency = (amount) => {
    if (!amount) return '$0.00';
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeClass = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'paid':
        return styles.statusCompleted;
      case 'pending':
        return styles.statusPending;
      case 'failed':
      case 'cancelled':
        return styles.statusFailed;
      case 'refunded':
        return styles.statusRefunded;
      default:
        return styles.statusDefault;
    }
  };

  const getPaymentMethodDisplay = (method) => {
    switch (method?.toLowerCase()) {
      case 'cash':
        return '💵 Cash';
      case 'card':
      case 'square_terminal':
        return '💳 Card (Terminal)';
      case 'square_reader':
        return '📱 Card (Reader)';
      case 'square_manual':
        return '💳 Card (Manual)';
      default:
        return method || 'Unknown';
    }
  };

  const getTransactionType = () => {
    if (payment.booking_id) {
      const bookingSource = payment.bookings?.booking_source;
      switch (bookingSource) {
        case 'pos':
          return 'POS Terminal';
        case 'online':
          return 'Online Booking';
        case 'admin':
          return 'Admin Booking';
        case 'walk_in':
          return 'Walk-in';
        default:
          return 'Service Booking';
      }
    } else if (payment.order_id) {
      return 'Product Order';
    }
    return 'Unknown';
  };

  const customer = payment.bookings?.customers || payment.orders?.customers;
  const service = payment.bookings?.services;
  const booking = payment.bookings;
  const order = payment.orders;

  return (
    <div className={styles.paymentDetails}>
      {/* Tab Navigation */}
      <div className={styles.tabNavigation}>
        <button
          className={`${styles.tabButton} ${activeTab === 'overview' ? styles.active : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`${styles.tabButton} ${activeTab === 'customer' ? styles.active : ''}`}
          onClick={() => setActiveTab('customer')}
        >
          Customer
        </button>
        {service && (
          <button
            className={`${styles.tabButton} ${activeTab === 'service' ? styles.active : ''}`}
            onClick={() => setActiveTab('service')}
          >
            Service
          </button>
        )}
        {payment.refunds && payment.refunds.length > 0 && (
          <button
            className={`${styles.tabButton} ${activeTab === 'refunds' ? styles.active : ''}`}
            onClick={() => setActiveTab('refunds')}
          >
            Refunds ({payment.refunds.length})
          </button>
        )}
      </div>

      {/* Tab Content */}
      <div className={styles.tabContent}>
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className={styles.overviewTab}>
            <div className={styles.section}>
              <h3>Transaction Information</h3>
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <label>Transaction ID</label>
                  <span className={styles.transactionId}>{payment.id}</span>
                </div>
                <div className={styles.infoItem}>
                  <label>Amount</label>
                  <span className={styles.amount}>{formatCurrency(payment.amount)}</span>
                </div>
                <div className={styles.infoItem}>
                  <label>Payment Method</label>
                  <span>{getPaymentMethodDisplay(payment.payment_method)}</span>
                </div>
                <div className={styles.infoItem}>
                  <label>Status</label>
                  <span className={`${styles.statusBadge} ${getStatusBadgeClass(payment.payment_status)}`}>
                    {payment.payment_status}
                  </span>
                </div>
                <div className={styles.infoItem}>
                  <label>Transaction Type</label>
                  <span>{getTransactionType()}</span>
                </div>
                <div className={styles.infoItem}>
                  <label>Payment Date</label>
                  <span>{formatDate(payment.payment_date)}</span>
                </div>
                {payment.transaction_id && (
                  <div className={styles.infoItem}>
                    <label>External Transaction ID</label>
                    <span className={styles.externalId}>{payment.transaction_id}</span>
                  </div>
                )}
                {payment.square_payment_id && (
                  <div className={styles.infoItem}>
                    <label>Square Payment ID</label>
                    <span className={styles.externalId}>{payment.square_payment_id}</span>
                  </div>
                )}
              </div>
            </div>

            {payment.notes && (
              <div className={styles.section}>
                <h3>Notes</h3>
                <div className={styles.notes}>
                  {payment.notes}
                </div>
              </div>
            )}

            <div className={styles.section}>
              <h3>System Information</h3>
              <div className={styles.infoGrid}>
                <div className={styles.infoItem}>
                  <label>Created</label>
                  <span>{formatDate(payment.created_at)}</span>
                </div>
                <div className={styles.infoItem}>
                  <label>Last Updated</label>
                  <span>{formatDate(payment.updated_at)}</span>
                </div>
                {payment.pos_session_id && (
                  <div className={styles.infoItem}>
                    <label>POS Session</label>
                    <span className={styles.sessionId}>{payment.pos_session_id}</span>
                  </div>
                )}
              </div>
            </div>

            {payment.receipt_url && (
              <div className={styles.section}>
                <h3>Receipt</h3>
                <a 
                  href={payment.receipt_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={styles.receiptLink}
                >
                  View Receipt
                </a>
              </div>
            )}
          </div>
        )}

        {/* Customer Tab */}
        {activeTab === 'customer' && customer && (
          <div className={styles.customerTab}>
            <div className={styles.section}>
              <h3>Customer Information</h3>
              <div className={styles.customerInfo}>
                <div className={styles.customerHeader}>
                  <h4>
                    <Link href={`/admin/customers/${customer.id}`}>
                      {customer.name}
                    </Link>
                  </h4>
                </div>
                <div className={styles.infoGrid}>
                  <div className={styles.infoItem}>
                    <label>Email</label>
                    <span>{customer.email || 'N/A'}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <label>Phone</label>
                    <span>{customer.phone || 'N/A'}</span>
                  </div>
                  {customer.address && (
                    <div className={styles.infoItem}>
                      <label>Address</label>
                      <span>
                        {customer.address}
                        {customer.city && `, ${customer.city}`}
                        {customer.state && `, ${customer.state}`}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Service Tab */}
        {activeTab === 'service' && service && (
          <div className={styles.serviceTab}>
            <div className={styles.section}>
              <h3>Service Information</h3>
              <div className={styles.serviceInfo}>
                <div className={styles.serviceHeader}>
                  <h4 style={{ color: service.color || '#333' }}>
                    {service.name}
                  </h4>
                  {service.category && (
                    <span className={styles.serviceCategory}>{service.category}</span>
                  )}
                </div>
                <div className={styles.infoGrid}>
                  <div className={styles.infoItem}>
                    <label>Base Price</label>
                    <span>{formatCurrency(service.price)}</span>
                  </div>
                  {booking?.tier_name && (
                    <div className={styles.infoItem}>
                      <label>Pricing Tier</label>
                      <span>{booking.tier_name}</span>
                    </div>
                  )}
                  {booking?.tier_price && (
                    <div className={styles.infoItem}>
                      <label>Tier Price</label>
                      <span>{formatCurrency(booking.tier_price)}</span>
                    </div>
                  )}
                  <div className={styles.infoItem}>
                    <label>Duration</label>
                    <span>{service.duration} minutes</span>
                  </div>
                  {booking && (
                    <>
                      <div className={styles.infoItem}>
                        <label>Booking Time</label>
                        <span>{formatDate(booking.start_time)}</span>
                      </div>
                      <div className={styles.infoItem}>
                        <label>Location</label>
                        <span>{booking.location || 'N/A'}</span>
                      </div>
                      {booking.booking_reference && (
                        <div className={styles.infoItem}>
                          <label>Booking Reference</label>
                          <span>{booking.booking_reference}</span>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Refunds Tab */}
        {activeTab === 'refunds' && payment.refunds && payment.refunds.length > 0 && (
          <div className={styles.refundsTab}>
            <div className={styles.section}>
              <h3>Refund History</h3>
              <div className={styles.refundsList}>
                {payment.refunds.map((refund) => (
                  <div key={refund.id} className={styles.refundItem}>
                    <div className={styles.refundHeader}>
                      <span className={styles.refundAmount}>
                        {formatCurrency(refund.refund_amount)}
                      </span>
                      <span className={`${styles.statusBadge} ${getStatusBadgeClass(refund.refund_status)}`}>
                        {refund.refund_status}
                      </span>
                    </div>
                    <div className={styles.refundDetails}>
                      <p><strong>Reason:</strong> {refund.refund_reason}</p>
                      <p><strong>Method:</strong> {refund.refund_method}</p>
                      <p><strong>Processed:</strong> {formatDate(refund.processed_at)}</p>
                      {refund.refund_notes && (
                        <p><strong>Notes:</strong> {refund.refund_notes}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
