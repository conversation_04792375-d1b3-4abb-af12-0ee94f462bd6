# Authentication System Guide

This directory contains documentation for the Ocean Soul Sparkles authentication system.

## Key Documentation

**Core Concepts & Design:**
- [Authentication Architecture](./architecture.md): Overview of the system architecture.
- [Authentication Flow](./flow.md): Detailed explanation of authentication sequences.
- [Security Model](./security-model.md): Covers security aspects including RLS policies.
- [User Profiles View](./user-profiles-view.md): Information on the user profiles view.
- [Auth Recovery System](./auth-recovery-system.md): Describes the authentication recovery mechanisms.

**Implementation & Usage:**
- [Implementation Guide](./implementation-guide.md): General implementation details and usage examples.
- [Session Fixes Implementation](./AUTHENTICATION_SESSION_FIXES_IMPLEMENTATION.md): Details on comprehensive fixes for session expiration issues.
- [Supabase Client](./supabase-client.md): Documentation for the Supabase client.
- [Auth Utils](./auth-utils.md): Utility functions for authentication tasks.
- [API Authentication](./api-authentication.md): How API authentication is handled.

**Testing & Troubleshooting:**
- [Authentication Testing Guide](./AUTHENTICATION_TESTING_GUIDE.md): Guide for testing authentication, particularly session fixes.
- [General Troubleshooting](./troubleshooting.md): Common issues and how to resolve them (general auth issues).
- [Authentication Troubleshooting (Specific Scenarios)](./authentication-troubleshooting.md): Additional troubleshooting for specific scenarios.

**Developer Information:**
- [Developer Guidelines](./developer-guidelines.md): Guidelines for developers working with the authentication system.

## Overview

The Ocean Soul Sparkles authentication system is built on Supabase and provides:

- Secure user authentication with JWT tokens
- Role-based access control
- Consistent API authentication
- Session management
- Token refresh
- Row Level Security (RLS) policies
- Direct Supabase integration with unified client architecture

The system is designed to be secure, reliable, and easy to use for both developers and end users. The application uses a streamlined Supabase integration for improved performance, reliability, and maintainability.

## Architecture

The authentication system uses direct Supabase integration with the following features:

- Simplifies the architecture
- Improves performance
- Enhances reliability
- Reduces complexity
- Improves maintainability

For implementation details, see the [Implementation Guide](./implementation-guide.md).

## Reports and Historical Fixes

This section provides links to specific historical reports, summaries of fixes, and improvements related to the authentication system. These documents offer context on past issues and resolutions.

- [AUTHENTICATION_BUILD_FIX_REPORT.md](./AUTHENTICATION_BUILD_FIX_REPORT.md)
- [AUTHENTICATION_JAVASCRIPT_FIXES_SUMMARY.md](./AUTHENTICATION_JAVASCRIPT_FIXES_SUMMARY.md)
- [AUTHENTICATION_SESSION_FIXES_SUMMARY.md](./AUTHENTICATION_SESSION_FIXES_SUMMARY.md)
- [AUTHENTICATION_TOKEN_VALIDATION_FIXES.md](./AUTHENTICATION_TOKEN_VALIDATION_FIXES.md)
- [CRITICAL_AUTHENTICATION_FIXES_SUMMARY.md](./CRITICAL_AUTHENTICATION_FIXES_SUMMARY.md)
- [MARKETING_SEGMENTS_AUTH_FIX.md](./MARKETING_SEGMENTS_AUTH_FIX.md)
- [POS_AUTHENTICATION_FIXES_COMPREHENSIVE.md](./POS_AUTHENTICATION_FIXES_COMPREHENSIVE.md)
- [PRODUCTION_AUTH_FIXES_TARGETED.md](./PRODUCTION_AUTH_FIXES_TARGETED.md)
- [STAFF_LOGIN_FIX.md](./STAFF_LOGIN_FIX.md)
- [authentication-improvements-summary.md](./authentication-improvements-summary.md)
- [booking-status-history-fix.md](./booking-status-history-fix.md)
- [auth-recovery-system.md](./auth-recovery-system.md)
