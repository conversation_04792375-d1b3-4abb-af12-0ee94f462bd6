import React from 'react';
import { useRouter } from 'next/router';
import { useCart } from '@/contexts/CartContext';
import styles from '@/styles/CartSidebar.module.css';
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils';

const CartSidebar = ({ isOpen, onClose }) => {
  const {
    cart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getCartTotal,
    getCartItemCount,
  } = useCart();
  const router = useRouter();

  const handleCheckout = () => {
    router.push('/checkout');
    onClose();
  };

  const handleUpdateQuantity = (productId, newQuantity) => {
    if (newQuantity > 0) {
      updateQuantity(productId, newQuantity);
    } else {
      // Optionally, could also call removeFromCart(productId) if newQuantity is 0
      updateQuantity(productId, 0); // Context handles removal if quantity is 0
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.sidebarOverlay} onClick={onClose}>
      <div className={styles.sidebar} onClick={(e) => e.stopPropagation()}>
        <div className={styles.header}>
          <h2>Your Cart ({getCartItemCount()})</h2>
          <button className={styles.closeButton} onClick={onClose}>
            &times;
          </button>
        </div>

        {cart.length === 0 ? (
          <div className={styles.emptyCartMessage}>
            <p>Your cart is currently empty.</p>
            <button onClick={onClose} className={styles.continueShoppingButton}>
              Continue Shopping
            </button>
          </div>
        ) : (
          <>
            <div className={styles.cartItemsContainer}>
              {cart.map((item) => (
                <div key={item.id} className={styles.cartItem}>
                  <div className={styles.itemImage}>
                    <img src={safeRender(item.image, '/images/placeholder.svg')} alt={safeRender(item.name, 'Product')} />
                  </div>
                  <div className={styles.itemDetails}>
                    <h4 className={styles.itemName}>{safeRender(item.name)}</h4>
                    <p className={styles.itemPrice}>{safeFormatCurrency(item.price)}</p>
                    <div className={styles.itemQuantity}>
                      <button onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}>-</button>
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleUpdateQuantity(item.id, parseInt(e.target.value, 10))}
                        min="0"
                      />
                      <button onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}>+</button>
                    </div>
                  </div>
                  <div className={styles.itemActions}>
                    <button className={styles.removeItemButton} onClick={() => removeFromCart(item.id)}>
                      Remove
                    </button>
                    <p className={styles.itemSubtotal}>{safeFormatCurrency(item.price * item.quantity)}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className={styles.footer}>
              <div className={styles.subtotal}>
                <span>Subtotal:</span>
                <span>{safeFormatCurrency(getCartTotal())}</span>
              </div>
              <button className={styles.checkoutButton} onClick={handleCheckout}>
                Proceed to Checkout
              </button>
              <div className={styles.footerActions}>
                <button className={styles.clearCartButton} onClick={clearCart}>
                  Clear Cart
                </button>
                <button className={styles.continueShoppingButtonAlt} onClick={onClose}>
                  Continue Shopping
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CartSidebar;
