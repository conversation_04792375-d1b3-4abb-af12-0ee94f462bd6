/* Social Media Dashboard Component Styles */
/* Ocean Soul Sparkles - Phase 7.3: Social Media Integration Layer */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

.container.mobile {
  padding: 15px;
  max-width: 100%;
}

/* Header */
.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 600;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #e91e63;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Navigation Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab {
  background: none;
  border: none;
  padding: 15px 25px;
  font-size: 1rem;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.tab:hover {
  color: #e91e63;
  background-color: rgba(233, 30, 99, 0.1);
}

.tab.active {
  color: #e91e63;
  border-bottom: 3px solid #e91e63;
}

/* Tab Content */
.tabContent {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Platforms Section */
.platforms {
  margin-bottom: 30px;
}

.platforms h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.noPlatforms {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.noPlatforms p {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.connectButtons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.connectButton {
  background: linear-gradient(135deg, #e91e63, #ad1457);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.connectButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

/* Platform List */
.platformList {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.platformCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.platformCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.platformInfo h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.status {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 10px;
}

.status.connected {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.profileInfo {
  margin-top: 10px;
}

.profileInfo p {
  margin: 5px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.platformActions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.settingsButton,
.disconnectButton {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.settingsButton {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.settingsButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.disconnectButton {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #fed7d7;
}

.disconnectButton:hover {
  background: #fed7d7;
  border-color: #feb2b2;
}

/* Recent Posts */
.recentPosts {
  margin-bottom: 30px;
}

.recentPosts h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.postGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.postCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.postCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.postImage {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.postContent {
  padding: 15px;
}

.postText {
  color: #495057;
  font-size: 0.9rem;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.postMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform {
  font-size: 1.2rem;
}

.engagement {
  color: #6c757d;
  font-size: 0.85rem;
}

/* Quick Actions */
.quickActions {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
}

.quickActions h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.actionButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.actionButton {
  background: linear-gradient(135deg, #e91e63, #ad1457);
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.actionButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Post Creator */
.postCreator {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  max-width: 600px;
}

.postCreator h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.postForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-weight: 600;
  color: #495057;
}

.messageInput,
.urlInput {
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.messageInput:focus,
.urlInput:focus {
  outline: none;
  border-color: #e91e63;
  box-shadow: 0 0 0 2px rgba(233, 30, 99, 0.2);
}

.messageInput {
  resize: vertical;
  min-height: 100px;
}

.platformSelector {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.platformOption {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.platformOption input[type="checkbox"] {
  accent-color: #e91e63;
}

.formActions {
  display: flex;
  justify-content: flex-end;
}

.postButton {
  background: linear-gradient(135deg, #e91e63, #ad1457);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.postButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

.postButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Analytics */
.analytics {
  max-width: 1000px;
}

.analytics h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.noAnalytics {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  color: #6c757d;
}

.analyticsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.analyticsCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
}

.analyticsCard h4 {
  color: #2c3e50;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.metric {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metricValue {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #e91e63;
  margin-bottom: 5px;
}

.metricLabel {
  color: #6c757d;
  font-size: 0.8rem;
  font-weight: 500;
}

.error {
  color: #e53e3e;
  font-style: italic;
}

/* Settings */
.settings {
  max-width: 600px;
}

.settings h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.settingsInfo {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #e91e63;
}

.settingsInfo p {
  margin: 10px 0;
  color: #495057;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h2 {
    font-size: 1.6rem;
  }

  .header p {
    font-size: 1rem;
  }

  .tabs {
    margin-bottom: 20px;
  }

  .tab {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .connectButtons {
    flex-direction: column;
    align-items: center;
  }

  .platformList {
    grid-template-columns: 1fr;
  }

  .platformActions {
    flex-direction: column;
  }

  .postGrid {
    grid-template-columns: 1fr;
  }

  .actionButtons {
    grid-template-columns: 1fr;
  }

  .analyticsGrid {
    grid-template-columns: 1fr;
  }

  .metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .platformCard,
  .postCreator,
  .quickActions,
  .analyticsCard {
    padding: 15px;
  }

  .metrics {
    grid-template-columns: 1fr;
  }
}
