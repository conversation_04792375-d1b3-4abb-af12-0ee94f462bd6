import { useState, useEffect, useRef } from 'react'
import { supabase } from '@/lib/supabase'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * CustomerSearch component for finding and selecting existing customers
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onCustomerSelect - Callback when customer is selected
 * @param {string} props.placeholder - Placeholder text for search input
 * @returns {JSX.Element}
 */
export default function CustomerSearch({ onCustomerSelect, placeholder = "Search customers..." }) {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchInputRef = useRef(null)
  const resultsRef = useRef(null)

  // Debounced search function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.trim().length >= 2) {
        performSearch(searchTerm.trim())
      } else {
        setSearchResults([])
        setShowResults(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        resultsRef.current && 
        !resultsRef.current.contains(event.target) &&
        !searchInputRef.current.contains(event.target)
      ) {
        setShowResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const performSearch = async (term) => {
    try {
      setIsSearching(true)
      
      const { data, error } = await supabase
        .from('customers')
        .select('id, name, email, phone, customer_status')
        .or(`name.ilike.%${term}%,email.ilike.%${term}%,phone.ilike.%${term}%`)
        .eq('customer_status', 'active')
        .order('name')
        .limit(10)

      if (error) {
        console.error('Error searching customers:', error)
        setSearchResults([])
        return
      }

      setSearchResults(data || [])
      setShowResults(true)
    } catch (error) {
      console.error('Error performing customer search:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const handleInputChange = (event) => {
    setSearchTerm(event.target.value)
  }

  const handleInputFocus = () => {
    if (searchResults.length > 0) {
      setShowResults(true)
    }
  }

  const handleCustomerSelect = (customer) => {
    setSearchTerm(customer.name)
    setShowResults(false)
    onCustomerSelect && onCustomerSelect(customer)
  }

  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      setShowResults(false)
      searchInputRef.current?.blur()
    }
  }

  const formatPhoneNumber = (phone) => {
    if (!phone) return ''
    // Simple Australian phone number formatting
    const cleaned = phone.replace(/\D/g, '')
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`
    }
    return phone
  }

  const getCustomerStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4caf50'
      case 'inactive': return '#ff9800'
      case 'blocked': return '#f44336'
      default: return '#666'
    }
  }

  return (
    <div className={styles.customerSearch}>
      <div className={styles.searchIcon}>🔍</div>
      <input
        ref={searchInputRef}
        type="text"
        value={searchTerm}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={styles.searchInput}
        autoComplete="off"
      />
      
      {isSearching && (
        <div className={styles.searchSpinner}>
          <div className={styles.loadingSpinner}></div>
        </div>
      )}

      {showResults && searchResults.length > 0 && (
        <div ref={resultsRef} className={styles.searchResults}>
          {searchResults.map((customer) => (
            <div
              key={customer.id}
              className={styles.searchResult}
              onClick={() => handleCustomerSelect(customer)}
            >
              <div className={styles.customerName}>
                {safeRender(customer.name, 'Unknown Customer')}
                <span 
                  className={styles.statusIndicator}
                  style={{ 
                    backgroundColor: getCustomerStatusColor(customer.customer_status),
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    display: 'inline-block',
                    marginLeft: '8px'
                  }}
                  title={`Status: ${customer.customer_status}`}
                />
              </div>
              <div className={styles.customerDetails}>
                {customer.email && (
                  <span>📧 {safeRender(customer.email)}</span>
                )}
                {customer.phone && (
                  <span style={{ marginLeft: customer.email ? '12px' : '0' }}>
                    📞 {formatPhoneNumber(customer.phone)}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {showResults && searchResults.length === 0 && !isSearching && searchTerm.length >= 2 && (
        <div ref={resultsRef} className={styles.searchResults}>
          <div className={styles.searchResult}>
            <div className={styles.customerName}>No customers found</div>
            <div className={styles.customerDetails}>
              Try searching with a different name, email, or phone number
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
