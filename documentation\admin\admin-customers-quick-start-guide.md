# Admin Customer Management Quick Start Implementation Guide

## Immediate Actions (This Week)

### 1. Database Setup
```bash
# Execute the customer system enhancements migration
cd your-project-directory
psql -h your-supabase-host -U postgres -d postgres -f db/migrations/customer_system_enhancements.sql
```

### 2. Priority 1 Components to Build

#### Advanced Customer Search Component
**File**: `components/admin/AdvancedCustomerSearch.js`
**Estimated Time**: 6-8 hours
**Key Features**:
- Multi-criteria search with debounced input
- Advanced filtering by value, location, status
- Saved filter presets
- Real-time search results

#### Customer Segmentation System
**File**: `components/admin/CustomerTagManager.js`
**Estimated Time**: 4-6 hours
**Key Features**:
- Tag creation and assignment
- Color-coded tag system
- Bulk tag operations
- Tag-based filtering

#### Enhanced Customer Profile
**File**: `components/admin/EnhancedCustomerProfile.js`
**Estimated Time**: 8-10 hours
**Key Features**:
- Tabbed interface with comprehensive data
- Customer analytics and insights
- Communication history
- Booking timeline integration

### 3. API Endpoints to Create

#### Advanced Customer Search
**File**: `pages/api/admin/customers/search.js`
```javascript
// GET /api/admin/customers/search
// Query params: search, tags, segment, lifetimeValue, bookingCount, location, etc.
export default async function handler(req, res) {
  const {
    search = '',
    tags = [],
    segment = 'all',
    lifetimeValue = {},
    bookingCount = {},
    location = {},
    customerStatus = 'all',
    limit = 50,
    offset = 0
  } = req.query;

  try {
    let query = supabase
      .from('customer_analytics_summary')
      .select('*')
      .order('registration_date', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply segment filter
    if (segment !== 'all') {
      switch (segment) {
        case 'new':
          query = query.gte('registration_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
          break;
        case 'regular':
          query = query.gte('total_bookings', 3);
          break;
        case 'vip':
          query = query.or('customer_tier.eq.gold,customer_tier.eq.platinum,lifetime_value.gte.1000');
          break;
        case 'inactive':
          query = query.lt('last_booking_date', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString());
          break;
      }
    }

    // Apply value filters
    if (lifetimeValue.min) {
      query = query.gte('lifetime_value', lifetimeValue.min);
    }
    if (lifetimeValue.max) {
      query = query.lte('lifetime_value', lifetimeValue.max);
    }

    // Apply booking count filters
    if (bookingCount.min) {
      query = query.gte('total_bookings', bookingCount.min);
    }
    if (bookingCount.max) {
      query = query.lte('total_bookings', bookingCount.max);
    }

    // Apply status filter
    if (customerStatus !== 'all') {
      query = query.eq('customer_status', customerStatus);
    }

    const { data, error, count } = await query;

    if (error) throw error;

    res.status(200).json({
      customers: data,
      total: count,
      hasMore: offset + limit < count
    });
  } catch (error) {
    console.error('Customer search error:', error);
    res.status(500).json({ error: 'Failed to search customers' });
  }
}
```

#### Customer Tags Management
**File**: `pages/api/admin/customer-tags/index.js`
```javascript
// GET /api/admin/customer-tags - List all tags
// POST /api/admin/customer-tags - Create new tag
export default async function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const { data, error } = await supabase
        .from('customer_tags')
        .select('*')
        .order('category', { ascending: true })
        .order('name', { ascending: true });

      if (error) throw error;

      res.status(200).json({ tags: data });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch tags' });
    }
  } else if (req.method === 'POST') {
    try {
      const { name, description, color, category } = req.body;

      const { data, error } = await supabase
        .from('customer_tags')
        .insert({
          name,
          description,
          color,
          category: category || 'general',
          created_by: req.user?.id
        })
        .select()
        .single();

      if (error) throw error;

      res.status(201).json({ tag: data });
    } catch (error) {
      res.status(500).json({ error: 'Failed to create tag' });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
```

#### Customer Analytics
**File**: `pages/api/admin/customers/[id]/analytics.js`
```javascript
// GET /api/admin/customers/[id]/analytics
export default async function handler(req, res) {
  const { id } = req.query;

  try {
    // Get customer analytics summary
    const { data: customer, error: customerError } = await supabase
      .from('customer_analytics_summary')
      .select('*')
      .eq('id', id)
      .single();

    if (customerError) throw customerError;

    // Get booking history for trends
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('start_time, status, actual_revenue, estimated_revenue, services(name)')
      .eq('customer_id', id)
      .order('start_time', { ascending: false })
      .limit(50);

    if (bookingsError) throw bookingsError;

    // Get communication history
    const { data: communications, error: commError } = await supabase
      .from('customer_communications')
      .select('communication_type, direction, created_at, status')
      .eq('customer_id', id)
      .order('created_at', { ascending: false })
      .limit(20);

    if (commError) throw commError;

    // Calculate analytics
    const analytics = {
      customer,
      bookingTrends: calculateBookingTrends(bookings),
      servicePreferences: calculateServicePreferences(bookings),
      communicationStats: calculateCommunicationStats(communications),
      revenueAnalysis: calculateRevenueAnalysis(bookings),
      engagementScore: calculateEngagementScore(customer, bookings, communications)
    };

    res.status(200).json(analytics);
  } catch (error) {
    console.error('Customer analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch customer analytics' });
  }
}

function calculateBookingTrends(bookings) {
  // Group bookings by month and calculate trends
  const monthlyBookings = {};
  bookings.forEach(booking => {
    const month = new Date(booking.start_time).toISOString().slice(0, 7);
    if (!monthlyBookings[month]) {
      monthlyBookings[month] = { count: 0, revenue: 0 };
    }
    monthlyBookings[month].count++;
    monthlyBookings[month].revenue += booking.actual_revenue || booking.estimated_revenue || 0;
  });

  return Object.entries(monthlyBookings)
    .map(([month, data]) => ({ month, ...data }))
    .sort((a, b) => a.month.localeCompare(b.month));
}

function calculateServicePreferences(bookings) {
  const serviceCount = {};
  bookings.forEach(booking => {
    const serviceName = booking.services?.name || 'Unknown';
    serviceCount[serviceName] = (serviceCount[serviceName] || 0) + 1;
  });

  return Object.entries(serviceCount)
    .map(([service, count]) => ({ service, count }))
    .sort((a, b) => b.count - a.count);
}
```

## Week 1 Implementation Checklist

### Day 1-2: Database & Backend Setup
- [ ] Run customer system enhancements migration
- [ ] Test new database tables and functions
- [ ] Create customer search API endpoint
- [ ] Create customer tags management API
- [ ] Create customer analytics API
- [ ] Update existing customer API to include new fields

### Day 3-4: Core Components
- [ ] Build AdvancedCustomerSearch component
- [ ] Create CustomerTagManager component
- [ ] Update CustomerList to use new search functionality
- [ ] Add bulk operations to customer list
- [ ] Implement customer segmentation display

### Day 5: Enhanced Profile & Integration
- [ ] Build EnhancedCustomerProfile component
- [ ] Integrate customer analytics display
- [ ] Add customer communication history
- [ ] Test all new functionality
- [ ] Fix integration issues

## Code Templates

### 1. Customer Search Hook
```javascript
// hooks/useCustomerSearch.js
import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';

export const useCustomerSearch = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    segment: 'all',
    tags: [],
    customerStatus: 'all'
  });
  const [pagination, setPagination] = useState({
    offset: 0,
    limit: 50,
    total: 0,
    hasMore: false
  });

  const debouncedSearch = useCallback(
    debounce(async (searchFilters) => {
      try {
        setLoading(true);
        setError(null);

        const queryParams = new URLSearchParams({
          ...searchFilters,
          offset: 0,
          limit: pagination.limit
        });

        const response = await fetch(`/api/admin/customers/search?${queryParams}`);
        const data = await response.json();

        if (!response.ok) throw new Error(data.error);

        setCustomers(data.customers);
        setPagination(prev => ({
          ...prev,
          offset: 0,
          total: data.total,
          hasMore: data.hasMore
        }));
      } catch (error) {
        setError(error.message);
        console.error('Customer search error:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    [pagination.limit]
  );

  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const loadMore = async () => {
    if (!pagination.hasMore || loading) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        ...filters,
        offset: pagination.offset + pagination.limit,
        limit: pagination.limit
      });

      const response = await fetch(`/api/admin/customers/search?${queryParams}`);
      const data = await response.json();

      if (!response.ok) throw new Error(data.error);

      setCustomers(prev => [...prev, ...data.customers]);
      setPagination(prev => ({
        ...prev,
        offset: prev.offset + prev.limit,
        hasMore: data.hasMore
      }));
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    customers,
    loading,
    error,
    filters,
    pagination,
    updateFilters,
    loadMore
  };
};
```

### 2. Customer Analytics Utilities
```javascript
// utils/customerAnalytics.js
export const calculateCustomerHealthScore = (customer, bookings, communications) => {
  let score = 50; // Base score

  // Booking frequency (0-30 points)
  const bookingCount = bookings?.length || 0;
  if (bookingCount >= 10) score += 30;
  else if (bookingCount >= 5) score += 20;
  else if (bookingCount >= 2) score += 10;

  // Recency (0-25 points)
  const daysSinceLastBooking = customer.days_since_last_booking;
  if (daysSinceLastBooking === null) score -= 25;
  else if (daysSinceLastBooking <= 30) score += 25;
  else if (daysSinceLastBooking <= 60) score += 15;
  else if (daysSinceLastBooking <= 90) score += 5;
  else score -= 15;

  // Value (0-25 points)
  const lifetimeValue = customer.lifetime_value || 0;
  if (lifetimeValue >= 1000) score += 25;
  else if (lifetimeValue >= 500) score += 15;
  else if (lifetimeValue >= 200) score += 10;
  else if (lifetimeValue >= 100) score += 5;

  // Communication responsiveness (0-20 points)
  const responseRate = calculateResponseRate(communications);
  score += Math.floor(responseRate * 20);

  return Math.max(0, Math.min(100, score));
};

export const calculateChurnRisk = (customer, bookings) => {
  let risk = 0.0;

  // Days since last booking
  const daysSinceLastBooking = customer.days_since_last_booking;
  if (daysSinceLastBooking > 180) risk += 0.4;
  else if (daysSinceLastBooking > 90) risk += 0.2;
  else if (daysSinceLastBooking > 60) risk += 0.1;

  // Booking frequency decline
  const recentBookings = bookings?.filter(b => 
    new Date(b.start_time) > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
  ).length || 0;
  
  const olderBookings = bookings?.filter(b => {
    const bookingDate = new Date(b.start_time);
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    const oneEightyDaysAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
    return bookingDate <= ninetyDaysAgo && bookingDate > oneEightyDaysAgo;
  }).length || 0;

  if (olderBookings > recentBookings) risk += 0.3;

  // Cancellation rate
  const canceledBookings = bookings?.filter(b => b.status === 'canceled').length || 0;
  const totalBookings = bookings?.length || 1;
  const cancellationRate = canceledBookings / totalBookings;
  if (cancellationRate > 0.3) risk += 0.2;
  else if (cancellationRate > 0.2) risk += 0.1;

  return Math.min(1.0, risk);
};

export const getCustomerInsights = (customer, analytics) => {
  const insights = [];

  // Health score insights
  if (customer.customer_health_score < 30) {
    insights.push({
      type: 'warning',
      title: 'Low Health Score',
      message: 'This customer may need attention to prevent churn.',
      action: 'Consider reaching out with a special offer or check-in call.'
    });
  }

  // Churn risk insights
  if (customer.churn_risk_score > 0.7) {
    insights.push({
      type: 'danger',
      title: 'High Churn Risk',
      message: 'This customer is at high risk of churning.',
      action: 'Immediate intervention recommended - personal outreach or retention offer.'
    });
  }

  // Value insights
  if (customer.lifetime_value > 1000 && customer.days_since_last_booking > 60) {
    insights.push({
      type: 'info',
      title: 'High-Value Customer Inactive',
      message: 'A valuable customer hasn\'t booked recently.',
      action: 'Reach out with VIP treatment or exclusive offers.'
    });
  }

  // Booking pattern insights
  if (analytics?.bookingTrends?.length > 3) {
    const recentTrend = analytics.bookingTrends.slice(-3);
    const isDecreasing = recentTrend.every((month, i) => 
      i === 0 || month.count <= recentTrend[i - 1].count
    );
    
    if (isDecreasing) {
      insights.push({
        type: 'warning',
        title: 'Declining Booking Frequency',
        message: 'Booking frequency has been decreasing over recent months.',
        action: 'Investigate satisfaction and consider re-engagement campaign.'
      });
    }
  }

  return insights;
};
```

## Testing Checklist

### Functionality Tests
- [ ] Advanced search returns accurate results
- [ ] Filtering works correctly for all criteria
- [ ] Customer tags can be created and assigned
- [ ] Customer profile displays comprehensive information
- [ ] Analytics calculations are accurate
- [ ] Bulk operations work correctly

### Performance Tests
- [ ] Search results return in <1 second
- [ ] Customer list loads quickly with 1000+ customers
- [ ] Analytics calculations complete in reasonable time
- [ ] Database queries are optimized

### User Experience Tests
- [ ] Search interface is intuitive
- [ ] Customer profile provides valuable insights
- [ ] Tag management is easy to use
- [ ] Mobile responsiveness maintained

## Success Metrics for Week 1

### Technical Metrics
- [ ] All new API endpoints respond in <500ms
- [ ] Database migration completes without errors
- [ ] Search functionality returns results in <1 second
- [ ] All tests pass

### User Experience Metrics
- [ ] Customer search time reduced by 70%
- [ ] Customer profile provides 5x more information
- [ ] Tag-based filtering improves workflow efficiency
- [ ] Enhanced analytics provide actionable insights

### Business Metrics
- [ ] Time to access customer information reduced by 60%
- [ ] Customer segmentation enables targeted actions
- [ ] Health score identifies at-risk customers
- [ ] Communication tracking improves customer service

## Next Week Preview

### Week 2 Focus Areas
1. **Customer Journey Mapping**: Implement visual customer journey timeline
2. **Communication Automation**: Build automated communication workflows
3. **Advanced Analytics**: Add predictive analytics and recommendations
4. **Loyalty Integration**: Implement customer loyalty and rewards system

This quick start guide provides everything needed to begin immediate implementation of the most impactful customer management system improvements.
