/**
 * Connection Status Indicator Component
 * Ocean Soul Sparkles - Real-time Dashboard Features
 */

import { useState, useEffect } from 'react'
import styles from '@/styles/admin/ConnectionStatusIndicator.module.css'

export default function ConnectionStatusIndicator({ 
  connectionStatus, 
  lastUpdated, 
  onRetry,
  className = '' 
}) {
  const [showDetails, setShowDetails] = useState(false)
  const [timeAgo, setTimeAgo] = useState('')

  // Update time ago display
  useEffect(() => {
    if (!lastUpdated) return

    const updateTimeAgo = () => {
      const now = new Date()
      const diff = now - lastUpdated
      const seconds = Math.floor(diff / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)

      if (seconds < 60) {
        setTimeAgo('just now')
      } else if (minutes < 60) {
        setTimeAgo(`${minutes}m ago`)
      } else if (hours < 24) {
        setTimeAgo(`${hours}h ago`)
      } else {
        setTimeAgo('over a day ago')
      }
    }

    updateTimeAgo()
    const interval = setInterval(updateTimeAgo, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [lastUpdated])

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: '🟢',
          text: 'Live',
          description: 'Real-time updates active',
          className: styles.connected
        }
      case 'connecting':
        return {
          icon: '🟡',
          text: 'Connecting',
          description: 'Establishing connection...',
          className: styles.connecting
        }
      case 'disconnected':
        return {
          icon: '🔴',
          text: 'Offline',
          description: 'Using cached data',
          className: styles.disconnected
        }
      case 'error':
        return {
          icon: '⚠️',
          text: 'Error',
          description: 'Connection failed',
          className: styles.error
        }
      default:
        return {
          icon: '⚪',
          text: 'Unknown',
          description: 'Status unknown',
          className: styles.unknown
        }
    }
  }

  const statusConfig = getStatusConfig()

  return (
    <div className={`${styles.container} ${statusConfig.className} ${className}`}>
      <div 
        className={styles.indicator}
        onClick={() => setShowDetails(!showDetails)}
        title={statusConfig.description}
      >
        <span className={styles.icon}>{statusConfig.icon}</span>
        <span className={styles.text}>{statusConfig.text}</span>
        {lastUpdated && (
          <span className={styles.timeAgo}>{timeAgo}</span>
        )}
      </div>

      {showDetails && (
        <div className={styles.details}>
          <div className={styles.detailsContent}>
            <div className={styles.statusRow}>
              <span className={styles.label}>Status:</span>
              <span className={styles.value}>{statusConfig.description}</span>
            </div>
            
            {lastUpdated && (
              <div className={styles.statusRow}>
                <span className={styles.label}>Last Update:</span>
                <span className={styles.value}>
                  {lastUpdated.toLocaleTimeString()}
                </span>
              </div>
            )}

            <div className={styles.statusRow}>
              <span className={styles.label}>Connection:</span>
              <span className={styles.value}>
                {connectionStatus === 'connected' ? 'WebSocket' : 'HTTP Polling'}
              </span>
            </div>

            {(connectionStatus === 'error' || connectionStatus === 'disconnected') && onRetry && (
              <div className={styles.actions}>
                <button 
                  className={styles.retryButton}
                  onClick={onRetry}
                >
                  Retry Connection
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Simple status dot component for minimal display
 */
export function ConnectionStatusDot({ connectionStatus, className = '' }) {
  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return '#10b981' // green
      case 'connecting':
        return '#f59e0b' // yellow
      case 'disconnected':
        return '#6b7280' // gray
      case 'error':
        return '#ef4444' // red
      default:
        return '#9ca3af' // light gray
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Live updates active'
      case 'connecting':
        return 'Connecting...'
      case 'disconnected':
        return 'Offline mode'
      case 'error':
        return 'Connection error'
      default:
        return 'Status unknown'
    }
  }

  return (
    <div 
      className={`${styles.statusDot} ${className}`}
      title={getStatusText()}
    >
      <div 
        className={styles.dot}
        style={{ backgroundColor: getStatusColor() }}
      />
      {connectionStatus === 'connected' && (
        <div 
          className={styles.pulse}
          style={{ backgroundColor: getStatusColor() }}
        />
      )}
    </div>
  )
}

/**
 * Connection status banner for important notifications
 */
export function ConnectionStatusBanner({ 
  connectionStatus, 
  onRetry, 
  onDismiss,
  className = '' 
}) {
  if (connectionStatus === 'connected') {
    return null // Don't show banner when connected
  }

  const getBannerConfig = () => {
    switch (connectionStatus) {
      case 'error':
        return {
          type: 'error',
          message: 'Connection lost. Dashboard may show outdated information.',
          action: 'Retry'
        }
      case 'disconnected':
        return {
          type: 'warning',
          message: 'Working offline. Some features may be limited.',
          action: 'Reconnect'
        }
      case 'connecting':
        return {
          type: 'info',
          message: 'Connecting to live updates...',
          action: null
        }
      default:
        return null
    }
  }

  const bannerConfig = getBannerConfig()
  if (!bannerConfig) return null

  return (
    <div className={`${styles.banner} ${styles[bannerConfig.type]} ${className}`}>
      <div className={styles.bannerContent}>
        <span className={styles.bannerMessage}>{bannerConfig.message}</span>
        <div className={styles.bannerActions}>
          {bannerConfig.action && onRetry && (
            <button 
              className={styles.bannerButton}
              onClick={onRetry}
            >
              {bannerConfig.action}
            </button>
          )}
          {onDismiss && (
            <button 
              className={styles.bannerDismiss}
              onClick={onDismiss}
              aria-label="Dismiss"
            >
              ×
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
