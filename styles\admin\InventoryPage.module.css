/* InventoryPage.module.css */
.inventoryPage {
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header h1 {
  margin: 0;
  font-size: 1.8rem;
  color: #333;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.addButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.addButton:hover {
  background-color: #5a0c8f;
}

.exportButton {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.exportButton:hover {
  background-color: #218838;
}

.exportButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.importButton {
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.importButton:hover {
  background-color: #0056b3;
}

.importButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.cancelButton {
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.cancelButton:hover {
  background-color: #545b62;
}

.cancelButton:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

.templateButton {
  background-color: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
}

.templateButton:hover {
  background-color: #138496;
}

.templateButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.exportOptions,
.importOptions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup label {
  font-weight: 500;
  color: #333;
}

.selectInput,
.fileInput {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.selectInput:focus,
.fileInput:focus {
  outline: none;
  border-color: #6a0dad;
  box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2);
}

.helpText {
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.importResults {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resultsSummary {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.resultsSummary p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.errorsList {
  background-color: #f8d7da;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.errorsList h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #721c24;
  font-size: 1rem;
}

.errorsList ul {
  margin: 0;
  padding-left: 1.5rem;
}

.errorsList li {
  color: #721c24;
  font-size: 0.85rem;
  margin-bottom: 0.25rem;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  margin-bottom: 1rem;
  position: relative;
}

.errorCloseButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  color: #721c24;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.errorCloseButton:hover {
  background-color: rgba(114, 28, 36, 0.1);
  border-radius: 50%;
}

.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statsCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.statsCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statsCard h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: #666;
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.tabsContainer {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tabButton {
  flex: 1;
  background-color: transparent;
  border: none;
  padding: 1rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.tabButton:hover:not(.activeTab) {
  background-color: #f5f5f5;
}

.activeTab {
  background-color: #6a0dad;
  color: white;
}

.tabContent {
  padding: 1rem;
  min-height: 400px;
}

.modalContent {
  padding: 1rem;
}

.modalContent h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

@media (max-width: 768px) {
  .inventoryPage {
    padding: 0.75rem;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .actionButtons {
    width: 100%;
    justify-content: stretch;
  }

  .addButton,
  .exportButton,
  .importButton {
    flex: 1;
    min-height: 44px;
  }

  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .statsCard {
    padding: 1rem;
  }

  .statValue {
    font-size: 1.5rem;
  }

  .tabContent {
    padding: 0.75rem;
  }
}

/* iPhone 13 Pro Max and similar devices */
@media (max-width: 428px) {
  .inventoryPage {
    padding: 0.5rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .addButton,
  .exportButton,
  .importButton {
    width: 100%;
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .statsContainer {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .statsCard {
    padding: 0.75rem;
  }

  .statsCard h3 {
    font-size: 0.9rem;
  }

  .statValue {
    font-size: 1.75rem;
  }

  .tabs {
    flex-direction: column;
  }

  .tabButton {
    padding: 0.75rem;
    font-size: 16px;
    min-height: 44px;
  }

  .tabContent {
    padding: 0.5rem;
    min-height: 300px;
  }

  .modalContent {
    padding: 0.75rem;
  }

  .modalContent h2 {
    font-size: 1.4rem;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  .inventoryPage {
    padding: 0.375rem;
  }

  .header h1 {
    font-size: 1.4rem;
  }

  .statsCard {
    padding: 0.5rem;
  }

  .statsCard h3 {
    font-size: 0.85rem;
  }

  .statValue {
    font-size: 1.5rem;
  }

  .tabButton {
    padding: 0.5rem;
    font-size: 15px;
  }

  .tabContent {
    padding: 0.375rem;
  }

  .modalContent {
    padding: 0.5rem;
  }

  .modalContent h2 {
    font-size: 1.3rem;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  .inventoryPage {
    padding: 0.25rem;
  }

  .header h1 {
    font-size: 1.3rem;
  }

  .statsCard {
    padding: 0.375rem;
  }

  .statsCard h3 {
    font-size: 0.8rem;
  }

  .statValue {
    font-size: 1.3rem;
  }

  .tabButton {
    padding: 0.375rem;
    font-size: 14px;
  }

  .tabContent {
    padding: 0.25rem;
  }

  .modalContent {
    padding: 0.375rem;
  }

  .modalContent h2 {
    font-size: 1.2rem;
  }
}
