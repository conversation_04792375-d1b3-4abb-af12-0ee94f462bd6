# Resend Welcome Email Testing Guide

## 🎯 **Overview**
This guide provides step-by-step instructions for testing the new resend welcome email functionality with secure application tokens.

## 🔧 **Features Implemented**

### ✅ **Resend Welcome Email**
- **Location**: User Management → User List → Artist/Braider rows
- **Button**: "Resend Welcome Email" (envelope icon)
- **Functionality**: 
  - Generates new secure application token
  - Invalidates previous tokens
  - Sends fresh welcome email
  - Resets application status to "pending"

### ✅ **Token Management**
- **Location**: User Management → User List → Artist/Braider rows
- **Button**: 🔑 (key icon)
- **Functionality**:
  - View all tokens for a user
  - See token status (active, expired, used)
  - Extend token expiration
  - Invalidate tokens manually

### ✅ **API Endpoints**
- `POST /api/admin/users/resend-welcome` - Resend welcome email
- `GET /api/admin/tokens/manage?userId=xxx` - Get user tokens
- `PATCH /api/admin/tokens/manage` - Update token status
- `DELETE /api/admin/tokens/manage` - Invalidate token

## 🧪 **Testing Steps**

### **Step 1: Prepare Test Environment**

1. **Ensure Email is Enabled**:
   ```bash
   # Check .env.local contains:
   FORCE_EMAIL_IN_DEV=true
   ```

2. **Restart Development Server**:
   ```bash
   npm run dev
   ```

3. **Verify Database Functions**:
   - The database migration should have created the required RPC functions
   - Check Supabase dashboard for `generate_application_token` function

### **Step 2: Test with Existing Artist/Braider User**

1. **Navigate to User Management**:
   - Go to `/admin/users`
   - Find your existing Artist/Braider user

2. **Locate New Buttons**:
   - Look for "Resend Welcome Email" button (envelope icon)
   - Look for "🔑" token management button
   - These should only appear for Artist/Braider roles

3. **Test Resend Welcome Email**:
   - Click "Resend Welcome Email" button
   - Confirm the action in the dialog
   - Watch console logs for success messages
   - Check your email inbox

### **Step 3: Verify Email Content**

The welcome email should contain:
- ✅ **Correct Subject**: "Welcome to Ocean Soul Sparkles Team, [Name]!"
- ✅ **Application Link**: `https://www.oceansoulsparkles.com.au/apply/artist?token=[secure-token]`
- ✅ **Staff Portal Link**: `https://www.oceansoulsparkles.com.au/staff-login`
- ✅ **Role-specific Content**: Artist or Braider specific instructions

### **Step 4: Test Application Token**

1. **Click Application Link**:
   - Click the link in the welcome email
   - Should go directly to application form (no login required)

2. **Verify Token Validation**:
   - Form should load without authentication
   - Should show user's information pre-filled

3. **Submit Application**:
   - Fill out the application form
   - Submit successfully
   - Token should be marked as "used"

### **Step 5: Test Token Management**

1. **Open Token Management**:
   - Click the 🔑 button next to the user
   - Modal should open showing token history

2. **Verify Token Information**:
   - Should show token status (active, expired, used)
   - Should show creation date and expiration
   - Should show who created the token

3. **Test Token Actions**:
   - **Extend**: Extend token expiration by 7 days
   - **Invalidate**: Mark token as used/invalid

### **Step 6: Test Multiple Tokens**

1. **Generate Multiple Tokens**:
   - Click "Resend Welcome Email" multiple times
   - Each should invalidate previous tokens
   - Only the latest token should be active

2. **Verify Token History**:
   - Open token management
   - Should see all tokens with correct statuses
   - Previous tokens should show as "used"

## 🔍 **Expected Console Logs**

### **Successful Resend Email**:
```
Resending welcome email for user: [user-id]
Created application entry for artist user [user-id]
Generated token for artist user: [token-prefix]...
Stored application token for artist user [user-id]
Welcome email result: { success: true, emailSent: true, ... }
Welcome email resend completed successfully
```

### **Successful Token Generation**:
```
Invalidating existing tokens for user
Generating new application token
Generated new token: [token-prefix]...
Sending welcome email with new token
```

## ❌ **Troubleshooting**

### **Issue: Email Not Received**
**Check**:
- `FORCE_EMAIL_IN_DEV=true` in `.env.local`
- Gmail SMTP settings are correct
- Console shows `emailSent: true`

**Solution**:
```bash
# Test email configuration
node scripts/test-email.js
```

### **Issue: Token Generation Fails**
**Check**:
- Database functions exist in Supabase
- Console shows 404 error for RPC function

**Solution**:
- Re-run database migration
- Check Supabase dashboard for functions

### **Issue: Application Link Doesn't Work**
**Check**:
- Token is active (not expired/used)
- URL format is correct
- Token validation API is working

**Solution**:
- Generate new token
- Check token management modal
- Verify API endpoints

## 📊 **Success Criteria**

### ✅ **Email Functionality**
- [ ] Welcome email is received in inbox
- [ ] Email contains correct application link
- [ ] Email contains staff portal link
- [ ] Email content is role-appropriate

### ✅ **Token Functionality**
- [ ] Application link works without login
- [ ] Token is marked as used after submission
- [ ] Previous tokens are invalidated
- [ ] Token management shows correct status

### ✅ **Admin Interface**
- [ ] Resend button appears for Artist/Braider only
- [ ] Token management button works
- [ ] Confirmation dialog functions properly
- [ ] Success/error messages display correctly

### ✅ **Database Integration**
- [ ] Tokens are stored correctly
- [ ] Application status updates to "pending"
- [ ] Last email sent timestamp is recorded
- [ ] Activity is logged for audit trail

## 🚀 **Next Steps After Testing**

1. **Production Deployment**:
   - Ensure `FORCE_EMAIL_IN_DEV=false` in production
   - Verify Gmail SMTP works in production environment
   - Test with real email addresses

2. **User Training**:
   - Document the new resend functionality
   - Train admin users on token management
   - Create user guides for the new process

3. **Monitoring**:
   - Monitor email delivery rates
   - Track token usage and expiration
   - Monitor for any security issues

## 📞 **Support**

If you encounter issues during testing:
1. Check console logs for detailed error messages
2. Verify environment variables are set correctly
3. Test email configuration with the test script
4. Check Supabase dashboard for database issues

This comprehensive testing ensures the resend welcome email functionality works correctly and provides a reliable way to onboard new Artist/Braider users.
