-- Phase 9.1: Enhanced Authentication System Database Schema
-- Ocean Soul Sparkles - Security & Compliance Enhancement
-- Database schema for MFA, biometric auth, and enhanced session management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- MULTI-FACTOR AUTHENTICATION (MFA) TABLES
-- =====================================================

-- MFA configurations for users
CREATE TABLE IF NOT EXISTS public.user_mfa_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    totp_secret TEXT, -- Encrypted TOTP secret
    backup_codes TEXT[], -- Array of encrypted backup codes
    sms_phone TEXT, -- Encrypted phone number for SMS backup
    recovery_email TEXT, -- Encrypted recovery email
    mfa_enforced BOOLEAN DEFAULT FALSE, -- Admin-enforced MFA
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- MFA verification attempts and logs
CREATE TABLE IF NOT EXISTS public.mfa_verification_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    verification_type VARCHAR(20) NOT NULL CHECK (verification_type IN ('totp', 'sms', 'backup_code', 'recovery')),
    success BOOLEAN NOT NULL,
    ip_address INET,
    user_agent TEXT,
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BIOMETRIC AUTHENTICATION TABLES
-- =====================================================

-- WebAuthn credentials for biometric authentication
CREATE TABLE IF NOT EXISTS public.webauthn_credentials (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    credential_id TEXT NOT NULL UNIQUE,
    public_key TEXT NOT NULL, -- Base64 encoded public key
    counter BIGINT DEFAULT 0,
    device_type VARCHAR(50), -- 'platform' or 'cross-platform'
    authenticator_name TEXT, -- User-friendly name for the device
    aaguid TEXT, -- Authenticator Attestation GUID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Biometric authentication attempts
CREATE TABLE IF NOT EXISTS public.biometric_auth_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    credential_id TEXT REFERENCES public.webauthn_credentials(credential_id) ON DELETE SET NULL,
    success BOOLEAN NOT NULL,
    ip_address INET,
    user_agent TEXT,
    failure_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ENHANCED SESSION MANAGEMENT TABLES
-- =====================================================

-- Enhanced session tracking
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    refresh_token TEXT UNIQUE,
    device_fingerprint TEXT,
    ip_address INET,
    user_agent TEXT,
    location_country TEXT,
    location_city TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Device fingerprinting for suspicious login detection
CREATE TABLE IF NOT EXISTS public.device_fingerprints (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    fingerprint_hash TEXT NOT NULL,
    device_info JSONB DEFAULT '{}', -- Browser, OS, screen resolution, etc.
    is_trusted BOOLEAN DEFAULT FALSE,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    login_count INTEGER DEFAULT 1,
    UNIQUE(user_id, fingerprint_hash)
);

-- Suspicious login attempts and security events
CREATE TABLE IF NOT EXISTS public.security_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    ip_address INET,
    user_agent TEXT,
    device_fingerprint TEXT,
    additional_data JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SOCIAL LOGIN ENHANCEMENTS
-- =====================================================

-- Enhanced social login tracking
CREATE TABLE IF NOT EXISTS public.social_login_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    provider VARCHAR(50) NOT NULL, -- 'google', 'facebook', 'apple', etc.
    provider_user_id TEXT NOT NULL,
    provider_email TEXT,
    provider_name TEXT,
    profile_picture_url TEXT,
    account_linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(provider, provider_user_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- MFA settings indexes
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_user_id ON public.user_mfa_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_enabled ON public.user_mfa_settings(mfa_enabled) WHERE mfa_enabled = TRUE;

-- MFA verification logs indexes
CREATE INDEX IF NOT EXISTS idx_mfa_verification_logs_user_id ON public.mfa_verification_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_verification_logs_created_at ON public.mfa_verification_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_mfa_verification_logs_success ON public.mfa_verification_logs(success, created_at);

-- WebAuthn credentials indexes
CREATE INDEX IF NOT EXISTS idx_webauthn_credentials_user_id ON public.webauthn_credentials(user_id);
CREATE INDEX IF NOT EXISTS idx_webauthn_credentials_active ON public.webauthn_credentials(is_active) WHERE is_active = TRUE;

-- Biometric auth logs indexes
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_user_id ON public.biometric_auth_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_biometric_auth_logs_created_at ON public.biometric_auth_logs(created_at);

-- User sessions indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(is_active, expires_at) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON public.user_sessions(session_token);

-- Device fingerprints indexes
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_user_id ON public.device_fingerprints(user_id);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_hash ON public.device_fingerprints(fingerprint_hash);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_trusted ON public.device_fingerprints(is_trusted) WHERE is_trusted = TRUE;

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON public.security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON public.security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON public.security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON public.security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_unresolved ON public.security_events(resolved, severity) WHERE resolved = FALSE;

-- Social login accounts indexes
CREATE INDEX IF NOT EXISTS idx_social_login_accounts_user_id ON public.social_login_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_social_login_accounts_provider ON public.social_login_accounts(provider);
CREATE INDEX IF NOT EXISTS idx_social_login_accounts_active ON public.social_login_accounts(is_active) WHERE is_active = TRUE;

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE public.user_mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mfa_verification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webauthn_credentials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.biometric_auth_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.device_fingerprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.social_login_accounts ENABLE ROW LEVEL SECURITY;

-- MFA settings policies
CREATE POLICY "Users can manage their own MFA settings" ON public.user_mfa_settings
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admin can view all MFA settings" ON public.user_mfa_settings
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- MFA verification logs policies
CREATE POLICY "Users can view their own MFA logs" ON public.mfa_verification_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can view all MFA logs" ON public.mfa_verification_logs
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- WebAuthn credentials policies
CREATE POLICY "Users can manage their own WebAuthn credentials" ON public.webauthn_credentials
  FOR ALL USING (user_id = auth.uid());

-- Biometric auth logs policies
CREATE POLICY "Users can view their own biometric logs" ON public.biometric_auth_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can view all biometric logs" ON public.biometric_auth_logs
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- User sessions policies
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own sessions" ON public.user_sessions
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admin can view all sessions" ON public.user_sessions
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- Device fingerprints policies
CREATE POLICY "Users can view their own device fingerprints" ON public.device_fingerprints
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own device fingerprints" ON public.device_fingerprints
  FOR UPDATE USING (user_id = auth.uid());

-- Security events policies
CREATE POLICY "Users can view their own security events" ON public.security_events
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin can manage all security events" ON public.security_events
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- Social login accounts policies
CREATE POLICY "Users can manage their own social accounts" ON public.social_login_accounts
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admin can view all social accounts" ON public.social_login_accounts
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      WHERE ur.id = auth.uid() AND ur.role IN ('dev', 'admin')
    )
  );

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for user_mfa_settings
CREATE TRIGGER update_user_mfa_settings_updated_at
    BEFORE UPDATE ON public.user_mfa_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_sessions 
    WHERE expires_at < NOW() OR (last_activity < NOW() - INTERVAL '30 days');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_user_id UUID,
    p_event_type VARCHAR(50),
    p_event_description TEXT,
    p_severity VARCHAR(20),
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_device_fingerprint TEXT DEFAULT NULL,
    p_additional_data JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO public.security_events (
        user_id, event_type, event_description, severity,
        ip_address, user_agent, device_fingerprint, additional_data
    ) VALUES (
        p_user_id, p_event_type, p_event_description, p_severity,
        p_ip_address, p_user_agent, p_device_fingerprint, p_additional_data
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.user_mfa_settings IS 'Multi-factor authentication settings for users';
COMMENT ON TABLE public.mfa_verification_logs IS 'Audit trail for MFA verification attempts';
COMMENT ON TABLE public.webauthn_credentials IS 'WebAuthn credentials for biometric authentication';
COMMENT ON TABLE public.biometric_auth_logs IS 'Audit trail for biometric authentication attempts';
COMMENT ON TABLE public.user_sessions IS 'Enhanced session tracking with device fingerprinting';
COMMENT ON TABLE public.device_fingerprints IS 'Device fingerprinting for suspicious login detection';
COMMENT ON TABLE public.security_events IS 'Security events and incidents tracking';
COMMENT ON TABLE public.social_login_accounts IS 'Social login account linking and tracking';

-- Migration completed successfully
SELECT 'Phase 9.1 Enhanced Authentication System migration completed successfully' AS status;
