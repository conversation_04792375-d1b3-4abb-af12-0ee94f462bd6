import { supabaseAdmin, getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';

/**
 * API endpoint for exporting products data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response or file download
 */
export default async function handler(req, res) {
  // Generate request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Products export request started`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log(`[${requestId}] Invalid method: ${req.method}`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Log environment and configuration
  console.log(`[${requestId}] Environment: ${process.env.NODE_ENV}`);
  console.log(`[${requestId}] Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing'}`);
  console.log(`[${requestId}] Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? 'configured' : 'missing'}`);

  // Authenticate request with enhanced error handling
  let authorized = false;
  let user = null;
  let role = null;
  let authError = null;

  try {
    // Development bypass for testing
    if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
      console.log(`[${requestId}] Development mode: bypassing authentication`);
      authorized = true;
      user = { id: 'dev-user', email: '<EMAIL>' };
      role = 'admin';
    } else {
      // Normal authentication
      console.log(`[${requestId}] Attempting authentication...`);
      const authResult = await authenticateAdminRequest(req);
      authorized = authResult.authorized;
      user = authResult.user;
      role = authResult.role;
      authError = authResult.error;

      console.log(`[${requestId}] Authentication result: authorized=${authorized}, role=${role}, user=${user?.email}`);
    }
  } catch (error) {
    console.error(`[${requestId}] Authentication error:`, error);
    authError = error;
    authorized = false;
  }

  if (!authorized) {
    console.error(`[${requestId}] Authentication failed:`, authError?.message);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authError?.message || 'Authentication failed',
      requestId
    });
  }

  try {
    const { format = 'csv', category, status, featured } = req.query;
    console.log(`[${requestId}] Export parameters: format=${format}, category=${category}, status=${status}, featured=${featured}`);

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      console.log(`[${requestId}] Invalid format: ${format}`);
      return res.status(400).json({
        error: 'Invalid format. Supported formats: csv, json',
        requestId
      });
    }

    // Initialize admin client with enhanced error handling
    let adminClient;
    try {
      console.log(`[${requestId}] Initializing admin client...`);
      adminClient = getAdminClient();
      console.log(`[${requestId}] Admin client initialized successfully`);
    } catch (clientError) {
      console.error(`[${requestId}] Failed to initialize admin client:`, clientError);

      // Fallback to supabaseAdmin if available
      if (supabaseAdmin) {
        console.log(`[${requestId}] Falling back to supabaseAdmin`);
        adminClient = supabaseAdmin;
      } else {
        console.error(`[${requestId}] No admin client available`);
        return res.status(500).json({
          error: 'Database connection failed',
          message: 'Unable to initialize database client',
          requestId
        });
      }
    }

    // Build query with enhanced logging
    console.log(`[${requestId}] Building products query...`);
    let query = adminClient
      .from('products')
      .select(`
        id,
        name,
        description,
        short_description,
        sku,
        price,
        sale_price,
        cost_price,
        category,
        stock,
        low_stock_threshold,
        image_url,
        gallery_images,
        status,
        featured,
        is_active,
        meta_title,
        meta_description,
        created_at,
        updated_at
      `)
      .order('name');

    // Apply filters with logging
    if (category && category !== 'all') {
      console.log(`[${requestId}] Applying category filter: ${category}`);
      query = query.eq('category', category);
    }

    if (status && status !== 'all') {
      console.log(`[${requestId}] Applying status filter: ${status}`);
      query = query.eq('status', status);
    }

    if (featured === 'true') {
      console.log(`[${requestId}] Applying featured filter: true`);
      query = query.eq('featured', true);
    } else if (featured === 'false') {
      console.log(`[${requestId}] Applying featured filter: false`);
      query = query.eq('featured', false);
    }

    // Execute query with timeout and enhanced error handling
    console.log(`[${requestId}] Executing products query...`);
    const queryStartTime = Date.now();

    const { data: products, error: queryError } = await Promise.race([
      query,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout after 30 seconds')), 30000)
      )
    ]);

    const queryDuration = Date.now() - queryStartTime;
    console.log(`[${requestId}] Query completed in ${queryDuration}ms`);

    if (queryError) {
      console.error(`[${requestId}] Database query error:`, {
        message: queryError.message,
        code: queryError.code,
        details: queryError.details,
        hint: queryError.hint
      });

      return res.status(500).json({
        error: 'Failed to fetch products data',
        message: queryError.message,
        code: queryError.code,
        requestId
      });
    }

    if (!products) {
      console.warn(`[${requestId}] Query returned null/undefined data`);
      return res.status(500).json({
        error: 'No data returned from query',
        requestId
      });
    }

    console.log(`[${requestId}] Successfully fetched ${products.length} products`);


    // Process data for export
    const exportData = products.map(product => ({
      id: product.id,
      name: product.name || '',
      description: product.description || '',
      short_description: product.short_description || '',
      sku: product.sku || '',
      price: product.price || 0,
      sale_price: product.sale_price || '',
      cost_price: product.cost_price || '',
      category: product.category || '',
      stock: product.stock || 0,
      low_stock_threshold: product.low_stock_threshold || 5,
      image_url: product.image_url || '',
      gallery_images: Array.isArray(product.gallery_images) ? product.gallery_images.join(';') : '',
      status: product.status || 'active',
      featured: product.featured || false,
      is_active: product.is_active || true,
      meta_title: product.meta_title || '',
      meta_description: product.meta_description || '',
      created_at: product.created_at,
      updated_at: product.updated_at
    }));

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `products_export_${timestamp}`;

    if (format === 'json') {
      // Return JSON format with proper headers
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).json(exportData);
    } else {
      // Return CSV format with proper headers
      const csvFields = [
        'id', 'name', 'description', 'short_description', 'sku', 'price', 'sale_price',
        'cost_price', 'category', 'stock', 'low_stock_threshold', 'image_url',
        'gallery_images', 'status', 'featured', 'is_active', 'meta_title',
        'meta_description', 'created_at', 'updated_at'
      ];

      const json2csvParser = new Parser({ fields: csvFields });
      const csv = json2csvParser.parse(exportData);

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).send(csv);
    }

  } catch (error) {
    console.error(`[${requestId}] Critical error in products export:`, {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    return res.status(500).json({
      error: 'Failed to export products',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      requestId
    });
  }
}
