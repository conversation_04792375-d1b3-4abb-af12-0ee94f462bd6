import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { Parser } from 'json2csv';

/**
 * API endpoint for exporting products data
 * Supports CSV and JSON formats
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response or file download
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request with development bypass
  let authorized = false;
  let user = null;
  let role = null;
  let authError = null;

  // Development bypass for testing
  if (process.env.NODE_ENV === 'development' && process.env.ENABLE_AUTH_BYPASS === 'true') {
    console.log('[Export] Development mode: bypassing authentication');
    authorized = true;
    user = { id: 'dev-user', email: '<EMAIL>' };
    role = 'admin';
  } else {
    // Normal authentication
    const authResult = await authenticateAdminRequest(req);
    authorized = authResult.authorized;
    user = authResult.user;
    role = authResult.role;
    authError = authResult.error;
  }

  if (!authorized) {
    console.error('[Export] Authentication failed:', authError?.message);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authError?.message || 'Authentication failed'
    });
  }

  try {
    const { format = 'csv', category, status, featured } = req.query;

    // Validate format
    if (!['csv', 'json'].includes(format)) {
      return res.status(400).json({ error: 'Invalid format. Supported formats: csv, json' });
    }

    // Build query
    let query = supabaseAdmin
      .from('products')
      .select(`
        id,
        name,
        description,
        short_description,
        sku,
        price,
        sale_price,
        cost_price,
        category,
        stock,
        low_stock_threshold,
        image_url,
        gallery_images,
        status,
        featured,
        is_active,
        meta_title,
        meta_description,
        created_at,
        updated_at
      `)
      .order('name');

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (featured === 'true') {
      query = query.eq('featured', true);
    } else if (featured === 'false') {
      query = query.eq('featured', false);
    }

    // Execute query
    const { data: products, error: queryError } = await query;

    if (queryError) {
      console.error('Error fetching products for export:', queryError);
      return res.status(500).json({ error: 'Failed to fetch products data' });
    }

    // Process data for export
    const exportData = products.map(product => ({
      id: product.id,
      name: product.name || '',
      description: product.description || '',
      short_description: product.short_description || '',
      sku: product.sku || '',
      price: product.price || 0,
      sale_price: product.sale_price || '',
      cost_price: product.cost_price || '',
      category: product.category || '',
      stock: product.stock || 0,
      low_stock_threshold: product.low_stock_threshold || 5,
      image_url: product.image_url || '',
      gallery_images: Array.isArray(product.gallery_images) ? product.gallery_images.join(';') : '',
      status: product.status || 'active',
      featured: product.featured || false,
      is_active: product.is_active || true,
      meta_title: product.meta_title || '',
      meta_description: product.meta_description || '',
      created_at: product.created_at,
      updated_at: product.updated_at
    }));

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `products_export_${timestamp}`;

    if (format === 'json') {
      // Return JSON format with proper headers
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).json(exportData);
    } else {
      // Return CSV format with proper headers
      const csvFields = [
        'id', 'name', 'description', 'short_description', 'sku', 'price', 'sale_price',
        'cost_price', 'category', 'stock', 'low_stock_threshold', 'image_url',
        'gallery_images', 'status', 'featured', 'is_active', 'meta_title',
        'meta_description', 'created_at', 'updated_at'
      ];

      const json2csvParser = new Parser({ fields: csvFields });
      const csv = json2csvParser.parse(exportData);

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      return res.status(200).send(csv);
    }

  } catch (error) {
    console.error('Error exporting products:', error);
    return res.status(500).json({
      error: 'Failed to export products',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
}
