/**
 * PWA Provider Component
 * Ocean Soul Sparkles - Provides PWA context and functionality throughout the app
 */

import { createContext, useContext, useEffect, useState } from 'react'
import { usePWA } from '@/lib/hooks/usePWA'
import { offlineStorage } from '@/lib/offline-storage'
import P<PERSON>InstallPrompt, { PWAStatusIndicator } from './PWAInstallPrompt'
import { toast } from 'react-toastify'

const PWAContext = createContext({})

export function PWAProvider({ children }) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [offlineQueue, setOfflineQueue] = useState([])
  const [syncInProgress, setSyncInProgress] = useState(false)
  
  const pwaHook = usePWA()
  const { 
    isOnline, 
    isInstalled, 
    requestBackgroundSync,
    cacheForOffline,
    getCachedData 
  } = pwaHook

  // Initialize PWA functionality
  useEffect(() => {
    const initializePWA = async () => {
      try {
        // Initialize offline storage
        await offlineStorage.init()
        
        // Load offline queue
        const queue = await offlineStorage.getSyncQueue()
        setOfflineQueue(queue)
        
        // Cache essential data for offline use
        await cacheEssentialData()
        
        setIsInitialized(true)
        console.log('[PWA] Provider initialized successfully')
      } catch (error) {
        console.error('[PWA] Failed to initialize:', error)
        setIsInitialized(true) // Continue even if initialization fails
      }
    }

    initializePWA()
  }, [])

  // Handle online/offline transitions
  useEffect(() => {
    if (isOnline && offlineQueue.length > 0 && !syncInProgress) {
      syncOfflineData()
    }
  }, [isOnline, offlineQueue.length, syncInProgress])

  // Cache essential data for offline use
  const cacheEssentialData = async () => {
    try {
      // Cache services data
      const servicesResponse = await fetch('/api/admin/services')
      if (servicesResponse.ok) {
        const services = await servicesResponse.json()
        await offlineStorage.cacheServices(services)
        await cacheForOffline(services, 'services')
      }

      // Cache customers data (limited)
      const customersResponse = await fetch('/api/admin/customers?limit=100')
      if (customersResponse.ok) {
        const customers = await customersResponse.json()
        await offlineStorage.cacheCustomers(customers)
        await cacheForOffline(customers, 'customers')
      }

      console.log('[PWA] Essential data cached for offline use')
    } catch (error) {
      console.error('[PWA] Failed to cache essential data:', error)
    }
  }

  // Sync offline data when connection is restored
  const syncOfflineData = async () => {
    if (syncInProgress) return

    setSyncInProgress(true)
    
    try {
      console.log('[PWA] Starting offline data sync...')
      
      // Sync offline bookings
      const offlineBookings = await offlineStorage.getOfflineBookings()
      for (const booking of offlineBookings) {
        try {
          const response = await fetch('/api/admin/pos/create-booking', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(booking)
          })

          if (response.ok) {
            await offlineStorage.markBookingSynced(booking.id)
            toast.success(`Booking synced: ${booking.service?.name || 'Unknown service'}`)
          }
        } catch (error) {
          console.error('[PWA] Failed to sync booking:', booking.id, error)
        }
      }

      // Sync offline photos
      const offlinePhotos = await offlineStorage.getOfflinePhotos()
      for (const photo of offlinePhotos) {
        try {
          const formData = new FormData()
          
          // Convert base64 back to blob
          const response = await fetch(photo.data)
          const blob = await response.blob()
          
          formData.append('photo', blob)
          formData.append('type', photo.type)
          formData.append('bookingId', photo.bookingId || '')
          formData.append('timestamp', photo.timestamp)

          const uploadResponse = await fetch('/api/admin/photos/upload', {
            method: 'POST',
            body: formData
          })

          if (uploadResponse.ok) {
            // Mark as synced (implementation needed in offline storage)
            console.log('[PWA] Photo synced:', photo.id)
            toast.success('Photo synced successfully')
          }
        } catch (error) {
          console.error('[PWA] Failed to sync photo:', photo.id, error)
        }
      }

      // Update queue
      const updatedQueue = await offlineStorage.getSyncQueue()
      setOfflineQueue(updatedQueue)

      // Request background sync for remaining items
      if (updatedQueue.length > 0) {
        await requestBackgroundSync('offline-sync')
      }

      console.log('[PWA] Offline data sync completed')
    } catch (error) {
      console.error('[PWA] Sync failed:', error)
      toast.error('Failed to sync offline data')
    } finally {
      setSyncInProgress(false)
    }
  }

  // Add item to offline queue
  const addToOfflineQueue = async (type, data, priority = 'medium') => {
    try {
      let itemId

      if (type === 'booking') {
        itemId = await offlineStorage.saveOfflineBooking(data)
      } else if (type === 'photo') {
        itemId = await offlineStorage.saveOfflinePhoto(data)
      }

      // Update queue state
      const updatedQueue = await offlineStorage.getSyncQueue()
      setOfflineQueue(updatedQueue)

      return itemId
    } catch (error) {
      console.error('[PWA] Failed to add to offline queue:', error)
      throw error
    }
  }

  // Get cached data for offline use
  const getOfflineData = async (type) => {
    try {
      switch (type) {
        case 'services':
          return await offlineStorage.getCachedServices()
        case 'customers':
          return await offlineStorage.getCachedCustomers()
        case 'dashboard':
          return await offlineStorage.getCachedDashboardData('dashboard')
        default:
          return await getCachedData(type)
      }
    } catch (error) {
      console.error('[PWA] Failed to get offline data:', error)
      return null
    }
  }

  // Cache dashboard data
  const cacheDashboardData = async (data) => {
    try {
      await offlineStorage.cacheDashboardData('dashboard', data)
      await cacheForOffline(data, 'dashboard')
    } catch (error) {
      console.error('[PWA] Failed to cache dashboard data:', error)
    }
  }

  // Get storage statistics
  const getStorageStats = async () => {
    try {
      return await offlineStorage.getStorageStats()
    } catch (error) {
      console.error('[PWA] Failed to get storage stats:', error)
      return {}
    }
  }

  // Cleanup old data
  const cleanupOfflineData = async (maxAge = 7 * 24 * 60 * 60 * 1000) => {
    try {
      await offlineStorage.cleanup(maxAge)
      const updatedQueue = await offlineStorage.getSyncQueue()
      setOfflineQueue(updatedQueue)
      toast.success('Offline data cleaned up')
    } catch (error) {
      console.error('[PWA] Failed to cleanup offline data:', error)
      toast.error('Failed to cleanup offline data')
    }
  }

  const contextValue = {
    // PWA hook values
    ...pwaHook,
    
    // Provider state
    isInitialized,
    offlineQueue,
    syncInProgress,
    
    // Provider methods
    addToOfflineQueue,
    getOfflineData,
    cacheDashboardData,
    getStorageStats,
    cleanupOfflineData,
    syncOfflineData,
    cacheEssentialData
  }

  return (
    <PWAContext.Provider value={contextValue}>
      {children}
      
      {/* PWA Install Prompt */}
      <PWAInstallPrompt 
        showOnMobile={true}
        showOnDesktop={false}
        position="bottom-right"
        autoShow={true}
      />
      
      {/* PWA Status Indicator for installed apps */}
      {isInstalled && (
        <div style={{ 
          position: 'fixed', 
          top: '20px', 
          right: '20px', 
          zIndex: 1000 
        }}>
          <PWAStatusIndicator showDetails={true} />
        </div>
      )}
    </PWAContext.Provider>
  )
}

// Hook to use PWA context
export function usePWAContext() {
  const context = useContext(PWAContext)
  if (!context) {
    throw new Error('usePWAContext must be used within a PWAProvider')
  }
  return context
}

export default PWAProvider
