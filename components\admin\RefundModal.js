import { useState, useEffect } from 'react'
import Modal from './Modal'
import ConfirmationDialog from './ConfirmationDialog'
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/RefundModal.module.css'

/**
 * RefundModal component for processing payment refunds
 * Follows Ocean Soul Sparkles admin interface patterns
 */
export default function RefundModal({ 
  isOpen, 
  onClose, 
  payment, 
  onRefundSuccess 
}) {
  const [formData, setFormData] = useState({
    refund_amount: '',
    refund_reason: '',
    refund_notes: '',
    refund_method: 'manual'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [refundHistory, setRefundHistory] = useState([])
  const [refundSummary, setRefundSummary] = useState(null)

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen && payment) {
      setFormData({
        refund_amount: '',
        refund_reason: '',
        refund_notes: '',
        refund_method: determineRefundMethod(payment.payment_method)
      })
      setError(null)
      fetchRefundHistory()
    }
  }, [isOpen, payment])

  // Determine appropriate refund method based on payment method
  const determineRefundMethod = (paymentMethod) => {
    if (paymentMethod?.includes('square') || paymentMethod?.includes('card')) {
      return 'square_api'
    } else if (paymentMethod === 'cash') {
      return 'cash'
    }
    return 'manual'
  }

  // Fetch existing refund history
  const fetchRefundHistory = async () => {
    if (!payment?.id) return

    try {
      const response = await fetch(`/api/admin/payments/${payment.id}/refunds`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'dev-bypass-token'}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRefundHistory(data.refunds || [])
        setRefundSummary(data.refund_summary)
      }
    } catch (error) {
      console.error('Error fetching refund history:', error)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    setError(null)
  }

  const validateForm = () => {
    const amount = parseFloat(formData.refund_amount)
    
    if (!formData.refund_amount || isNaN(amount) || amount <= 0) {
      setError('Please enter a valid refund amount')
      return false
    }

    if (!formData.refund_reason) {
      setError('Please select a refund reason')
      return false
    }

    if (refundSummary && amount > refundSummary.remaining_amount) {
      setError(`Refund amount cannot exceed remaining amount of ${safeFormatCurrency(refundSummary.remaining_amount)}`)
      return false
    }

    return true
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setShowConfirmation(true)
  }

  const processRefund = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/admin/payments/${payment.id}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'dev-bypass-token'}`
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to process refund')
      }

      // Success
      if (onRefundSuccess) {
        onRefundSuccess(data.refund)
      }

      // Refresh refund history
      await fetchRefundHistory()

      // Reset form
      setFormData({
        refund_amount: '',
        refund_reason: '',
        refund_notes: '',
        refund_method: determineRefundMethod(payment.payment_method)
      })

      setShowConfirmation(false)

    } catch (error) {
      console.error('Error processing refund:', error)
      setError(error.message || 'Failed to process refund')
      setShowConfirmation(false)
    } finally {
      setLoading(false)
    }
  }

  const formatRefundReason = (reason) => {
    const reasons = {
      'customer_request': 'Customer Request',
      'service_issue': 'Service Issue',
      'billing_error': 'Billing Error',
      'other': 'Other'
    }
    return reasons[reason] || reason
  }

  const formatRefundMethod = (method) => {
    const methods = {
      'cash': 'Cash Refund',
      'square_api': 'Square API',
      'manual': 'Manual Processing'
    }
    return methods[method] || method
  }

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'completed': return styles.statusCompleted
      case 'pending': return styles.statusPending
      case 'failed': return styles.statusFailed
      case 'cancelled': return styles.statusCancelled
      default: return styles.statusDefault
    }
  }

  if (!payment) return null

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title="Process Refund"
        size="large"
      >
        <div className={styles.refundModal}>
          {/* Payment Details Section */}
          <div className={styles.section}>
            <h3>Payment Details</h3>
            <div className={styles.paymentInfo}>
              <div className={styles.infoRow}>
                <span className={styles.label}>Payment ID:</span>
                <span className={styles.value}>{safeRender(payment.id?.substring(0, 8))}...</span>
              </div>
              <div className={styles.infoRow}>
                <span className={styles.label}>Amount:</span>
                <span className={styles.value}>{safeFormatCurrency(payment.amount)}</span>
              </div>
              <div className={styles.infoRow}>
                <span className={styles.label}>Payment Method:</span>
                <span className={styles.value}>{safeRender(payment.payment_method)}</span>
              </div>
              <div className={styles.infoRow}>
                <span className={styles.label}>Transaction ID:</span>
                <span className={styles.value}>{safeRender(payment.transaction_id, 'N/A')}</span>
              </div>
              {refundSummary && (
                <>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>Total Refunded:</span>
                    <span className={styles.value}>{safeFormatCurrency(refundSummary.total_refunded)}</span>
                  </div>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>Remaining Amount:</span>
                    <span className={styles.value}>{safeFormatCurrency(refundSummary.remaining_amount)}</span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Refund Form Section */}
          {refundSummary?.can_refund && (
            <div className={styles.section}>
              <h3>Process New Refund</h3>
              <form onSubmit={handleSubmit} className={styles.refundForm}>
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="refund_amount">Refund Amount *</label>
                    <input
                      type="number"
                      id="refund_amount"
                      name="refund_amount"
                      value={formData.refund_amount}
                      onChange={handleInputChange}
                      step="0.01"
                      min="0.01"
                      max={refundSummary?.remaining_amount || payment.amount}
                      placeholder="0.00"
                      className={styles.input}
                      required
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="refund_reason">Refund Reason *</label>
                    <select
                      id="refund_reason"
                      name="refund_reason"
                      value={formData.refund_reason}
                      onChange={handleInputChange}
                      className={styles.select}
                      required
                    >
                      <option value="">Select reason...</option>
                      <option value="customer_request">Customer Request</option>
                      <option value="service_issue">Service Issue</option>
                      <option value="billing_error">Billing Error</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label htmlFor="refund_method">Refund Method</label>
                    <select
                      id="refund_method"
                      name="refund_method"
                      value={formData.refund_method}
                      onChange={handleInputChange}
                      className={styles.select}
                    >
                      <option value="cash">Cash Refund</option>
                      <option value="square_api">Square API</option>
                      <option value="manual">Manual Processing</option>
                    </select>
                  </div>
                </div>

                <div className={styles.formGroup}>
                  <label htmlFor="refund_notes">Refund Notes</label>
                  <textarea
                    id="refund_notes"
                    name="refund_notes"
                    value={formData.refund_notes}
                    onChange={handleInputChange}
                    placeholder="Optional notes about this refund..."
                    className={styles.textarea}
                    rows="3"
                  />
                </div>

                {error && (
                  <div className={styles.error}>
                    {error}
                  </div>
                )}

                <div className={styles.formActions}>
                  <button
                    type="button"
                    onClick={onClose}
                    className={styles.cancelButton}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className={styles.submitButton}
                    disabled={loading}
                  >
                    {loading ? 'Processing...' : 'Process Refund'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Refund History Section */}
          {refundHistory.length > 0 && (
            <div className={styles.section}>
              <h3>Refund History</h3>
              <div className={styles.refundHistory}>
                {refundHistory.map((refund) => (
                  <div key={refund.id} className={styles.refundItem}>
                    <div className={styles.refundHeader}>
                      <span className={styles.refundAmount}>
                        {safeFormatCurrency(refund.refund_amount)}
                      </span>
                      <span className={`${styles.statusBadge} ${getStatusBadgeClass(refund.refund_status)}`}>
                        {refund.refund_status}
                      </span>
                    </div>
                    <div className={styles.refundDetails}>
                      <div className={styles.refundMeta}>
                        <span>Reason: {formatRefundReason(refund.refund_reason)}</span>
                        <span>Method: {formatRefundMethod(refund.refund_method)}</span>
                        <span>By: {refund.processed_by.email}</span>
                      </div>
                      <div className={styles.refundDate}>
                        {new Date(refund.processed_at).toLocaleString('en-AU')}
                      </div>
                      {refund.refund_notes && (
                        <div className={styles.refundNotes}>
                          Notes: {refund.refund_notes}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!refundSummary?.can_refund && (
            <div className={styles.noRefundMessage}>
              <p>This payment cannot be refunded. It may already be fully refunded or not in a completed state.</p>
            </div>
          )}
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        title="Confirm Refund"
        message={`Are you sure you want to process a refund of ${safeFormatCurrency(formData.refund_amount)} for this payment? This action cannot be undone.`}
        onConfirm={processRefund}
        onCancel={() => setShowConfirmation(false)}
        confirmText="Process Refund"
        cancelText="Cancel"
        confirmButtonClass={styles.confirmRefund}
      />
    </>
  )
}
