# Booking Status History Fix

This document describes the implementation of the fix for the foreign key relationship issue between the `booking_status_history` table and the `auth.users` table.

## Issue Description

The `BookingStatusHistory.js` component was experiencing a 400 Bad Request error when trying to fetch booking status history. The error occurred when attempting to join the `booking_status_history` table with user data through the `changed_by` field.

The specific error message was:
```
Error fetching booking status history: {
  code: 'PGRST200', 
  details: "Searched for a foreign key relationship between 'booking_status_history' and 'changed_by' in the schema 'public', but no matches were found.", 
  hint: null, 
  message: "Could not find a relationship between 'booking_status_history' and 'changed_by' in the schema cache"
}
```

The problematic query was trying to join the `booking_status_history` table with the `users` table using the syntax:
```javascript
users:changed_by(email,display_name)
```

## Solution Implemented

### 1. Created a User Profiles View

We created a SQL view called `user_profiles` that exposes selected fields from the `auth.users` table and joins it with the `user_roles` table:

```sql
CREATE VIEW public.user_profiles AS
SELECT 
  au.id,
  au.email,
  au.raw_user_meta_data->>'name' as display_name,
  au.created_at,
  au.last_sign_in_at,
  ur.role
FROM 
  auth.users au
LEFT JOIN 
  public.user_roles ur ON au.id = ur.id;
```

This view makes it easier to join with user data in queries.

### 2. Updated the BookingStatusHistory.js Component

We modified the query in the `BookingStatusHistory.js` component to use the `user_profiles` view with the foreign key constraint name:

```javascript
// Fetch status history from Supabase
const { data, error } = await supabase
  .from('booking_status_history')
  .select(`
    id,
    previous_status,
    new_status,
    notes,
    created_at,
    changed_by,
    user_profiles!booking_status_history_changed_by_fkey (email, display_name, role)
  `)
  .eq('booking_id', bookingId)
  .order('created_at', { ascending: false });
```

We also updated the reference to the user in the JSX to use `item.user_profiles` instead of `item.users`.

## Implementation Steps

### 1. Created the User Profiles View

We executed SQL to create the `user_profiles` view in the Supabase database:

```sql
-- Create a view to expose user profile information
DROP VIEW IF EXISTS public.user_profiles;

-- Create the view
CREATE VIEW public.user_profiles AS
SELECT 
  au.id,
  au.email,
  au.raw_user_meta_data->>'name' as display_name,
  au.created_at,
  au.last_sign_in_at,
  ur.role
FROM 
  auth.users au
LEFT JOIN 
  public.user_roles ur ON au.id = ur.id;

-- Grant permissions
GRANT SELECT ON public.user_profiles TO authenticated;
GRANT SELECT ON public.user_profiles TO service_role;
```

### 2. Verified the Foreign Key Relationship

We confirmed that the foreign key relationship between `booking_status_history.changed_by` and `auth.users(id)` was correctly established:

```sql
SELECT conname AS constraint_name,
       conrelid::regclass AS table_name,
       pg_get_constraintdef(oid) AS constraint_definition
FROM pg_constraint
WHERE conrelid = 'booking_status_history'::regclass
  AND contype = 'f';
```

Result:
```
[
  {
    "constraint_name": "booking_status_history_booking_id_fkey",
    "table_name": "booking_status_history",
    "constraint_definition": "FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE"
  },
  {
    "constraint_name": "booking_status_history_changed_by_fkey",
    "table_name": "booking_status_history",
    "constraint_definition": "FOREIGN KEY (changed_by) REFERENCES auth.users(id)"
  }
]
```

### 3. Tested the Join Query

We tested the join between `booking_status_history` and `user_profiles` using SQL:

```sql
SELECT 
  bsh.id, 
  bsh.previous_status, 
  bsh.new_status, 
  bsh.notes, 
  bsh.created_at, 
  bsh.changed_by, 
  json_build_object(
    'email', up.email,
    'display_name', up.display_name,
    'role', up.role
  ) as user_profiles
FROM 
  booking_status_history bsh
LEFT JOIN 
  user_profiles up ON bsh.changed_by = up.id
WHERE 
  bsh.booking_id = '3e4b4ae6-b078-4c39-9b24-e89f5483820b'
ORDER BY 
  bsh.created_at DESC
LIMIT 5;
```

### 4. Created a Test Page

We created a test page at `/admin/test-status-history` to verify that the `BookingStatusHistory` component works correctly with the new view.

## Issues Encountered and Resolutions

### 1. Row Level Security (RLS) on Views

We initially tried to enable RLS on the `user_profiles` view:

```sql
ALTER VIEW public.user_profiles ENABLE ROW LEVEL SECURITY;
```

However, this resulted in an error:
```
ERROR: 42809: ALTER action ENABLE ROW SECURITY cannot be performed on relation "user_profiles"
DETAIL: This operation is not supported for views.
```

**Resolution**: We removed the RLS statements from the view creation SQL. Instead, we rely on the RLS policies of the underlying tables (`auth.users` and `user_roles`) to control access to the view.

### 2. Script Execution Issues

We initially created a Node.js script to apply the migration, but encountered issues with the execution environment. 

**Resolution**: We used the Supabase API directly to execute the SQL statements.

## Testing

We tested the solution by:

1. Verifying that the `user_profiles` view was created successfully
2. Confirming that the foreign key relationship between `booking_status_history.changed_by` and `auth.users(id)` was correctly established
3. Testing the join between `booking_status_history` and `user_profiles` using SQL
4. Creating a test page to verify that the `BookingStatusHistory` component works correctly with the new view

## Conclusion

The implementation of the `user_profiles` view has successfully resolved the foreign key relationship issue between the `booking_status_history` table and the `auth.users` table. The `BookingStatusHistory` component can now successfully retrieve and display user information using the new view.

This solution follows the established Supabase authentication and database access patterns in the application and provides a reusable approach for joining with user data in other parts of the application.
