-- Phase 6: AI-Powered Features & Automation Database Schema Updates
-- Ocean Soul Sparkles - AI Features Migration
-- Created: January 15, 2025

-- Add AI-related columns to existing tables
BEGIN;

-- Add AI optimization columns to artist_profiles table
ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS 
  ai_optimization_enabled BOOLEAN DEFAULT true;

ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS 
  last_ai_analysis TIMESTAMP;

ALTER TABLE artist_profiles ADD COLUMN IF NOT EXISTS 
  ai_performance_score DECIMAL(3,2);

-- Add AI-related columns to bookings table
ALTER TABLE bookings ADD COLUMN IF NOT EXISTS
  ai_optimized BOOLEAN DEFAULT false;

ALTER TABLE bookings ADD COLUMN IF NOT EXISTS
  ai_confidence_score DECIMAL(3,2);

ALTER TABLE bookings ADD COLUMN IF NOT EXISTS
  travel_time_minutes INTEGER;

ALTER TABLE bookings ADD COLUMN IF NOT EXISTS
  ai_optimization_applied_at TIMESTAMP;

ALTER TABLE bookings ADD COLUMN IF NOT EXISTS
  original_start_time TIMESTAMP;

-- <PERSON>reate AI insights cache table
CREATE TABLE IF NOT EXISTS ai_insights_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cache_key VARCHAR(255) UNIQUE NOT NULL,
  insights_data JSONB NOT NULL,
  insight_type VARCHAR(50) DEFAULT 'daily',
  target_date DATE NOT NULL,
  confidence_level DECIMAL(3,2) DEFAULT 0.5,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL,
  
  -- Indexes for performance
  CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1),
  CONSTRAINT valid_insight_type CHECK (insight_type IN ('daily', 'weekly', 'monthly'))
);

-- Create indexes for ai_insights_cache
CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_key ON ai_insights_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_date ON ai_insights_cache(target_date);
CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_expires ON ai_insights_cache(expires_at);

-- Create AI recommendations table
CREATE TABLE IF NOT EXISTS ai_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(50) NOT NULL,
  category VARCHAR(50) NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium',
  target_id UUID,
  target_type VARCHAR(50),
  recommendation_data JSONB NOT NULL,
  confidence_score DECIMAL(3,2) DEFAULT 0.5,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  applied_at TIMESTAMP,
  dismissed_at TIMESTAMP,
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT valid_priority CHECK (priority IN ('low', 'medium', 'high')),
  CONSTRAINT valid_status CHECK (status IN ('pending', 'applied', 'dismissed', 'expired')),
  CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Create indexes for ai_recommendations
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_type ON ai_recommendations(type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_status ON ai_recommendations(status);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_priority ON ai_recommendations(priority);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_target ON ai_recommendations(target_id, target_type);
CREATE INDEX IF NOT EXISTS idx_ai_recommendations_created ON ai_recommendations(created_at);

-- Create AI compatibility scores table for customer-artist matching
CREATE TABLE IF NOT EXISTS ai_compatibility_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  artist_id UUID NOT NULL REFERENCES artist_profiles(id) ON DELETE CASCADE,
  compatibility_score DECIMAL(4,3) NOT NULL,
  confidence_level DECIMAL(3,2) NOT NULL,
  factors JSONB,
  last_calculated TIMESTAMP DEFAULT NOW(),
  calculation_version VARCHAR(10) DEFAULT '1.0',
  
  -- Unique constraint to prevent duplicates
  UNIQUE(customer_id, artist_id),
  
  -- Constraints
  CONSTRAINT valid_compatibility_score CHECK (compatibility_score >= 0 AND compatibility_score <= 1),
  CONSTRAINT valid_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1)
);

-- Create indexes for ai_compatibility_scores
CREATE INDEX IF NOT EXISTS idx_ai_compatibility_customer ON ai_compatibility_scores(customer_id);
CREATE INDEX IF NOT EXISTS idx_ai_compatibility_artist ON ai_compatibility_scores(artist_id);
CREATE INDEX IF NOT EXISTS idx_ai_compatibility_score ON ai_compatibility_scores(compatibility_score DESC);
CREATE INDEX IF NOT EXISTS idx_ai_compatibility_calculated ON ai_compatibility_scores(last_calculated);

-- Create AI travel time cache table
CREATE TABLE IF NOT EXISTS ai_travel_time_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  origin_location TEXT NOT NULL,
  destination_location TEXT NOT NULL,
  travel_time_minutes INTEGER NOT NULL,
  distance_meters INTEGER,
  traffic_factor DECIMAL(3,2) DEFAULT 1.0,
  departure_time_slot VARCHAR(10), -- e.g., '09:00', '14:30'
  day_of_week INTEGER, -- 0-6, Sunday = 0
  cached_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL,
  source VARCHAR(20) DEFAULT 'google_maps',
  
  -- Unique constraint for cache key
  UNIQUE(origin_location, destination_location, departure_time_slot, day_of_week),
  
  -- Constraints
  CONSTRAINT valid_day_of_week CHECK (day_of_week >= 0 AND day_of_week <= 6),
  CONSTRAINT valid_travel_time CHECK (travel_time_minutes >= 0),
  CONSTRAINT valid_distance CHECK (distance_meters >= 0)
);

-- Create indexes for ai_travel_time_cache
CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_locations ON ai_travel_time_cache(origin_location, destination_location);
CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_expires ON ai_travel_time_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_ai_travel_cache_time_slot ON ai_travel_time_cache(departure_time_slot, day_of_week);

-- Create AI optimization history table
CREATE TABLE IF NOT EXISTS ai_optimization_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  artist_id UUID NOT NULL REFERENCES artist_profiles(id) ON DELETE CASCADE,
  optimization_date DATE NOT NULL,
  optimization_type VARCHAR(50) NOT NULL,
  original_schedule JSONB,
  optimized_schedule JSONB,
  improvements JSONB,
  applied BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  applied_at TIMESTAMP,
  created_by UUID REFERENCES auth.users(id),
  
  -- Constraints
  CONSTRAINT valid_optimization_type CHECK (optimization_type IN ('schedule', 'travel', 'conflict_resolution', 'efficiency'))
);

-- Create indexes for ai_optimization_history
CREATE INDEX IF NOT EXISTS idx_ai_optimization_artist ON ai_optimization_history(artist_id);
CREATE INDEX IF NOT EXISTS idx_ai_optimization_date ON ai_optimization_history(optimization_date);
CREATE INDEX IF NOT EXISTS idx_ai_optimization_type ON ai_optimization_history(optimization_type);
CREATE INDEX IF NOT EXISTS idx_ai_optimization_applied ON ai_optimization_history(applied);

-- Add AI settings to admin_settings if not exists
INSERT INTO admin_settings (setting_key, setting_value, setting_type, description, created_at, updated_at)
VALUES 
  ('ai_features_enabled', 'true', 'boolean', 'Enable AI-powered features and automation', NOW(), NOW()),
  ('ai_cache_duration', '1800', 'number', 'AI cache duration in seconds (30 minutes)', NOW(), NOW()),
  ('ai_max_recommendations', '10', 'number', 'Maximum number of AI recommendations to generate', NOW(), NOW()),
  ('ai_confidence_threshold', '0.7', 'number', 'Minimum confidence threshold for AI recommendations', NOW(), NOW()),
  ('ai_optimization_auto_apply', 'false', 'boolean', 'Automatically apply AI optimizations with high confidence', NOW(), NOW())
ON CONFLICT (setting_key) DO NOTHING;

-- Create function to clean up expired AI cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_ai_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Clean up expired insights cache
  DELETE FROM ai_insights_cache WHERE expires_at < NOW();
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Clean up expired travel time cache
  DELETE FROM ai_travel_time_cache WHERE expires_at < NOW();
  
  -- Clean up old compatibility scores (older than 30 days)
  DELETE FROM ai_compatibility_scores WHERE last_calculated < NOW() - INTERVAL '30 days';
  
  -- Clean up old optimization history (older than 90 days)
  DELETE FROM ai_optimization_history WHERE created_at < NOW() - INTERVAL '90 days';
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to update AI performance scores
CREATE OR REPLACE FUNCTION update_ai_performance_scores()
RETURNS VOID AS $$
BEGIN
  -- Update artist AI performance scores based on recent bookings
  UPDATE artist_profiles 
  SET 
    ai_performance_score = (
      SELECT COALESCE(AVG(customer_rating), 0) / 5.0
      FROM bookings 
      WHERE assigned_artist_id = artist_profiles.id 
        AND customer_rating IS NOT NULL 
        AND start_time > NOW() - INTERVAL '30 days'
    ),
    last_ai_analysis = NOW()
  WHERE ai_optimization_enabled = true;
END;
$$ LANGUAGE plpgsql;

-- Create RLS policies for AI tables
ALTER TABLE ai_insights_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_compatibility_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_travel_time_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_optimization_history ENABLE ROW LEVEL SECURITY;

-- RLS policies for admin access
CREATE POLICY "Admin full access to ai_insights_cache" ON ai_insights_cache
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );

CREATE POLICY "Admin full access to ai_recommendations" ON ai_recommendations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );

CREATE POLICY "Admin full access to ai_compatibility_scores" ON ai_compatibility_scores
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );

CREATE POLICY "Admin full access to ai_travel_time_cache" ON ai_travel_time_cache
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );

CREATE POLICY "Admin full access to ai_optimization_history" ON ai_optimization_history
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.role IN ('admin', 'dev')
    )
  );

-- Artist access to their own AI data
CREATE POLICY "Artists can view their ai_optimization_history" ON ai_optimization_history
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM artist_profiles 
      WHERE artist_profiles.id = ai_optimization_history.artist_id 
      AND artist_profiles.user_id = auth.uid()
    )
  );

CREATE POLICY "Artists can view their ai_compatibility_scores" ON ai_compatibility_scores
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM artist_profiles 
      WHERE artist_profiles.id = ai_compatibility_scores.artist_id 
      AND artist_profiles.user_id = auth.uid()
    )
  );

-- Add comments for documentation
COMMENT ON TABLE ai_insights_cache IS 'Caches AI-generated business insights and analytics';
COMMENT ON TABLE ai_recommendations IS 'Stores AI-generated recommendations for business optimization';
COMMENT ON TABLE ai_compatibility_scores IS 'Customer-artist compatibility scores for intelligent matching';
COMMENT ON TABLE ai_travel_time_cache IS 'Caches travel time calculations for schedule optimization';
COMMENT ON TABLE ai_optimization_history IS 'History of AI schedule optimizations and their results';

COMMENT ON FUNCTION cleanup_expired_ai_cache() IS 'Cleans up expired AI cache entries and old data';
COMMENT ON FUNCTION update_ai_performance_scores() IS 'Updates AI performance scores for artists based on recent bookings';

COMMIT;

-- Success message
SELECT 'Phase 6: AI-Powered Features database schema migration completed successfully!' as message;
