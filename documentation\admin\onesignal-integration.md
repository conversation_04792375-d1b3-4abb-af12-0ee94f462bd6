# OneSignal Integration

This document provides detailed implementation information for integrating OneSignal with the OceanSoulSparkles admin panel for notifications.

## Overview

OneSignal is a comprehensive customer engagement solution that enables the OceanSoulSparkles admin panel to send push notifications, emails, SMS, and in-app messages to customers. This integration replaces the previously planned SendGrid integration for a more unified notification approach.

## Current Implementation Status

The OneSignal integration has been partially implemented with the following components:

1. **OneSignal Initialization** (`lib/onesignal.js`)
   - Basic initialization utilities created
   - Configuration for web push notifications
   - Helper functions for setting user IDs and emails

2. **Notification Service** (`lib/notifications.js`)
   - Functions for sending notifications to users
   - Support for both push and email channels
   - Integration with booking management system

3. **Booking Notifications**
   - Notification sending for booking creation
   - Notification sending for booking status updates
   - Notification sending for booking cancellations

## Features

- Push notifications for web and mobile
- Email notifications
- SMS notifications (with additional setup)
- In-app messaging
- Automated notification workflows
- Notification analytics
- User segmentation

## Setup Instructions

### 1. Create a OneSignal Account

1. Sign up for a OneSignal account at [https://onesignal.com](https://onesignal.com)
2. Create a new app in the OneSignal dashboard
3. Configure the app for web push notifications
4. Set up email notifications in the OneSignal dashboard

### 2. Configure Environment Variables

Add the following environment variables to your `.env.local` file:

```
NEXT_PUBLIC_ONESIGNAL_APP_ID=your-onesignal-app-id
NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID=your-safari-web-id
ONESIGNAL_REST_API_KEY=your-rest-api-key
```

Add these variables to your Vercel project settings as well.

### 3. Install OneSignal SDK

Install the OneSignal SDK in your Next.js project:

```bash
npm install react-onesignal
```

### 4. OneSignal Implementation Details

#### Current Implementation

The OneSignal integration has been implemented with the following components:

1. **OneSignal Initialization** (`lib/onesignal.js`)

The OneSignal initialization utility has been created with the following features:

- Dynamic import of the OneSignal SDK to prevent server-side rendering issues
- Configuration for web push notifications with customized appearance
- Helper functions for setting user IDs and emails
- Conditional initialization to disable in development mode
- Support for Safari web push notifications

```javascript
// lib/onesignal.js (excerpt)
export const initializeOneSignal = () => {
  // Skip initialization in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('OneSignal initialization skipped in development mode');
    return Promise.resolve();
  }

  if (typeof window !== 'undefined') {
    return import('react-onesignal')
      .then(module => {
        const OneSignal = module.default;

        return OneSignal.init({
          appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
          safari_web_id: process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID,
          // Configuration options...
        });
      });
  }
  return Promise.resolve();
};
```

2. **Notification Service** (`lib/notifications.js`)

The notification service has been implemented with the following features:

- Functions for sending notifications to users via different channels
- Support for both push and email notifications
- Integration with the booking management system
- Development mode handling to prevent actual notifications during development
- Database recording of all notifications for history and tracking

```javascript
// lib/notifications.js (excerpt)
export async function sendNotification({
  userId,
  title,
  message,
  data = {},
  url,
  channel = 'push'
}) {
  try {
    // Skip actual notification in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode: Notification would be sent', {
        userId, title, message, data, url, channel
      });

      // Still record notification in database
      await supabase.from('notifications').insert([{
        user_id: userId,
        title,
        message,
        notification_type: data.type || 'general',
        related_id: data.id,
        is_read: false
      }]);

      return { success: true, development: true };
    }

    // Send actual notification in production
    // ...
  } catch (error) {
    console.error('Error sending notification:', error);
    return { success: false, error: error.message };
  }
}
```

#### OneSignal Initialization in _app.js

OneSignal is initialized in the application's `_app.js` file using the `useOneSignal` hook:

```javascript
// pages/_app.js (excerpt)
import { useOneSignal } from '@/lib/onesignal'

function MyApp({ Component, pageProps }) {
  // Initialize OneSignal
  useOneSignal()

  // Rest of the component...
}
```

This ensures that OneSignal is initialized once when the application loads and is available throughout the application.

### 6. Create Notification Service

Create a notification service for sending notifications:

```javascript
// lib/notification-service.js
import { supabase } from './supabase'

// Send notification to a specific user
export async function sendNotification(userId, title, message, data = {}, options = {}) {
  try {
    const {
      emailSubject,
      emailBody,
      url,
      channel = 'push', // 'push', 'email', or 'sms'
      scheduleTime = null
    } = options;

    // Prepare notification payload
    const payload = {
      app_id: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
      include_external_user_ids: [userId],
      headings: { en: title },
      contents: { en: message },
      data,
      channel_for_external_user_ids: channel,
      web_url: url
    };

    // Add email specific properties if channel is email
    if (channel === 'email') {
      payload.email_subject = emailSubject || title;
      payload.email_body = emailBody || message;
    }

    // Add scheduling if provided
    if (scheduleTime) {
      payload.send_after = new Date(scheduleTime).toISOString();
    }

    // Send notification via OneSignal REST API
    const response = await fetch('https://onesignal.com/api/v1/notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${process.env.ONESIGNAL_REST_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`OneSignal API error: ${JSON.stringify(result)}`);
    }

    // Record notification in database
    await supabase
      .from('notifications')
      .insert([
        {
          user_id: userId,
          title,
          message,
          notification_type: data.type || 'general',
          related_id: data.id,
          is_read: false
        }
      ]);

    return {
      success: true,
      notification_id: result.id
    };
  } catch (error) {
    console.error('Error sending notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Send notification to multiple users
export async function sendBulkNotification(userIds, title, message, data = {}, options = {}) {
  try {
    // Implementation similar to sendNotification but with multiple user IDs
    // ...

    return { success: true };
  } catch (error) {
    console.error('Error sending bulk notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Mark notification as read
export async function markNotificationAsRead(notificationId) {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
```

### 7. Set Up OneSignal Webhooks

1. In the OneSignal dashboard, go to Settings > Webhooks
2. Create a new webhook for notification events
3. Set the webhook URL to your API endpoint (e.g., `https://yourdomain.com/api/webhooks/onesignal`)
4. Select the events you want to receive (e.g., Opened, Clicked)

Create an API endpoint to handle OneSignal webhooks:

```javascript
// pages/api/webhooks/onesignal.js
import { supabase } from '@/lib/supabase'

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const events = req.body;

    // Process each event
    for (const event of events) {
      const { event: eventType, notification_id, data } = event;

      // Record event in database
      await supabase
        .from('notification_events')
        .insert([
          {
            notification_id,
            event_type: eventType,
            event_data: event
          }
        ]);

      // Update metrics if this is a campaign notification
      if (data && data.campaign_id) {
        await supabase
          .from('campaign_metrics')
          .insert([
            {
              campaign_id: data.campaign_id,
              metric_type: eventType,
              metric_value: 1
            }
          ]);
      }
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error processing OneSignal webhook:', error);
    return res.status(500).json({ error: 'Failed to process webhook' });
  }
}
```

## Usage Examples

### Subscribing a User to Notifications

```javascript
import { setOneSignalExternalUserId, setOneSignalEmail } from '@/lib/onesignal'

// When a user logs in or signs up
async function onUserAuthenticated(user) {
  // Set the user's ID in OneSignal
  await setOneSignalExternalUserId(user.id);

  // Set the user's email in OneSignal
  if (user.email) {
    await setOneSignalEmail(user.email);
  }
}
```

### Sending a Notification

```javascript
import { sendNotification } from '@/lib/notification-service'

// Send a push notification
async function sendOrderConfirmation(order, customer) {
  const result = await sendNotification(
    customer.id,
    'Order Confirmation',
    `Your order #${order.id} has been confirmed.`,
    {
      type: 'order_confirmation',
      id: order.id
    },
    {
      url: `/orders/${order.id}`
    }
  );

  return result;
}

// Send an email notification
async function sendShippingNotification(order, customer) {
  const result = await sendNotification(
    customer.id,
    'Order Shipped',
    `Your order #${order.id} has been shipped.`,
    {
      type: 'order_shipped',
      id: order.id
    },
    {
      channel: 'email',
      emailSubject: `Your OceanSoulSparkles Order #${order.id} Has Shipped`,
      emailBody: `<div>Your order has been shipped...</div>`,
      url: `/orders/${order.id}`
    }
  );

  return result;
}
```

## Implementation Plan

The following tasks need to be completed to fully implement the OneSignal notification system:

### 1. Complete Booking Notification System

- **Booking Reminders**: Implement scheduled notifications to remind customers about upcoming bookings (24 hours before appointment)
- **Booking Confirmation Emails**: Enhance email templates for booking confirmations with branded HTML content
- **Booking Update Notifications**: Improve notification content for booking updates with more detailed information about changes

### 2. Implement Customer Notification Preferences

- Create a customer preferences page where customers can manage their notification settings
- Implement category-based notification preferences (bookings, marketing, orders)
- Add support for notification frequency settings

### 3. Implement Admin Notification Dashboard

- Create a notification dashboard in the admin panel
- Add ability to view sent notifications and their status
- Implement notification analytics and reporting
- Add ability to send manual notifications to customers

### 4. Set Up OneSignal Webhooks

- Configure OneSignal webhooks for tracking notification delivery and interactions
- Create API endpoints to handle webhook events
- Implement notification analytics based on webhook data

## Testing Plan

1. **Unit Testing**
   - Test OneSignal initialization with mocked environment
   - Test notification sending functions with mocked API calls
   - Test notification database recording

2. **Integration Testing**
   - Test OneSignal initialization in the application
   - Test push notification subscription flow
   - Test sending actual notifications in a staging environment
   - Test notification webhooks with test events

3. **User Acceptance Testing**
   - Test notification delivery on different devices and browsers
   - Test email notification delivery and rendering
   - Test notification preferences management
   - Test notification analytics and reporting

## Security Considerations

- Protect OneSignal API keys using environment variables
- Validate webhook requests using OneSignal authentication
- Implement proper authentication for notification-related API endpoints
- Ensure GDPR compliance for user notifications
- Provide clear opt-out mechanisms for all notification channels
- Implement rate limiting for notification sending to prevent abuse
