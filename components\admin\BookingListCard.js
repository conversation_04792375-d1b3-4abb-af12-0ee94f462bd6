import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/BookingListCard.module.css'; // New CSS module

const BookingListCard = ({ artistId }) => {
  const { supabaseClient, user, loading: authLoading } = useAuth();
  const [bookings, setBookings] = useState([]);
  const [filter, setFilter] = useState('upcoming'); // 'upcoming' or 'past'
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const limit = 10; // Number of bookings per page

  const fetchBookings = useCallback(async (currentFilter, currentPage, append = false) => {
    if (!artistId && user?.artistProfile?.id) { // Fallback to user.artistProfile.id if artistId prop isn't directly passed
      artistId = user.artistProfile.id;
    }
    if (!artistId || !supabaseClient || !user?.access_token) {
      if (!authLoading && !artistId) setError("Artist profile ID not available.");
      else if (!authLoading) setError("User or artist information is not available.");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError('');

    try {
      const queryParams = new URLSearchParams({
        filter: currentFilter, // Changed from 'status' to 'filter' to match planned API
        limit: limit.toString(),
        offset: ((currentPage - 1) * limit).toString(),
        // artistId: artistId, // The API should get artistId from the authenticated user
      });

      const response = await fetch(`/api/artist/bookings?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${supabaseClient.auth.session()?.access_token}`,
        },
      });

      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();

      if (append) {
        setBookings(prev => [...prev, ...data]);
      } else {
        setBookings(data);
      }
      setHasMore(data.length === limit);

    } catch (err) {
      console.error(`Error fetching ${currentFilter} bookings:`, err);
      setError(`Failed to load ${currentFilter} bookings. ${err.message}`);
      // toast.error(`Failed to load ${currentFilter} bookings.`); // Potentially too noisy
    } finally {
      setLoading(false);
    }
  }, [artistId, supabaseClient, user?.access_token, user?.artistProfile?.id, limit, authLoading, filter]); // Added filter to dependencies

  useEffect(() => {
    // Use artistId prop or fallback to user.artistProfile.id from context
    const currentArtistId = artistId || user?.artistProfile?.id;
    if (currentArtistId && !authLoading) {
      setPage(1); // Reset page when filter or artistId changes
      setBookings([]); // Clear previous bookings
      fetchBookings(filter, 1, false);
    } else if (!authLoading && !currentArtistId) {
        setError("Artist ID not available to fetch bookings.");
    }
  }, [artistId, user?.artistProfile?.id, filter, authLoading]); // Removed fetchBookings from dependencies to prevent loops

  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
    // Fetching will be triggered by the useEffect above due to 'filter' changing
  };

  const loadMoreBookings = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      // Pass current artistId to fetchBookings explicitly if needed, or rely on its internal logic
      fetchBookings(filter, nextPage, true);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric', month: 'short', day: 'numeric',
            hour: '2-digit', minute: '2-digit', hour12: true
        });
    } catch (e) {
        console.warn("Error formatting date:", dateString, e);
        return dateString; // Fallback to original string if parsing fails
    }
  };

  if (authLoading && !user) { // Only show initial auth loading if user is not yet available
    return <div className={styles.loadingState}>Authenticating...</div>;
  }

  const currentArtistId = artistId || user?.artistProfile?.id;
  if (!currentArtistId && !loading && !authLoading) {
     return <div className={styles.errorState}>Artist information not available. Cannot load bookings.</div>;
  }

  return (
    <div className={styles.bookingListCard}>
      <div className={styles.cardHeader}>
        <h3>Booking History</h3>
        <div className={styles.filters}>
          <button
            onClick={() => handleFilterChange('upcoming')}
            className={`${styles.filterButton} ${filter === 'upcoming' ? styles.activeFilter : ''}`}
            disabled={loading}
          >
            Upcoming
          </button>
          <button
            onClick={() => handleFilterChange('past')}
            className={`${styles.filterButton} ${filter === 'past' ? styles.activeFilter : ''}`}
            disabled={loading}
          >
            Past
          </button>
        </div>
      </div>

      {error && <p className={styles.error}>{error}</p>}

      {loading && bookings.length === 0 && <div className={styles.loadingState}><div className={styles.spinner}></div>Loading bookings...</div>}
      {!loading && bookings.length === 0 && !error && <p className={styles.emptyState}>No {filter} bookings found.</p>}

      {bookings.length > 0 && (
        <ul className={styles.bookingList}>
          {bookings.map((booking) => (
            <li key={booking.id} className={styles.bookingItem}>
              <div className={styles.bookingItemHeader}>
                <span className={styles.serviceName}>{booking.service_name || booking.services?.name || 'Service Unavailable'}</span>
                <span className={`${styles.statusBadge} ${styles[booking.status?.toLowerCase() || 'unknown']}`}>
                  {booking.status || 'N/A'}
                </span>
              </div>
              <div className={styles.bookingItemBody}>
                <p><strong>Date:</strong> {formatDate(booking.start_time)}</p>
                <p><strong>Customer:</strong> {booking.customer_name || booking.customers?.name || 'N/A'}</p>
                {booking.location && <p><strong>Location:</strong> {booking.location}</p>}
                {booking.notes && <p className={styles.notes}><strong>Notes:</strong> {booking.notes}</p>}
              </div>
            </li>
          ))}
        </ul>
      )}

      {hasMore && !loading && bookings.length > 0 && (
        <button onClick={loadMoreBookings} className={styles.loadMoreButton} disabled={loading}>
          Load More
        </button>
      )}
       {loading && bookings.length > 0 && <div className={styles.loadingState}><div className={styles.spinner}></div>Loading more bookings...</div>}
    </div>
  );
};

export default BookingListCard;
