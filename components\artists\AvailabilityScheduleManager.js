import { useState, useEffect } from 'react';
import styles from '@/styles/artists/AvailabilityScheduleManager.module.css'; // To be created

const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export default function AvailabilityScheduleManager({ currentAvailability = [], onSubmit, isSubmitting }) {
  // Store availability grouped by day for easier management
  // e.g., { 0: [{start_time: '09:00', end_time: '17:00'}], 1: [], ... }
  const [schedule, setSchedule] = useState({});

  useEffect(() => {
    // Initialize schedule from props
    const initialSchedule = {};
    daysOfWeek.forEach((_, index) => {
      initialSchedule[index] = currentAvailability
        .filter(slot => slot.day_of_week === index)
        .map(slot => ({ start_time: slot.start_time, end_time: slot.end_time }))
        .sort((a, b) => (a.start_time || '').localeCompare(b.start_time || ''));
    });
    setSchedule(initialSchedule);
  }, [currentAvailability]);

  const handleAddTimeSlot = (dayIndex) => {
    setSchedule(prev => {
      const daySlots = prev[dayIndex] ? [...prev[dayIndex]] : [];
      // Add a default new slot, or prompt user, or use fixed values for now
      daySlots.push({ start_time: '09:00', end_time: '17:00' });
      daySlots.sort((a, b) => (a.start_time || '').localeCompare(b.start_time || ''));
      return { ...prev, [dayIndex]: daySlots };
    });
  };

  const handleRemoveTimeSlot = (dayIndex, slotIndex) => {
    setSchedule(prev => {
      const daySlots = [...(prev[dayIndex] || [])];
      daySlots.splice(slotIndex, 1);
      return { ...prev, [dayIndex]: daySlots };
    });
  };

  const handleTimeChange = (dayIndex, slotIndex, field, value) => {
    // Basic time validation (HH:MM)
    if (value && !/^([01]\d|2[0-3]):([0-5]\d)$/.test(value)) {
        // Optionally show an error or prevent update for invalid format immediately
        console.warn("Invalid time format, should be HH:MM");
        // return; // Or allow typing and validate on submit
    }

    setSchedule(prev => {
      const daySlots = [...(prev[dayIndex] || [])];
      const updatedSlot = { ...daySlots[slotIndex], [field]: value };

      // Ensure end_time is after start_time if both are set
      if (updatedSlot.start_time && updatedSlot.end_time && updatedSlot.start_time >= updatedSlot.end_time) {
          if (field === 'start_time') updatedSlot.end_time = ''; // Clear end_time if start_time makes it invalid
          // Or provide visual feedback about the error
      }

      daySlots[slotIndex] = updatedSlot;
      daySlots.sort((a, b) => (a.start_time || '').localeCompare(b.start_time || ''));
      return { ...prev, [dayIndex]: daySlots };
    });
  };

  const handleSetDayUnavailable = (dayIndex) => {
    setSchedule(prev => ({ ...prev, [dayIndex]: [] })); // Empty array means unavailable
  };

  const handleSubmit = () => {
    const flatSchedule = [];
    for (const dayIndex in schedule) {
      schedule[dayIndex].forEach(slot => {
        // Validate slot before adding to submission
        if (slot.start_time && slot.end_time && slot.start_time < slot.end_time &&
            /^([01]\d|2[0-3]):([0-5]\d)$/.test(slot.start_time) &&
            /^([01]\d|2[0-3]):([0-5]\d)$/.test(slot.end_time) ) {
          flatSchedule.push({
            day_of_week: parseInt(dayIndex),
            start_time: slot.start_time, // Ensure format is HH:MM or HH:MM:SS if API needs seconds
            end_time: slot.end_time,
          });
        } else {
            // Optionally, collect errors and display them instead of silent skip
            console.warn("Skipping invalid slot for day", dayIndex, slot);
        }
      });
    }
    onSubmit(flatSchedule);
  };

  return (
    <div className={styles.scheduleManager}>
      <h3>Set Your Weekly Recurring Availability</h3>
      {daysOfWeek.map((dayName, dayIndex) => (
        <div key={dayIndex} className={styles.daySection}>
          <h4>{dayName}</h4>
          {(schedule[dayIndex] || []).length === 0 && (
            <p className={styles.unavailableText}>Unavailable</p>
          )}
          {(schedule[dayIndex] || []).map((slot, slotIndex) => (
            <div key={slotIndex} className={styles.timeSlot}>
              <input
                type="time"
                value={slot.start_time || ''}
                onChange={(e) => handleTimeChange(dayIndex, slotIndex, 'start_time', e.target.value)}
                className={styles.timeInput}
              />
              <span className={styles.timeSeparator}>to</span>
              <input
                type="time"
                value={slot.end_time || ''}
                onChange={(e) => handleTimeChange(dayIndex, slotIndex, 'end_time', e.target.value)}
                className={styles.timeInput}
              />
              <button
                onClick={() => handleRemoveTimeSlot(dayIndex, slotIndex)}
                className={styles.removeButton}
                title="Remove time slot"
              >
                &times;
              </button>
            </div>
          ))}
          <div className={styles.dayActions}>
            <button onClick={() => handleAddTimeSlot(dayIndex)} className={styles.addButton}>
              + Add Time Slot
            </button>
            {(schedule[dayIndex] || []).length > 0 && (
                 <button onClick={() => handleSetDayUnavailable(dayIndex)} className={styles.unavailableButton}>
                    Mark as Unavailable
                </button>
            )}
          </div>
        </div>
      ))}
      <div className={styles.formActions}>
        <button onClick={handleSubmit} className={styles.saveButton} disabled={isSubmitting}>
          {isSubmitting ? 'Saving Schedule...' : 'Save Weekly Schedule'}
        </button>
      </div>
    </div>
  );
}
