# User Profiles View

This document explains the `user_profiles` view that provides a convenient way to access user information from the `auth.users` table in Supabase.

## Overview

The `user_profiles` view is a SQL view that exposes selected fields from the `auth.users` table and joins it with the `user_roles` table to provide role information. This view makes it easier to join with user data in queries.

## Schema

The view has the following schema:

```sql
CREATE VIEW public.user_profiles AS
SELECT 
  au.id,
  au.email,
  au.raw_user_meta_data->>'name' as display_name,
  au.created_at,
  au.last_sign_in_at,
  ur.role
FROM 
  auth.users au
LEFT JOIN 
  public.user_roles ur ON au.id = ur.id;
```

## Fields

- `id`: The user's UUID from auth.users
- `email`: The user's email address
- `display_name`: The user's name from the metadata
- `created_at`: When the user account was created
- `last_sign_in_at`: When the user last signed in
- `role`: The user's role from the user_roles table (admin, staff, or null)

## Usage

### Joining with User Data

When you need to join a table that has a foreign key to `auth.users`, use the `user_profiles` view with the foreign key constraint name:

```javascript
// Example: Joining booking_status_history with user_profiles
const { data, error } = await supabase
  .from('booking_status_history')
  .select(`
    id,
    previous_status,
    new_status,
    notes,
    created_at,
    changed_by,
    user_profiles!booking_status_history_changed_by_fkey (email, display_name, role)
  `)
  .eq('booking_id', bookingId);
```

The key part is `user_profiles!booking_status_history_changed_by_fkey` which tells Supabase to use the foreign key constraint to join with the `user_profiles` view.

### Direct Queries

You can also query the view directly:

```javascript
const { data, error } = await supabase
  .from('user_profiles')
  .select('id, email, display_name, role')
  .eq('role', 'admin');
```

## Security

The view has Row Level Security (RLS) policies that ensure:

1. Users can only view their own profile
2. Admins can view all profiles

```sql
CREATE POLICY "Users can view their own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

## Implementation

The view is created in the `supabase/migrations/create_user_profiles_view.sql` file and can be applied using the `scripts/apply-user-profiles-migration.js` script:

```bash
node scripts/apply-user-profiles-migration.js
```

## Troubleshooting

If you encounter issues with the view:

1. Verify the view exists in your Supabase database
2. Check that the RLS policies are correctly applied
3. Ensure your queries use the correct foreign key constraint name

For foreign key constraint names, you can find them in the Supabase dashboard under Database > Tables > [your table] > Foreign Keys.
