import { useState, useEffect } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * AvailabilityDashboard component for displaying booking availability and quick actions
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onQuickBooking - Callback for quick booking action
 * @param {Function} props.onViewCalendar - Callback for view calendar action
 * @returns {JSX.Element}
 */
export default function AvailabilityDashboard({ onQuickBooking, onViewCalendar }) {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [timeSlots, setTimeSlots] = useState([])
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null)
  const [loading, setLoading] = useState(false)

  // Generate time slots for the selected date
  const generateTimeSlots = (date) => {
    const slots = []
    const startHour = 9 // 9 AM
    const endHour = 17 // 5 PM
    const slotDuration = 30 // 30 minutes

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += slotDuration) {
        const slotTime = new Date(date)
        slotTime.setHours(hour, minute, 0, 0)
        
        // Mock availability status  
        // Temporarily making most slots available until we sync up artist availability
        const hash = (hour * 60 + minute) % 10
        const status = hash < 8 ? 'available' : hash < 9 ? 'busy' : 'unavailable'
        
        slots.push({
          time: slotTime,
          status,
          id: `${hour}-${minute}`
        })
      }
    }
    
    return slots
  }

  // Update time slots when date changes
  useEffect(() => {
    setLoading(true)
    const slots = generateTimeSlots(selectedDate)
    setTimeSlots(slots)
    setSelectedTimeSlot(null)
    setLoading(false)
  }, [selectedDate])

  const handleDateChange = (event) => {
    setSelectedDate(event.target.value)
  }

  const handleTimeSlotClick = (slot) => {
    if (slot.status === 'available') {
      setSelectedTimeSlot(slot)
    }
  }

  const handleRefresh = () => {
    setLoading(true)
    // Simulate refresh delay
    setTimeout(() => {
      const slots = generateTimeSlots(selectedDate)
      setTimeSlots(slots)
      setLoading(false)
    }, 500)
  }

  // Quick action handlers
  const handleQuickBooking = () => {
    if (onQuickBooking) {
      onQuickBooking()
    } else {
      // Default behavior - navigate to booking creation
      window.location.href = '/admin/bookings?action=create'
    }
  }

  const handleViewCalendar = () => {
    if (onViewCalendar) {
      onViewCalendar()
    } else {
      // Default behavior - open calendar in new tab
      window.open('/admin/bookings', '_blank')
    }
  }

  const handleCustomerSearch = () => {
    // Navigate to customer search
    window.open('/admin/customers', '_blank')
  }

  const handleTodaysBookings = () => {
    // Navigate to today's bookings with date filter
    const today = new Date().toISOString().split('T')[0]
    window.open(`/admin/bookings?date=${today}`, '_blank')
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-AU', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false
    })
  }

  return (
    <div className={styles.availabilityDashboard}>
      <div className={styles.dashboardHeader}>
        <h3 className={styles.dashboardTitle}>Booking Availability</h3>
        <button 
          className={styles.refreshButton}
          onClick={handleRefresh}
          disabled={loading}
        >
          {loading ? '⏳' : '🔄'} Refresh
        </button>
      </div>

      <div className={styles.availabilityGrid}>
        <div className={styles.timeSlotView}>
          <div className={styles.timeSlotHeader}>
            <h4 className={styles.timeSlotTitle}>Available Time Slots</h4>
            <div className={styles.dateSelector}>
              <label htmlFor="date-select">Date:</label>
              <input
                id="date-select"
                type="date"
                value={selectedDate}
                onChange={handleDateChange}
                className={styles.dateInput}
                // TODO: Temporarily removed date restriction until we sync up artist availability
                // min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {loading ? (
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading time slots...</p>
            </div>
          ) : (
            <div className={styles.timeSlots}>
              {timeSlots.map((slot) => (
                <div
                  key={slot.id}
                  className={`${styles.timeSlot} ${styles[slot.status]} ${
                    selectedTimeSlot?.id === slot.id ? styles.selected : ''
                  }`}
                  onClick={() => handleTimeSlotClick(slot)}
                  title={`${formatTime(slot.time)} - ${slot.status}`}
                >
                  {formatTime(slot.time)}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className={styles.quickActions}>
          <h4 className={styles.quickActionsTitle}>Quick Actions</h4>
          
          <button
            className={styles.actionButton}
            onClick={handleQuickBooking}
            disabled={!selectedTimeSlot}
          >
            <span className={styles.actionIcon}>⚡</span>
            <div className={styles.actionContent}>
              <div className={styles.actionTitle}>Quick Booking</div>
              <div className={styles.actionDescription}>
                {selectedTimeSlot
                  ? `Book for ${formatTime(selectedTimeSlot.time)}`
                  : 'Select a time slot first'
                }
              </div>
            </div>
          </button>

          <button
            className={styles.actionButton}
            onClick={handleViewCalendar}
          >
            <span className={styles.actionIcon}>📅</span>
            <div className={styles.actionContent}>
              <div className={styles.actionTitle}>View Calendar</div>
              <div className={styles.actionDescription}>
                Open full booking calendar
              </div>
            </div>
          </button>

          <button
            className={styles.actionButton}
            onClick={handleCustomerSearch}
          >
            <span className={styles.actionIcon}>👥</span>
            <div className={styles.actionContent}>
              <div className={styles.actionTitle}>Customer Search</div>
              <div className={styles.actionDescription}>
                Find existing customers
              </div>
            </div>
          </button>

          <button
            className={styles.actionButton}
            onClick={handleTodaysBookings}
          >
            <span className={styles.actionIcon}>📋</span>
            <div className={styles.actionContent}>
              <div className={styles.actionTitle}>Today's Bookings</div>
              <div className={styles.actionDescription}>
                View all bookings for today
              </div>
            </div>
          </button>
        </div>
      </div>

      {selectedTimeSlot && (
        <div className={styles.selectedSlotInfo}>
          <h4>Selected Time Slot</h4>
          <p>
            <strong>Date:</strong> {new Date(selectedDate).toLocaleDateString('en-AU')}
          </p>
          <p>
            <strong>Time:</strong> {formatTime(selectedTimeSlot.time)}
          </p>
          <p>
            <strong>Status:</strong> {selectedTimeSlot.status}
          </p>
        </div>
      )}
    </div>
  )
}
