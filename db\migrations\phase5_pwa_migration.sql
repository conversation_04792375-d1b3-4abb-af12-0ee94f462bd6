-- =============================================
-- PHASE 5: PWA ENHANCEMENT DATABASE MIGRATION
-- Ocean Soul Sparkles - Progressive Web App Support
-- =============================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- PHOTOS TABLE FOR PWA CAMERA INTEGRATION
-- =============================================

-- Create photos table for storing captured photos
CREATE TABLE IF NOT EXISTS public.photos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  filename TEXT NOT NULL,
  original_filename TEXT,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT,
  type TEXT NOT NULL CHECK (type IN ('before', 'after', 'portfolio', 'receipt')),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
  public_url TEXT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  metadata JSONB DEFAULT '{}',
  
  -- Constraints
  CONSTRAINT photos_type_check CHECK (type IN ('before', 'after', 'portfolio', 'receipt')),
  CONSTRAINT photos_file_size_check CHECK (file_size > 0)
);

-- Create indexes for photos table
CREATE INDEX IF NOT EXISTS idx_photos_type ON public.photos(type);
CREATE INDEX IF NOT EXISTS idx_photos_booking_id ON public.photos(booking_id);
CREATE INDEX IF NOT EXISTS idx_photos_uploaded_at ON public.photos(uploaded_at);
CREATE INDEX IF NOT EXISTS idx_photos_created_at ON public.photos(created_at);
CREATE INDEX IF NOT EXISTS idx_photos_file_path ON public.photos(file_path);

-- =============================================
-- ENHANCE BOOKINGS TABLE FOR PHOTO SUPPORT
-- =============================================

-- Add photo URL columns to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS before_photo_url TEXT,
ADD COLUMN IF NOT EXISTS after_photo_url TEXT,
ADD COLUMN IF NOT EXISTS photo_count INTEGER DEFAULT 0;

-- Add PWA-specific fields to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS offline_created BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS sync_status TEXT DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed')),
ADD COLUMN IF NOT EXISTS offline_data JSONB DEFAULT '{}';

-- =============================================
-- PWA SYNC QUEUE TABLE (OPTIONAL)
-- =============================================

-- Create PWA sync queue table for advanced offline sync tracking
CREATE TABLE IF NOT EXISTS public.pwa_sync_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  item_type TEXT NOT NULL,
  item_id TEXT NOT NULL,
  data JSONB NOT NULL,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  processed_at TIMESTAMPTZ,
  
  -- Constraints
  UNIQUE(item_type, item_id)
);

-- Create indexes for sync queue
CREATE INDEX IF NOT EXISTS idx_pwa_sync_status ON public.pwa_sync_queue(status);
CREATE INDEX IF NOT EXISTS idx_pwa_sync_priority ON public.pwa_sync_queue(priority);
CREATE INDEX IF NOT EXISTS idx_pwa_sync_created_at ON public.pwa_sync_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_pwa_sync_item_type ON public.pwa_sync_queue(item_type);

-- =============================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================

-- Enable RLS on photos table
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;

-- Photos policies
CREATE POLICY "Photos are viewable by authenticated users" ON public.photos
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Photos can be inserted by authenticated users" ON public.photos
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Photos can be updated by authenticated users" ON public.photos
  FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Photos can be deleted by authenticated users" ON public.photos
  FOR DELETE USING (auth.role() = 'authenticated');

-- Enable RLS on PWA sync queue
ALTER TABLE public.pwa_sync_queue ENABLE ROW LEVEL SECURITY;

-- PWA sync queue policies
CREATE POLICY "Sync queue accessible by authenticated users" ON public.pwa_sync_queue
  FOR ALL USING (auth.role() = 'authenticated');

-- =============================================
-- DATABASE FUNCTIONS
-- =============================================

-- Function to update booking photo count
CREATE OR REPLACE FUNCTION update_booking_photo_count()
RETURNS TRIGGER AS $$
BEGIN
  -- Update photo count for the booking
  IF TG_OP = 'INSERT' THEN
    UPDATE public.bookings 
    SET photo_count = (
      SELECT COUNT(*) 
      FROM public.photos 
      WHERE booking_id = NEW.booking_id
    )
    WHERE id = NEW.booking_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    UPDATE public.bookings 
    SET photo_count = (
      SELECT COUNT(*) 
      FROM public.photos 
      WHERE booking_id = OLD.booking_id
    )
    WHERE id = OLD.booking_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update booking photo URLs
CREATE OR REPLACE FUNCTION update_booking_photo_urls()
RETURNS TRIGGER AS $$
BEGIN
  -- Update booking with photo URL when before/after photos are added
  IF NEW.type = 'before' AND NEW.booking_id IS NOT NULL THEN
    UPDATE public.bookings 
    SET before_photo_url = NEW.public_url 
    WHERE id = NEW.booking_id;
  ELSIF NEW.type = 'after' AND NEW.booking_id IS NOT NULL THEN
    UPDATE public.bookings 
    SET after_photo_url = NEW.public_url 
    WHERE id = NEW.booking_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old photos
CREATE OR REPLACE FUNCTION cleanup_old_photos(days_old INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete photos older than specified days that are not linked to bookings
  DELETE FROM public.photos 
  WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND booking_id IS NULL
    AND type IN ('portfolio', 'receipt');
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to process PWA sync queue
CREATE OR REPLACE FUNCTION process_pwa_sync_queue()
RETURNS INTEGER AS $$
DECLARE
  processed_count INTEGER := 0;
  queue_item RECORD;
BEGIN
  -- Process pending items in priority order
  FOR queue_item IN 
    SELECT * FROM public.pwa_sync_queue 
    WHERE status = 'pending' 
    ORDER BY 
      CASE priority 
        WHEN 'high' THEN 1 
        WHEN 'medium' THEN 2 
        WHEN 'low' THEN 3 
      END,
      created_at ASC
    LIMIT 100
  LOOP
    -- Mark as processing
    UPDATE public.pwa_sync_queue 
    SET status = 'processing', updated_at = NOW() 
    WHERE id = queue_item.id;
    
    -- Here you would implement the actual sync logic
    -- For now, just mark as completed
    UPDATE public.pwa_sync_queue 
    SET status = 'completed', processed_at = NOW(), updated_at = NOW() 
    WHERE id = queue_item.id;
    
    processed_count := processed_count + 1;
  END LOOP;
  
  RETURN processed_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- DATABASE TRIGGERS
-- =============================================

-- Trigger to update booking photo URLs
CREATE TRIGGER update_booking_photos_trigger
  AFTER INSERT ON public.photos
  FOR EACH ROW
  EXECUTE FUNCTION update_booking_photo_urls();

-- Trigger to update booking photo count
CREATE TRIGGER update_booking_photo_count_trigger
  AFTER INSERT OR DELETE ON public.photos
  FOR EACH ROW
  EXECUTE FUNCTION update_booking_photo_count();

-- Trigger to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_photos_updated_at
  BEFORE UPDATE ON public.photos
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pwa_sync_queue_updated_at
  BEFORE UPDATE ON public.pwa_sync_queue
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- VIEWS FOR ANALYTICS
-- =============================================

-- Create view for photo analytics
CREATE OR REPLACE VIEW photo_analytics AS
SELECT 
  type,
  COUNT(*) as total_photos,
  SUM(file_size) as total_size_bytes,
  AVG(file_size) as avg_size_bytes,
  MIN(created_at) as first_photo,
  MAX(created_at) as latest_photo,
  COUNT(DISTINCT booking_id) as unique_bookings
FROM public.photos
GROUP BY type;

-- Create view for PWA sync analytics
CREATE OR REPLACE VIEW pwa_sync_analytics AS
SELECT 
  item_type,
  status,
  COUNT(*) as count,
  AVG(retry_count) as avg_retries,
  MIN(created_at) as oldest_item,
  MAX(created_at) as newest_item
FROM public.pwa_sync_queue
GROUP BY item_type, status;

-- Grant access to views
GRANT SELECT ON photo_analytics TO authenticated;
GRANT SELECT ON pwa_sync_analytics TO authenticated;

-- =============================================
-- STORAGE BUCKET SETUP (Run in Supabase Dashboard)
-- =============================================

-- Note: This should be run in the Supabase Dashboard Storage section
-- or via the Supabase API, not in SQL editor

/*
-- Create storage bucket for photos
INSERT INTO storage.buckets (id, name, public)
VALUES ('ocean-soul-sparkles', 'ocean-soul-sparkles', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies (run in Supabase Dashboard)
CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow public access" ON storage.objects
  FOR SELECT USING (bucket_id = 'ocean-soul-sparkles');

CREATE POLICY "Allow authenticated updates" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );

CREATE POLICY "Allow authenticated deletes" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'ocean-soul-sparkles' AND
    auth.role() = 'authenticated'
  );
*/

-- =============================================
-- SAMPLE DATA AND TESTING
-- =============================================

-- Insert sample photo types for testing
-- (Uncomment for development/testing)
/*
INSERT INTO public.photos (filename, file_path, public_url, type, file_size, mime_type) 
VALUES 
  ('sample_before.jpg', 'photos/before/sample_before.jpg', 'https://example.com/sample_before.jpg', 'before', 1024000, 'image/jpeg'),
  ('sample_after.jpg', 'photos/after/sample_after.jpg', 'https://example.com/sample_after.jpg', 'after', 1024000, 'image/jpeg'),
  ('sample_portfolio.jpg', 'photos/portfolio/sample_portfolio.jpg', 'https://example.com/sample_portfolio.jpg', 'portfolio', 1024000, 'image/jpeg')
ON CONFLICT DO NOTHING;
*/

-- =============================================
-- MIGRATION COMPLETION
-- =============================================

-- Log migration completion
DO $$
BEGIN
  RAISE NOTICE 'Phase 5 PWA migration completed successfully!';
  RAISE NOTICE 'Created tables: photos, pwa_sync_queue';
  RAISE NOTICE 'Enhanced bookings table with photo support';
  RAISE NOTICE 'Created functions, triggers, and views';
  RAISE NOTICE 'Enabled Row Level Security policies';
  RAISE NOTICE 'Next steps: Create storage bucket in Supabase Dashboard';
END $$;
