/**
 * Production Configuration Verification Script
 * Verifies that all required environment variables and configurations are set correctly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.production' });

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  console.log('🔍 Checking environment variables...');
  
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const missing = [];
  const configured = [];
  
  required.forEach(key => {
    if (process.env[key]) {
      configured.push(key);
      console.log(`   ✅ ${key}: configured`);
    } else {
      missing.push(key);
      console.log(`   ❌ ${key}: missing`);
    }
  });
  
  return { missing, configured };
}

/**
 * Test Supabase admin client
 */
async function testSupabaseAdminClient() {
  console.log('\n🔧 Testing Supabase admin client...');
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.log('   ❌ Missing required environment variables');
      return false;
    }
    
    const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    console.log('   ✅ Admin client created successfully');
    
    // Test a simple query
    const { data, error } = await adminClient
      .from('services')
      .select('id')
      .limit(1);
    
    if (error) {
      console.log(`   ❌ Admin client query failed: ${error.message}`);
      return false;
    }
    
    console.log('   ✅ Admin client query successful');
    return true;
  } catch (error) {
    console.log(`   ❌ Admin client error: ${error.message}`);
    return false;
  }
}

/**
 * Test RLS policies
 */
async function testRLSPolicies() {
  console.log('\n🛡️  Testing RLS policies...');
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test services table
    const { data: services, error: servicesError } = await adminClient
      .from('services')
      .select('id, name, status')
      .limit(5);
    
    if (servicesError) {
      console.log(`   ❌ Services RLS test failed: ${servicesError.message}`);
      return false;
    }
    
    console.log(`   ✅ Services RLS test passed (${services.length} records)`);
    
    // Test products table
    const { data: products, error: productsError } = await adminClient
      .from('products')
      .select('id, name, status')
      .limit(5);
    
    if (productsError) {
      console.log(`   ❌ Products RLS test failed: ${productsError.message}`);
      return false;
    }
    
    console.log(`   ✅ Products RLS test passed (${products.length} records)`);
    
    return true;
  } catch (error) {
    console.log(`   ❌ RLS test error: ${error.message}`);
    return false;
  }
}

/**
 * Test export query performance
 */
async function testExportQueryPerformance() {
  console.log('\n⚡ Testing export query performance...');
  
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    const adminClient = createClient(supabaseUrl, supabaseServiceKey);
    
    // Test services export query
    const servicesStart = Date.now();
    const { data: services, error: servicesError } = await adminClient
      .from('services')
      .select(`
        id, name, description, duration, price, color, category,
        category_id, image_url, status, featured, visible_on_public,
        visible_on_pos, visible_on_events, meta_title, meta_description,
        booking_requirements, availability_notes, created_at, updated_at
      `)
      .order('name');
    
    const servicesDuration = Date.now() - servicesStart;
    
    if (servicesError) {
      console.log(`   ❌ Services export query failed: ${servicesError.message}`);
      return false;
    }
    
    console.log(`   ✅ Services export query: ${servicesDuration}ms (${services.length} records)`);
    
    // Test products export query
    const productsStart = Date.now();
    const { data: products, error: productsError } = await adminClient
      .from('products')
      .select(`
        id, name, description, short_description, sku, price, sale_price,
        cost_price, category, stock, low_stock_threshold, image_url,
        gallery_images, status, featured, is_active, meta_title,
        meta_description, created_at, updated_at
      `)
      .order('name');
    
    const productsDuration = Date.now() - productsStart;
    
    if (productsError) {
      console.log(`   ❌ Products export query failed: ${productsError.message}`);
      return false;
    }
    
    console.log(`   ✅ Products export query: ${productsDuration}ms (${products.length} records)`);
    
    // Performance warnings
    if (servicesDuration > 5000) {
      console.log(`   ⚠️  Services query is slow (${servicesDuration}ms)`);
    }
    
    if (productsDuration > 5000) {
      console.log(`   ⚠️  Products query is slow (${productsDuration}ms)`);
    }
    
    return true;
  } catch (error) {
    console.log(`   ❌ Performance test error: ${error.message}`);
    return false;
  }
}

/**
 * Main verification function
 */
async function runVerification() {
  console.log('🔍 Production Configuration Verification');
  console.log('==========================================\n');
  
  const results = [];
  
  // Check environment variables
  const envCheck = checkEnvironmentVariables();
  results.push({
    test: 'Environment Variables',
    passed: envCheck.missing.length === 0,
    details: envCheck
  });
  
  if (envCheck.missing.length > 0) {
    console.log('\n❌ Missing required environment variables. Cannot continue.');
    process.exit(1);
  }
  
  // Test Supabase admin client
  const adminClientTest = await testSupabaseAdminClient();
  results.push({
    test: 'Supabase Admin Client',
    passed: adminClientTest
  });
  
  // Test RLS policies
  const rlsTest = await testRLSPolicies();
  results.push({
    test: 'RLS Policies',
    passed: rlsTest
  });
  
  // Test export query performance
  const performanceTest = await testExportQueryPerformance();
  results.push({
    test: 'Export Query Performance',
    passed: performanceTest
  });
  
  // Summary
  console.log('\n📊 Verification Summary:');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.test}`);
  });
  
  console.log(`\nTotal: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 All verification tests passed! Production configuration is ready.');
    process.exit(0);
  } else {
    console.log('\n❌ Some verification tests failed. Please fix the issues before deploying.');
    process.exit(1);
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  runVerification().catch(error => {
    console.error('💥 Verification error:', error);
    process.exit(1);
  });
}

module.exports = { runVerification };
