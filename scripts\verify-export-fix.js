/**
 * Verification Script for Export Fix
 * Verifies that the database schema mismatch has been resolved
 */

import https from 'https';
import http from 'http';

// Configuration
const PRODUCTION_URL = 'https://www.oceansoulsparkles.com.au';
const LOCAL_URL = 'http://localhost:3000';

/**
 * Make HTTP request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const requestOptions = {
      ...options,
      headers: {
        'Authorization': 'Bearer test-token',
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: res.headers['content-type']?.includes('application/json') ? JSON.parse(data) : data
          };
          resolve(result);
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Test export endpoint for schema errors
 */
async function testExportEndpoint(baseUrl, endpoint, format) {
  const url = `${baseUrl}/api/admin/inventory/${endpoint}/export?format=${format}`;
  
  try {
    const response = await makeRequest(url);
    
    // Check for the specific schema error we were fixing
    if (response.status === 500 && response.data?.message?.includes('does not exist')) {
      return {
        success: false,
        error: 'SCHEMA_ERROR',
        message: response.data.message,
        endpoint,
        format
      };
    }
    
    // Check for authentication error (expected in production)
    if (response.status === 401) {
      return {
        success: true,
        error: 'AUTH_ERROR',
        message: 'Authentication required (expected)',
        endpoint,
        format,
        requestId: response.data?.requestId
      };
    }
    
    // Check for successful response (in development)
    if (response.status === 200) {
      return {
        success: true,
        error: null,
        message: 'Export successful',
        endpoint,
        format,
        dataSize: typeof response.data === 'string' ? response.data.length : JSON.stringify(response.data).length
      };
    }
    
    // Other errors
    return {
      success: false,
      error: 'OTHER_ERROR',
      message: response.data?.message || `HTTP ${response.status}`,
      endpoint,
      format,
      status: response.status
    };
    
  } catch (error) {
    return {
      success: false,
      error: 'REQUEST_ERROR',
      message: error.message,
      endpoint,
      format
    };
  }
}

/**
 * Run verification tests
 */
async function runVerification() {
  console.log('🔍 Export Fix Verification');
  console.log('==========================\n');
  
  const endpoints = ['services', 'products'];
  const formats = ['csv', 'json'];
  const environments = [
    { name: 'Production', url: PRODUCTION_URL },
    { name: 'Local', url: LOCAL_URL }
  ];
  
  const results = [];
  
  for (const env of environments) {
    console.log(`📍 Testing ${env.name} Environment (${env.url})`);
    console.log('─'.repeat(50));
    
    for (const endpoint of endpoints) {
      for (const format of formats) {
        const result = await testExportEndpoint(env.url, endpoint, format);
        results.push({ ...result, environment: env.name });
        
        const status = result.success ? '✅' : '❌';
        const errorInfo = result.error ? ` (${result.error})` : '';
        const requestId = result.requestId ? ` [${result.requestId}]` : '';
        
        console.log(`${status} ${endpoint}/${format}: ${result.message}${errorInfo}${requestId}`);
      }
    }
    console.log('');
  }
  
  // Summary
  console.log('📊 Verification Summary');
  console.log('=======================');
  
  const schemaErrors = results.filter(r => r.error === 'SCHEMA_ERROR');
  const authErrors = results.filter(r => r.error === 'AUTH_ERROR');
  const successes = results.filter(r => r.success && !r.error);
  const otherErrors = results.filter(r => !r.success && r.error !== 'SCHEMA_ERROR');
  
  console.log(`Schema Errors (the bug we fixed): ${schemaErrors.length}`);
  console.log(`Authentication Errors (expected): ${authErrors.length}`);
  console.log(`Successful Exports: ${successes.length}`);
  console.log(`Other Errors: ${otherErrors.length}`);
  
  if (schemaErrors.length > 0) {
    console.log('\n❌ SCHEMA ERRORS STILL PRESENT:');
    schemaErrors.forEach(error => {
      console.log(`   - ${error.environment}/${error.endpoint}/${error.format}: ${error.message}`);
    });
    console.log('\n🚨 The fix did not resolve the database schema issues!');
    process.exit(1);
  } else {
    console.log('\n✅ NO SCHEMA ERRORS DETECTED');
    console.log('🎉 The database schema mismatch has been successfully resolved!');
    
    if (authErrors.length > 0) {
      console.log('\n📝 Note: Authentication errors are expected in production without valid credentials.');
    }
    
    if (successes.length > 0) {
      console.log('\n📝 Note: Successful exports indicate the endpoints are working correctly.');
    }
    
    process.exit(0);
  }
}

// Run verification if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runVerification().catch(error => {
    console.error('💥 Verification error:', error);
    process.exit(1);
  });
}

export { runVerification, testExportEndpoint };
