-- OceanSoulSparkles Admin Panel Database Schema

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User roles table (Enhanced with 5 role system)
CREATE TABLE public.user_roles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('dev', 'admin', 'artist', 'braider', 'user')) DEFAULT 'user',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Extended user profiles table
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL PRIMARY KEY,
  name TEXT,
  phone TEXT,
  subscription_status TEXT CHECK (subscription_status IN ('successful', 'failed', 'pending')) DEFAULT 'pending',
  last_login_at TIMESTAMPTZ,
  login_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User activity tracking
CREATE TABLE public.user_activity_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_description TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Artist/Braider application system
CREATE TABLE public.artist_braider_applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  application_type TEXT CHECK (application_type IN ('artist', 'braider')) NOT NULL,
  experience_years INTEGER,
  portfolio_url TEXT,
  availability_preferences JSONB,
  service_specializations TEXT[],
  previous_experience TEXT,
  references TEXT,
  status TEXT CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')) DEFAULT 'pending',
  reviewed_by UUID REFERENCES auth.users,
  reviewed_at TIMESTAMPTZ,
  review_notes TEXT,
  welcome_email_sent BOOLEAN DEFAULT FALSE,
  welcome_email_sent_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User service assignments (for artists/braiders)
CREATE TABLE public.user_service_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users ON DELETE CASCADE,
  service_id UUID REFERENCES public.services ON DELETE CASCADE,
  is_primary_provider BOOLEAN DEFAULT FALSE,
  skill_level TEXT CHECK (skill_level IN ('beginner', 'intermediate', 'advanced', 'expert')) DEFAULT 'intermediate',
  hourly_rate DECIMAL(10, 2),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, service_id)
);

-- Enable RLS on user_roles
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Enable RLS on new tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_braider_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_service_assignments ENABLE ROW LEVEL SECURITY;

-- Create function to get user role (Enhanced for new role system)
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
  role_name TEXT;
BEGIN
  SELECT role INTO role_name FROM public.user_roles WHERE id = $1;
  RETURN COALESCE(role_name, 'user');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has admin privileges
CREATE OR REPLACE FUNCTION public.is_admin_or_dev(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) IN ('dev', 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user has staff privileges (artist/braider/admin/dev)
CREATE OR REPLACE FUNCTION public.is_staff_or_above(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) IN ('dev', 'admin', 'artist', 'braider');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policies for user_roles
CREATE POLICY "Users can view their own role" ON public.user_roles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins and devs can view all roles" ON public.user_roles
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can update roles" ON public.user_roles
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can insert roles" ON public.user_roles
  FOR INSERT WITH CHECK (is_admin_or_dev(auth.uid()));

-- Create policies for user_profiles
CREATE POLICY "Users can view their own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins and devs can view all profiles" ON public.user_profiles
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Users can update their own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins and devs can update all profiles" ON public.user_profiles
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Admins and devs can insert profiles" ON public.user_profiles
  FOR INSERT WITH CHECK (is_admin_or_dev(auth.uid()));

-- Create policies for user_activity_log
CREATE POLICY "Users can view their own activity" ON public.user_activity_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and devs can view all activity" ON public.user_activity_log
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "System can insert activity logs" ON public.user_activity_log
  FOR INSERT WITH CHECK (true);

-- Create policies for artist_braider_applications
CREATE POLICY "Users can view their own applications" ON public.artist_braider_applications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and devs can view all applications" ON public.artist_braider_applications
  FOR SELECT USING (is_admin_or_dev(auth.uid()));

CREATE POLICY "Users can create their own applications" ON public.artist_braider_applications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins and devs can update applications" ON public.artist_braider_applications
  FOR UPDATE USING (is_admin_or_dev(auth.uid()));

-- Create policies for user_service_assignments
CREATE POLICY "Users can view their own service assignments" ON public.user_service_assignments
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Staff can view all service assignments" ON public.user_service_assignments
  FOR SELECT USING (is_staff_or_above(auth.uid()));

CREATE POLICY "Admins and devs can manage service assignments" ON public.user_service_assignments
  FOR ALL USING (is_admin_or_dev(auth.uid()));

-- Customers table
CREATE TABLE public.customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT,
  address TEXT,
  city TEXT,
  state TEXT,
  postal_code TEXT,
  country TEXT DEFAULT 'Australia',
  notes TEXT,
  marketing_consent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer preferences table
CREATE TABLE public.customer_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  preference_key TEXT NOT NULL,
  preference_value TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id, preference_key)
);

-- Services table
CREATE TABLE public.services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL, -- in minutes
  price DECIMAL(10, 2) NOT NULL,
  color TEXT NOT NULL, -- for calendar display
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bookings table
CREATE TABLE public.bookings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id),
  service_id UUID REFERENCES public.services(id),
  start_time TIMESTAMPTZ NOT NULL,
  end_time TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('confirmed', 'pending', 'canceled')),
  location TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Product categories table
CREATE TABLE public.product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES public.product_categories(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Products table
CREATE TABLE public.products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  sku TEXT UNIQUE,
  price DECIMAL(10, 2) NOT NULL,
  sale_price DECIMAL(10, 2),
  cost_price DECIMAL(10, 2),
  category_id UUID REFERENCES public.product_categories(id),
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory table
CREATE TABLE public.inventory (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL DEFAULT 0,
  low_stock_threshold INTEGER DEFAULT 5,
  last_restock_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Inventory transactions table
CREATE TABLE public.inventory_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  transaction_type TEXT NOT NULL,
  reference_id UUID,
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Suppliers table
CREATE TABLE public.suppliers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  contact_name TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Purchase orders table
CREATE TABLE public.purchase_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  supplier_id UUID REFERENCES public.suppliers(id),
  order_date TIMESTAMPTZ NOT NULL,
  expected_delivery_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  total_amount DECIMAL(10, 2),
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Purchase order items table
CREATE TABLE public.purchase_order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  purchase_order_id UUID REFERENCES public.purchase_orders(id) NOT NULL,
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  received_quantity INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Orders table
CREATE TABLE public.orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id),
  order_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL,
  payment_status TEXT NOT NULL,
  shipping_address TEXT,
  shipping_city TEXT,
  shipping_state TEXT,
  shipping_postal_code TEXT,
  shipping_country TEXT DEFAULT 'Australia',
  shipping_method TEXT,
  shipping_cost DECIMAL(10, 2) DEFAULT 0,
  subtotal DECIMAL(10, 2) NOT NULL,
  tax DECIMAL(10, 2) DEFAULT 0,
  discount DECIMAL(10, 2) DEFAULT 0,
  total DECIMAL(10, 2) NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Order items table
CREATE TABLE public.order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES public.orders(id) NOT NULL,
  product_id UUID REFERENCES public.products(id) NOT NULL,
  quantity INTEGER NOT NULL,
  price DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payments table
CREATE TABLE public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES public.orders(id),
  booking_id UUID REFERENCES public.bookings(id),
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'AUD',
  payment_method TEXT NOT NULL,
  payment_status TEXT NOT NULL,
  transaction_id TEXT,
  payment_date TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK (
    (order_id IS NOT NULL AND booking_id IS NULL) OR
    (order_id IS NULL AND booking_id IS NOT NULL)
  )
);

-- Refunds table
CREATE TABLE public.refunds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id UUID REFERENCES public.payments(id) NOT NULL,
  refund_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'AUD',
  refund_reason TEXT NOT NULL,
  refund_notes TEXT,
  refund_method TEXT NOT NULL, -- 'cash', 'square_api', 'manual'
  refund_status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'cancelled'
  square_refund_id TEXT, -- For Square API refunds
  processed_by UUID REFERENCES auth.users(id) NOT NULL,
  processed_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK (refund_amount > 0),
  CHECK (refund_reason IN ('customer_request', 'service_issue', 'billing_error', 'other')),
  CHECK (refund_method IN ('cash', 'square_api', 'manual')),
  CHECK (refund_status IN ('pending', 'completed', 'failed', 'cancelled'))
);

-- Invoices table
CREATE TABLE public.invoices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  invoice_number TEXT NOT NULL UNIQUE,
  customer_id UUID REFERENCES public.customers(id) NOT NULL,
  order_id UUID REFERENCES public.orders(id),
  booking_id UUID REFERENCES public.bookings(id),
  amount DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2) DEFAULT 0,
  currency TEXT NOT NULL DEFAULT 'AUD',
  issue_date TIMESTAMPTZ NOT NULL,
  due_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CHECK (
    (order_id IS NOT NULL AND booking_id IS NULL) OR
    (order_id IS NULL AND booking_id IS NOT NULL)
  )
);

-- Customer segments table
CREATE TABLE public.customer_segments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  segment_query JSONB NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Marketing campaigns table
CREATE TABLE public.marketing_campaigns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  campaign_type TEXT NOT NULL,
  target_segment UUID REFERENCES public.customer_segments(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Campaign messages table
CREATE TABLE public.campaign_messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id) ON DELETE CASCADE,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT NOT NULL,
  scheduled_date TIMESTAMPTZ,
  sent_date TIMESTAMPTZ,
  status TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Campaign metrics table
CREATE TABLE public.campaign_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id) ON DELETE CASCADE,
  metric_type TEXT NOT NULL,
  metric_value INTEGER NOT NULL,
  recorded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Discount codes table
CREATE TABLE public.discount_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_id UUID REFERENCES public.marketing_campaigns(id),
  code TEXT NOT NULL UNIQUE,
  discount_type TEXT NOT NULL,
  discount_value DECIMAL(10, 2) NOT NULL,
  min_purchase_amount DECIMAL(10, 2),
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_type TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  related_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.discount_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create enhanced RLS policies for all tables using new role system
-- This grants staff and above access to all tables (dev, admin, artist, braider)
CREATE POLICY "Staff and above access" ON public.customers FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.customer_preferences FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.services FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.bookings FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.product_categories FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.products FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.inventory FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.inventory_transactions FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.suppliers FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.purchase_orders FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.purchase_order_items FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.orders FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.order_items FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.payments FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Staff and above access" ON public.invoices FOR ALL USING (is_staff_or_above(auth.uid()));
CREATE POLICY "Admin and dev access only" ON public.customer_segments FOR ALL USING (is_admin_or_dev(auth.uid()));
CREATE POLICY "Admin and dev access only" ON public.marketing_campaigns FOR ALL USING (is_admin_or_dev(auth.uid()));
CREATE POLICY "Admin and dev access only" ON public.campaign_messages FOR ALL USING (is_admin_or_dev(auth.uid()));
CREATE POLICY "Admin and dev access only" ON public.campaign_metrics FOR ALL USING (is_admin_or_dev(auth.uid()));
CREATE POLICY "Admin and dev access only" ON public.discount_codes FOR ALL USING (is_admin_or_dev(auth.uid()));
CREATE POLICY "Users can view their own notifications" ON public.notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Staff and above access to all notifications" ON public.notifications FOR ALL USING (is_staff_or_above(auth.uid()));
