import { useState, useEffect, useCallback } from 'react';
import styles from '@/styles/artists/ServiceSpecializationManager.module.css'; // To be created

export default function ServiceSpecializationManager({ currentSpecializations = [], onSubmit, isSubmitting }) {
  const [allServices, setAllServices] = useState([]);
  const [selectedServiceIds, setSelectedServiceIds] = useState(new Set());
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [errorServices, setErrorServices] = useState(null);

  // Fetch all available system services
  const fetchAllServices = useCallback(async () => {
    setIsLoadingServices(true);
    setErrorServices(null);
    try {
      const response = await fetch('/api/internal/services');
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to fetch services list');
      }
      const data = await response.json();
      setAllServices(data || []);
    } catch (err) {
      setErrorServices(err.message);
      setAllServices([]);
    } finally {
      setIsLoadingServices(false);
    }
  }, []);

  useEffect(() => {
    fetchAllServices();
  }, [fetchAllServices]);

  // Initialize selectedServiceIds based on currentSpecializations prop
  useEffect(() => {
    setSelectedServiceIds(new Set(currentSpecializations.map(id => String(id)) || []));
  }, [currentSpecializations]);

  const handleCheckboxChange = (serviceId) => {
    const stringServiceId = String(serviceId); // Ensure comparison with strings
    setSelectedServiceIds(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(stringServiceId)) {
        newSelected.delete(stringServiceId);
      } else {
        newSelected.add(stringServiceId);
      }
      return newSelected;
    });
  };

  const handleSubmit = () => {
    onSubmit(Array.from(selectedServiceIds)); // Submit as an array of strings
  };

  // Group services by category for display
  const servicesByCategory = allServices.reduce((acc, service) => {
    const category = service.category || 'Uncategorized';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(service);
    return acc;
  }, {});

  if (isLoadingServices) {
    return <p className={styles.loadingText}>Loading available services...</p>;
  }

  if (errorServices) {
    return <p className={styles.errorText}>Error loading services: {errorServices} <button onClick={fetchAllServices} className={styles.retryButton}>Retry</button></p>;
  }

  if (allServices.length === 0) {
    return <p className={styles.noServicesText}>No services available to select from.</p>;
  }

  return (
    <div className={styles.specializationManager}>
      <h3>Manage Your Service Specializations</h3>
      <p className={styles.instructions}>Select the services you are skilled in and wish to offer.</p>

      {Object.entries(servicesByCategory).map(([category, services]) => (
        <div key={category} className={styles.categorySection}>
          <h4 className={styles.categoryTitle}>{category}</h4>
          <div className={styles.serviceList}>
            {services.map(service => (
              <label key={service.id} className={styles.serviceItem}>
                <input
                  type="checkbox"
                  checked={selectedServiceIds.has(String(service.id))}
                  onChange={() => handleCheckboxChange(service.id)}
                  className={styles.checkbox}
                />
                <span className={styles.serviceName}>{service.name}</span>
                {service.duration && <span className={styles.serviceMeta}>({service.duration} min)</span>}
                {service.price && <span className={styles.serviceMeta}> - ${service.price}</span>}
                {service.status !== 'active' && <span className={`${styles.serviceMeta} ${styles.statusInactive}`}> (Inactive)</span>}
              </label>
            ))}
          </div>
        </div>
      ))}

      <div className={styles.formActions}>
        <button onClick={handleSubmit} className={styles.saveButton} disabled={isSubmitting}>
          {isSubmitting ? 'Saving Specializations...' : 'Save Specializations'}
        </button>
      </div>
    </div>
  );
}
