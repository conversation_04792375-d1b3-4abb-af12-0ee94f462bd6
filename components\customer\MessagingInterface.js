/**
 * Messaging Interface Component - Phase 8: Advanced Customer Experience
 * Ocean Soul Sparkles - In-App Messaging System
 */

import { useState, useEffect, useRef } from 'react'
import { useCustomer } from '@/contexts/CustomerContext'
import { useMobileOptimization } from '@/lib/hooks/useMobileOptimization'
import { useMessagingWebSocket } from '@/lib/hooks/useWebSocket'
import { toast } from 'react-toastify'
import styles from '@/styles/customer/MessagingInterface.module.css'

export default function MessagingInterface({ conversationId, onMessageUpdate }) {
  const { customer } = useCustomer()
  const { isMobile, viewport } = useMobileOptimization()

  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [sending, setSending] = useState(false)
  const [conversation, setConversation] = useState(null)
  const [attachments, setAttachments] = useState([])
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  // Use messaging WebSocket hook
  const {
    messages,
    connectionStatus,
    sendMessage: sendWebSocketMessage,
    readyState
  } = useMessagingWebSocket(conversationId, {
    onMessage: (data) => {
      if (onMessageUpdate) {
        onMessageUpdate()
      }
    }
  })
  
  const messagesEndRef = useRef(null)
  const fileInputRef = useRef(null)
  const textareaRef = useRef(null)

  useEffect(() => {
    if (conversationId) {
      loadConversation()
    }
  }, [conversationId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px'
    }
  }, [newMessage])

  const loadConversation = async () => {
    try {
      const response = await fetch(`/api/customer/messaging/conversations/${conversationId}`)
      const data = await response.json()
      
      if (data.success) {
        setConversation(data.conversation)
      }
    } catch (error) {
      console.error('Error loading conversation:', error)
    }
  }



  const markMessagesAsRead = async () => {
    try {
      await fetch(`/api/customer/messaging/mark-read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ conversation_id: conversationId })
      })
      
      if (onMessageUpdate) {
        onMessageUpdate()
      }
    } catch (error) {
      console.error('Error marking messages as read:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (e) => {
    e.preventDefault()
    
    if (!newMessage.trim() && attachments.length === 0) {
      return
    }

    try {
      setSending(true)
      
      const messageData = {
        conversation_id: conversationId,
        message_text: newMessage.trim(),
        message_type: attachments.length > 0 ? 'mixed' : 'text',
        attachments: attachments
      }

      const response = await fetch('/api/customer/messaging/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(messageData)
      })

      const data = await response.json()
      
      if (data.success) {
        setNewMessage('')
        setAttachments([])

        // Send via WebSocket for real-time updates
        if (readyState === 1) { // WebSocket.OPEN
          sendWebSocketMessage(newMessage.trim(), attachments)
        }

        if (onMessageUpdate) {
          onMessageUpdate()
        }
      } else {
        throw new Error(data.error || 'Failed to send message')
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('Failed to send message')
    } finally {
      setSending(false)
    }
  }

  const handleFileUpload = async (e) => {
    const files = Array.from(e.target.files)
    
    if (files.length === 0) return

    try {
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', 'message_attachment')
        
        const response = await fetch('/api/customer/upload', {
          method: 'POST',
          body: formData
        })
        
        const data = await response.json()
        
        if (data.success) {
          return {
            name: file.name,
            url: data.url,
            type: file.type,
            size: file.size
          }
        } else {
          throw new Error(data.error || 'Upload failed')
        }
      })

      const uploadedFiles = await Promise.all(uploadPromises)
      setAttachments(prev => [...prev, ...uploadedFiles])
      
    } catch (error) {
      console.error('Error uploading files:', error)
      toast.error('Failed to upload files')
    }
  }

  const removeAttachment = (index) => {
    setAttachments(prev => prev.filter((_, i) => i !== index))
  }

  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now - date) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString()
    }
  }

  const renderMessage = (message) => {
    const isOwnMessage = message.sender_type === 'customer' && message.sender_id === customer.id
    
    return (
      <div
        key={message.id}
        className={`${styles.message} ${isOwnMessage ? styles.ownMessage : styles.otherMessage}`}
      >
        <div className={styles.messageContent}>
          {message.message_text && (
            <div className={styles.messageText}>
              {message.message_text}
            </div>
          )}
          
          {message.attachment_url && (
            <div className={styles.messageAttachment}>
              {message.message_type === 'image' ? (
                <img 
                  src={message.attachment_url} 
                  alt="Attachment"
                  className={styles.attachmentImage}
                />
              ) : (
                <a 
                  href={message.attachment_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.attachmentLink}
                >
                  📎 View Attachment
                </a>
              )}
            </div>
          )}
        </div>
        
        <div className={styles.messageInfo}>
          <span className={styles.messageTime}>
            {formatMessageTime(message.created_at)}
          </span>
          {isOwnMessage && (
            <span className={styles.messageStatus}>
              {message.read_at ? '✓✓' : '✓'}
            </span>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.messagingInterface}>
      {/* Conversation Header */}
      {conversation && (
        <div className={styles.conversationHeader}>
          <h3 className={styles.conversationTitle}>
            {conversation.title || 'Conversation'}
          </h3>
          <div className={styles.conversationMeta}>
            {conversation.participants?.map(participant => (
              <span key={participant.id} className={styles.participant}>
                {participant.name}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Messages Area */}
      <div className={styles.messagesArea}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.spinner}></div>
            <p>Loading messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className={styles.emptyMessages}>
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          <div className={styles.messagesList}>
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Message Input */}
      <form onSubmit={handleSendMessage} className={styles.messageForm}>
        {/* Attachments Preview */}
        {attachments.length > 0 && (
          <div className={styles.attachmentsPreview}>
            {attachments.map((attachment, index) => (
              <div key={index} className={styles.attachmentPreview}>
                <span className={styles.attachmentName}>
                  {attachment.name}
                </span>
                <button
                  type="button"
                  onClick={() => removeAttachment(index)}
                  className={styles.removeAttachment}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}

        <div className={styles.inputArea}>
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className={styles.attachButton}
            disabled={sending}
          >
            📎
          </button>
          
          <textarea
            ref={textareaRef}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type your message..."
            className={styles.messageInput}
            disabled={sending}
            rows={1}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault()
                handleSendMessage(e)
              }
            }}
          />
          
          <button
            type="submit"
            disabled={(!newMessage.trim() && attachments.length === 0) || sending}
            className={styles.sendButton}
          >
            {sending ? '⏳' : '➤'}
          </button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx"
          onChange={handleFileUpload}
          style={{ display: 'none' }}
        />
      </form>
    </div>
  )
}
