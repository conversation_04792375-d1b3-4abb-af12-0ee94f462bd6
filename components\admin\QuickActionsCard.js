import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/QuickActionsCard.module.css'

export default function QuickActionsCard({ profile, onProfileUpdate }) {
  const { user } = useAuth()
  const [loading, setLoading] = useState({})

  const handleAction = async (actionType, actionData = {}) => {
    try {
      setLoading(prev => ({ ...prev, [actionType]: true }))

      let endpoint = ''
      let method = 'POST'
      let body = {}

      switch (actionType) {
        case 'updateAvailability':
          endpoint = '/api/artist/availability'
          method = 'PUT'
          body = { is_available_today: actionData.available }
          break
        case 'blockTime':
          endpoint = '/api/artist/block-time'
          body = {
            start_time: actionData.startTime,
            end_time: actionData.endTime,
            reason: actionData.reason || 'Personal time'
          }
          break
        case 'viewBookings':
          window.open('/admin/bookings', '_blank')
          return
        case 'updateProfile':
          window.open('/admin/my-profile', '_blank')
          return
        case 'viewEarnings':
          window.open('/admin/analytics', '_blank')
          return
        case 'manageServices':
          window.open('/admin/inventory?tab=services', '_blank')
          return
        case 'unavailableWeek': // New case for setting unavailable for the week
          endpoint = '/api/artist/availability/set-unavailable-week';
          method = 'PUT';
          body = {}; // API infers artist and week from authenticated user
          break;
        // Add other emergency actions like 'cancelAllToday' if they need API calls
        // case 'cancelAllToday':
        //   endpoint = '/api/artist/cancel-all-today'; // Example
        //   method = 'POST'; // Or PUT/DELETE as appropriate
        //   body = {};
        //   break;
        default:
          // Check if it's an action that doesn't require an endpoint (like opening a new tab)
          if (!['viewBookings', 'updateProfile', 'viewEarnings', 'manageServices'].includes(actionType)) {
            console.warn(`Unknown action type or action that should not reach generic fetch: ${actionType}`);
            // throw new Error('Unknown action type'); // Or handle more gracefully
          }
          // For actions that open new tabs, endpoint will be empty, so this is fine.
          break;
      }

      if (endpoint) { // Only proceed if an endpoint is defined (i.e., it's an API call)
        const response = await fetch(endpoint, {
          method,
          headers: {
            // Assuming user.access_token is correctly populated by useAuth()
            // If not, you might need to get the token via getAuthToken() from 'lib/auth-token-manager'
            'Authorization': `Bearer ${user.access_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })

        const responseData = await response.json(); // Try to get JSON response regardless of status for more info

        if (response.ok) {
          toast.success(responseData.message || 'Action completed successfully');
          onProfileUpdate?.(); // Call to refresh dashboard data if provided
        } else {
          console.error('API Error Data:', responseData);
          toast.error(responseData.error || responseData.message || `Action failed: ${response.statusText}`);
          // No need to throw new Error here as we're handling it with a toast
        }
      }
    } catch (error) {
      // This catch block handles network errors or errors thrown before/during fetch
      console.error(`Error performing ${actionType}:`, error);
      toast.error(error.message || `Failed to ${actionType.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
    } finally {
      setLoading(prev => ({ ...prev, [actionType]: false }))
    }
  }

  const quickActions = [
    {
      id: 'updateAvailability',
      title: 'Toggle Availability',
      description: profile?.is_available_today ? 'Mark as unavailable' : 'Mark as available',
      icon: profile?.is_available_today ? '🔴' : '🟢',
      color: profile?.is_available_today ? '#ef4444' : '#10b981',
      action: () => handleAction('updateAvailability', { available: !profile?.is_available_today })
    },
    {
      id: 'viewBookings',
      title: 'My Bookings',
      description: 'View and manage your bookings',
      icon: '📅',
      color: '#3b82f6',
      action: () => handleAction('viewBookings')
    },
    {
      id: 'blockTime',
      title: 'Block Time',
      description: 'Block time for personal use',
      icon: '⏰',
      color: '#f59e0b',
      action: () => {
        const startTime = prompt('Start time (HH:MM):')
        const endTime = prompt('End time (HH:MM):')
        const reason = prompt('Reason (optional):')

        if (startTime && endTime) {
          const today = new Date().toISOString().split('T')[0]
          handleAction('blockTime', {
            startTime: `${today}T${startTime}:00`,
            endTime: `${today}T${endTime}:00`,
            reason
          })
        }
      }
    },
    {
      id: 'updateProfile',
      title: 'Edit Profile',
      description: 'Update your profile information',
      icon: '👤',
      color: '#8b5cf6',
      action: () => handleAction('updateProfile')
    },
    {
      id: 'viewEarnings',
      title: 'View Earnings',
      description: 'Check your earnings and payments',
      icon: '💰',
      color: '#10b981',
      action: () => handleAction('viewEarnings')
    },
    {
      id: 'manageServices',
      title: 'My Services',
      description: 'Manage your service offerings',
      icon: '🎨',
      color: '#ec4899',
      action: () => handleAction('manageServices')
    }
  ]

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3>Quick Actions</h3>
        <span className={styles.subtitle}>Common tasks and shortcuts</span>
      </div>

      <div className={styles.content}>
        <div className={styles.actionsGrid}>
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              disabled={loading[action.id]}
              className={styles.actionButton}
              style={{ borderLeftColor: action.color }}
            >
              <div className={styles.actionIcon}>
                {loading[action.id] ? (
                  <div className={styles.spinner}></div>
                ) : (
                  <span style={{ fontSize: '1.5rem' }}>{action.icon}</span>
                )}
              </div>
              <div className={styles.actionContent}>
                <div className={styles.actionTitle}>{action.title}</div>
                <div className={styles.actionDescription}>{action.description}</div>
              </div>
              <div className={styles.actionArrow}>→</div>
            </button>
          ))}
        </div>

        {/* Status Summary */}
        <div className={styles.statusSummary}>
          <h4>Current Status</h4>
          <div className={styles.statusGrid}>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>Availability</span>
              <span
                className={styles.statusValue}
                style={{ color: profile?.is_available_today ? '#10b981' : '#ef4444' }}
              >
                {profile?.is_available_today ? 'Available' : 'Unavailable'}
              </span>
            </div>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>Today's Bookings</span>
              <span className={styles.statusValue}>
                {profile?.todays_bookings || 0}
              </span>
            </div>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>This Week</span>
              <span className={styles.statusValue}>
                {profile?.weekly_bookings || 0} bookings
              </span>
            </div>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>Rating</span>
              <span className={styles.statusValue}>
                {profile?.average_rating ? `${profile.average_rating.toFixed(1)} ⭐` : 'No ratings yet'}
              </span>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className={styles.recentActivity}>
          <h4>Recent Activity</h4>
          <div className={styles.activityList}>
            <div className={styles.activityItem}>
              <div className={styles.activityIcon}>📅</div>
              <div className={styles.activityContent}>
                <span className={styles.activityText}>New booking confirmed</span>
                <span className={styles.activityTime}>2 hours ago</span>
              </div>
            </div>
            <div className={styles.activityItem}>
              <div className={styles.activityIcon}>⭐</div>
              <div className={styles.activityContent}>
                <span className={styles.activityText}>Received 5-star review</span>
                <span className={styles.activityTime}>1 day ago</span>
              </div>
            </div>
            <div className={styles.activityItem}>
              <div className={styles.activityIcon}>💰</div>
              <div className={styles.activityContent}>
                <span className={styles.activityText}>Payment received</span>
                <span className={styles.activityTime}>2 days ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Actions */}
        <div className={styles.emergencySection}>
          <h4>Emergency Actions</h4>
          <div className={styles.emergencyActions}>
            <button
              className={styles.emergencyButton}
              onClick={() => {
                if (confirm('Are you sure you want to cancel all today\'s bookings? This action cannot be undone.')) {
                  handleAction('cancelAllToday')
                }
              }}
            >
              🚨 Cancel All Today's Bookings
            </button>
            <button
              className={styles.emergencyButton}
              onClick={() => {
                if (confirm('This will mark you as unavailable for the rest of the week. Continue?')) {
                  handleAction('unavailableWeek')
                }
              }}
            >
              ⛔ Unavailable This Week
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
