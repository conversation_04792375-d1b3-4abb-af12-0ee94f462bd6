/* Calendar Integration Component Styles */
/* Ocean Soul Sparkles - Phase 7: Advanced Integrations & Ecosystem */

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
}

.container.mobile {
  padding: 15px;
  max-width: 100%;
}

/* Header */
.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 600;
}

.header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Navigation Tabs */
.tabs {
  display: flex;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab {
  background: none;
  border: none;
  padding: 15px 25px;
  font-size: 1rem;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab:hover {
  color: #4ECDC4;
  background-color: rgba(78, 205, 196, 0.1);
}

.tab.active {
  color: #4ECDC4;
  border-bottom: 3px solid #4ECDC4;
}

.badge {
  background-color: #e74c3c;
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

/* Tab Content */
.tabContent {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Sync Status */
.syncStatus {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 30px;
}

.syncHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.syncHeader h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.syncButton {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.syncButton:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.syncButton.syncing {
  opacity: 0.7;
  cursor: not-allowed;
}

.lastSync {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

/* Calendars Section */
.calendars {
  margin-bottom: 30px;
}

.calendars h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.noCalendars {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.noCalendars p {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.connectButton {
  background: linear-gradient(135deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.connectButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

/* Calendar List */
.calendarList {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.calendarCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendarCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.calendarInfo h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.status {
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  display: inline-block;
  margin-bottom: 10px;
}

.status.connected {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.lastSyncTime {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0;
}

.calendarActions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.settingsButton,
.disconnectButton {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.settingsButton {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.settingsButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.disconnectButton {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #fed7d7;
}

.disconnectButton:hover {
  background: #fed7d7;
  border-color: #feb2b2;
}

/* Statistics */
.stats {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
}

.stats h3 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.statCard {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.statNumber {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #4ECDC4;
  margin-bottom: 5px;
}

.statLabel {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Settings */
.settings {
  max-width: 800px;
}

.settingsCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
}

.settingsCard h4 {
  color: #2c3e50;
  margin: 0 0 20px 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.settingGroup {
  margin-bottom: 20px;
}

.settingGroup label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.settingGroup input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4ECDC4;
}

.settingGroup select {
  margin-left: 10px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.95rem;
}

/* Conflicts */
.conflicts {
  max-width: 900px;
}

.noConflicts {
  text-align: center;
  padding: 40px 20px;
  background: #d4edda;
  border-radius: 12px;
  color: #155724;
}

.conflictList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.conflictCard {
  background: white;
  border: 1px solid #fed7d7;
  border-left: 4px solid #e53e3e;
  border-radius: 12px;
  padding: 20px;
}

.conflictInfo h4 {
  color: #e53e3e;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.conflictInfo p {
  margin: 5px 0;
  color: #495057;
}

.conflictActions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.resolveButton {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.resolveButton:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .header h2 {
    font-size: 1.6rem;
  }

  .header p {
    font-size: 1rem;
  }

  .tabs {
    margin-bottom: 20px;
  }

  .tab {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .syncHeader {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .calendarList {
    grid-template-columns: 1fr;
  }

  .calendarActions {
    flex-direction: column;
  }

  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
  }

  .statCard {
    padding: 15px;
  }

  .statNumber {
    font-size: 1.6rem;
  }

  .conflictActions {
    flex-direction: column;
  }

  .settingGroup label {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .settingGroup select {
    margin-left: 0;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }

  .syncStatus {
    padding: 20px;
  }

  .calendarCard,
  .settingsCard,
  .conflictCard {
    padding: 15px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }
}
